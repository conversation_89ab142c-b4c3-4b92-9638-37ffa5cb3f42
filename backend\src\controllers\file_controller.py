"""
File controller for the Bank Reconciliation API.
"""

import os
import time
from flask import request, jsonify, send_file
from werkzeug.utils import secure_filename
from utils.file_parser import parse_file

# Create uploads directory if it doesn't exist
uploads_dir = os.path.join(os.getcwd(), 'uploads')
try:
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir, recursive=True)
        print(f"Created uploads directory: {uploads_dir}")
except Exception as e:
    print(f"Error creating uploads directory: {str(e)}")

def upload_file():
    """
    Upload a file.
    """
    try:
        # Check if file is provided
        if 'file' not in request.files:
            return jsonify({'message': 'No file part'}), 400
        
        file = request.files['file']
        
        # Check if file is empty
        if file.filename == '':
            return jsonify({'message': 'No selected file'}), 400
        
        # Secure filename
        filename = secure_filename(file.filename)
        
        # Add timestamp to filename to avoid conflicts
        timestamp = int(time.time())
        filename = f"{timestamp}_{filename}"
        
        # Save file
        file_path = os.path.join(uploads_dir, filename)
        file.save(file_path)
        
        return jsonify({
            'status': True,
            'message': 'File uploaded successfully',
            'data': {
                'fileName': filename,
                'filePath': file_path,
                'fileSize': os.path.getsize(file_path),
                'fileType': os.path.splitext(filename)[1].lower()
            }
        }), 201
    
    except Exception as e:
        print(f"Error uploading file: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

async def preview_file(file_path):
    """
    Preview file content.
    """
    try:
        # Decode file path
        file_path = file_path.replace('%2F', '/')
        
        # Check if file exists
        if not os.path.exists(file_path):
            return jsonify({'message': 'File not found'}), 404
        
        # Parse file
        file_data = await parse_file(file_path)
        
        return jsonify({
            'status': True,
            'data': {
                'fileInfo': {
                    'fileName': os.path.basename(file_path),
                    'fileSize': file_data['metadata']['fileSize'],
                    'fileType': file_data['metadata']['fileType'],
                    'lastModified': file_data['metadata']['lastModified'].isoformat() if hasattr(file_data['metadata']['lastModified'], 'isoformat') else str(file_data['metadata']['lastModified'])
                },
                'preview': file_data['preview'],
                'content': file_data['content'],
                'metadata': file_data['metadata'],
                'summary': file_data['summary']
            }
        }), 200
    
    except Exception as e:
        print(f"Error previewing file: {str(e)}")
        return jsonify({'message': str(e)}), 500

def download_file(file_path):
    """
    Download file.
    """
    try:
        # Decode file path
        file_path = file_path.replace('%2F', '/')
        
        # Check if file exists
        if not os.path.exists(file_path):
            return jsonify({'message': 'File not found'}), 404
        
        # Get file name
        file_name = os.path.basename(file_path)
        
        # Send file
        return send_file(
            file_path,
            as_attachment=True,
            download_name=file_name,
            mimetype='application/octet-stream'
        )
    
    except Exception as e:
        print(f"Error downloading file: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

async def analyze_file():
    """
    Analyze file content and extract insights.
    """
    try:
        # Check if file is provided
        if 'file' not in request.files:
            return jsonify({'message': 'No file part'}), 400
        
        file = request.files['file']
        
        # Check if file is empty
        if file.filename == '':
            return jsonify({'message': 'No selected file'}), 400
        
        # Secure filename
        filename = secure_filename(file.filename)
        
        # Add timestamp to filename to avoid conflicts
        timestamp = int(time.time())
        filename = f"{timestamp}_{filename}"
        
        # Save file
        file_path = os.path.join(uploads_dir, filename)
        file.save(file_path)
        
        # Parse file
        file_data = await parse_file(file_path)
        
        # Extract insights
        insights = extract_insights(file_data)
        
        return jsonify({
            'status': True,
            'message': 'File analyzed successfully',
            'data': {
                'fileInfo': {
                    'fileName': filename,
                    'fileSize': os.path.getsize(file_path),
                    'fileType': os.path.splitext(filename)[1].lower(),
                },
                'insights': insights,
                'summary': file_data['summary']
            }
        }), 200
    
    except Exception as e:
        print(f"Error analyzing file: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

def extract_insights(file_data):
    """
    Extract insights from file data.
    """
    insights = {}
    
    # Extract insights based on file type
    file_type = file_data['metadata']['fileType']
    
    if file_type in ['xlsx', 'xls']:
        # Excel insights
        insights = extract_excel_insights(file_data)
    elif file_type == 'csv':
        # CSV insights
        insights = extract_csv_insights(file_data)
    elif file_type == 'pdf':
        # PDF insights
        insights = extract_pdf_insights(file_data)
    
    return insights

def extract_excel_insights(file_data):
    """
    Extract insights from Excel data.
    """
    insights = {
        'sheetCount': len(file_data['content'].get('sheets', [])),
        'dataTypes': file_data['summary'].get('dataTypes', {}),
        'statistics': file_data['summary'].get('statistics', {})
    }
    
    return insights

def extract_csv_insights(file_data):
    """
    Extract insights from CSV data.
    """
    insights = {
        'rowCount': file_data['summary'].get('rowCount', 0),
        'columnCount': file_data['summary'].get('columnCount', 0)
    }
    
    return insights

def extract_pdf_insights(file_data):
    """
    Extract insights from PDF data.
    """
    insights = {
        'pageCount': file_data['summary'].get('pageCount', 0),
        'tableCount': file_data['summary'].get('tableCount', 0),
        'rowCount': file_data['summary'].get('rowCount', 0)
    }
    
    return insights
