import { API_BASE_URL } from '../apiRoutes';

/**
 * Test the connection to the backend
 * @returns {Promise<Object>} The result of the connection test
 */
export const testConnection = async () => {
  try {
    console.log('Testing connection to:', `${API_BASE_URL}/health`);
    
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Backend connection successful:', data);
      return {
        success: true,
        message: 'Connected to backend successfully',
        data
      };
    } else {
      console.error('Backend connection failed with status:', response.status);
      return {
        success: false,
        message: `Failed to connect to backend: ${response.status} ${response.statusText}`,
      };
    }
  } catch (error) {
    console.error('Backend connection error:', error);
    return {
      success: false,
      message: `Error connecting to backend: ${error.message}`,
    };
  }
};

// Run the test when this file is imported
testConnection().then(result => {
  console.log('Connection test result:', result);
});

export default testConnection;
