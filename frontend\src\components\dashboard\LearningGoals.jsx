import React from 'react';

const LearningGoals = ({ goals = [], onAddGoal }) => {
  return (
    <div className="bg-white/30 backdrop-blur-xl p-6 rounded-xl shadow-lg border border-white/20">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-[#412D6C] font-semibold">Learning Goals</h3>
        <button
          onClick={onAddGoal}
          className="text-xs text-[#412D6C] font-medium hover:underline"
        >
          + Add Goal
        </button>
      </div>

      <div className="space-y-4">
        {Array.isArray(goals) && goals.map((goal, index) => {
          // Calculate progress percentage with safety checks
          const current = goal?.current || 0;
          const target = goal?.target || 1; // Avoid division by zero
          const progressPercentage = (current / target) * 100;
          const isCompleted = progressPercentage >= 100;

          return (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  {isCompleted ? (
                    <span className="text-green-500 mr-2">✓</span>
                  ) : (
                    <span className="text-[#412D6C] mr-2">◯</span>
                  )}
                  <h4 className={`text-sm font-medium ${isCompleted ? 'text-green-700' : 'text-gray-700'}`}>
                    {goal?.title || 'Untitled Goal'}
                  </h4>
                </div>
                <span className="text-xs font-medium text-gray-500">
                  {current}/{target} {goal?.unit || 'units'}
                </span>
              </div>

              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full ${isCompleted ? 'bg-green-500' : 'bg-[#412D6C]'}`}
                  style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                ></div>
              </div>

              <div className="text-xs text-gray-500">
                {goal?.description || 'No description'}
                {goal?.deadline && (
                  <span className="ml-2 text-xs font-medium">
                    Due: {new Date(goal.deadline).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {(!Array.isArray(goals) || goals.length === 0) && (
        <div className="text-center py-8 text-gray-500">
          <p>No learning goals set yet.</p>
          <button
            onClick={onAddGoal}
            className="mt-2 text-[#412D6C] hover:underline"
          >
            Set your first goal
          </button>
        </div>
      )}
    </div>
  );
};

export default LearningGoals;
