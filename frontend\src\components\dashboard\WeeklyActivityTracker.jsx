import React from 'react';

const WeeklyActivityTracker = ({ weeklyData = [] }) => {
  // Get current day of the week (0 = Sunday, 1 = Monday, etc.)
  const currentDayIndex = new Date().getDay();

  // Days of the week
  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Calculate max hours for scaling with safety check
  const maxHours = Array.isArray(weeklyData) && weeklyData.length > 0
    ? Math.max(...weeklyData.map(day => day?.hours || 0), 4) // Minimum of 4 for scaling
    : 4;

  return (
    <div className="bg-white/30 backdrop-blur-xl p-6 rounded-xl shadow-lg border border-white/20">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-[#412D6C] font-semibold">Weekly Activity</h3>
        <div className="text-sm text-gray-500">
          Total: <span className="font-medium">{Array.isArray(weeklyData) ? weeklyData.reduce((sum, day) => sum + (day?.hours || 0), 0) : 0} hours</span>
        </div>
      </div>

      <div className="flex items-end justify-between h-48 mt-4">
        {Array.isArray(weeklyData) && weeklyData.map((day, index) => {
          const isToday = index === currentDayIndex;
          const heightPercentage = ((day?.hours || 0) / maxHours) * 100;

          return (
            <div key={index} className="flex flex-col items-center w-[14.28%]">
              <div className="relative w-full flex justify-center mb-2">
                <div
                  className={`w-12 rounded-t-lg ${isToday ? 'bg-[#6A4CA6]' : 'bg-[#412D6C]/60'}`}
                  style={{ height: `${heightPercentage}%` }}
                ></div>

                {day.hours > 0 && (
                  <div className="absolute -top-6 text-xs font-medium text-gray-700">
                    {day.hours}h
                  </div>
                )}
              </div>

              <div className={`text-xs font-medium ${isToday ? 'text-[#412D6C]' : 'text-gray-500'}`}>
                {daysOfWeek[index]}
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex justify-between text-sm text-gray-600">
          <div>
            <span className="inline-block w-3 h-3 rounded-full bg-[#6A4CA6] mr-1"></span>
            Today
          </div>
          <div>
            <span className="inline-block w-3 h-3 rounded-full bg-[#412D6C]/60 mr-1"></span>
            Other Days
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeeklyActivityTracker;
