"""
Development server script with hot reload for the Bank Reconciliation API.
"""

import os
import sys
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class AppReloader(FileSystemEventHandler):
    def __init__(self):
        self.last_reload = time.time()
    
    def on_modified(self, event):
        # Only reload if it's a Python file and not in __pycache__
        if event.src_path.endswith('.py') and '__pycache__' not in event.src_path:
            # Avoid multiple reloads in a short time
            current_time = time.time()
            if current_time - self.last_reload > 1:
                self.last_reload = current_time
                print(f"\nDetected change in {event.src_path}")
                print("Restarting server...\n")
                os.execv(sys.executable, [sys.executable] + sys.argv)

if __name__ == '__main__':
    # Start file watcher
    event_handler = AppReloader()
    observer = Observer()
    observer.schedule(event_handler, path='src', recursive=True)
    observer.start()
    
    try:
        # Import app
        from src.index import app
        
        print("\n=== Development Server with Hot Reload ===")
        print("Press Ctrl+C to stop the server\n")
        
        # Run app
        port = int(os.environ.get('PORT', 8080))
        app.run(host='0.0.0.0', port=port, debug=True, use_reloader=False)
    
    except KeyboardInterrupt:
        observer.stop()
    
    observer.join()
