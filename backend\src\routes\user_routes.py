"""
User routes for the Bank Reconciliation API.
"""

from flask import Blueprint, request, jsonify
from models.user_model import User
from middleware.auth_middleware import auth_required

# Create blueprint
user_bp = Blueprint('user', __name__)

# @route   GET /api/users/me
# @desc    Get current user
# @access  Private
@user_bp.route('/me', methods=['GET'])
@auth_required
def get_current_user():
    try:
        # Get user ID from request
        user_id = request.user['id']
        
        # Find user by ID
        user = User.find_by_id(user_id)
        if not user:
            return jsonify({'message': 'User not found'}), 404
        
        return jsonify({
            'status': True,
            'data': user.to_dict()
        }), 200
    
    except Exception as e:
        print(f"Error getting user: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

# @route   PUT /api/users/me
# @desc    Update user profile
# @access  Private
@user_bp.route('/me', methods=['PUT'])
@auth_required
def update_user_profile():
    try:
        # Get user ID from request
        user_id = request.user['id']
        
        # Find user by ID
        user = User.find_by_id(user_id)
        if not user:
            return jsonify({'message': 'User not found'}), 404
        
        # Get data from request
        data = request.get_json()
        
        # Update user fields
        if 'firstName' in data:
            user.first_name = data['firstName']
        if 'lastName' in data:
            user.last_name = data['lastName']
        if 'email' in data:
            # Check if email is already taken
            existing_user = User.find_by_email(data['email'])
            if existing_user and str(existing_user._id) != user_id:
                return jsonify({'message': 'Email already in use'}), 400
            user.email = data['email']
        
        # Save user
        user.save()
        
        return jsonify({
            'status': True,
            'message': 'User profile updated successfully',
            'data': user.to_dict()
        }), 200
    
    except Exception as e:
        print(f"Error updating user: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

# @route   DELETE /api/users/me
# @desc    Delete user
# @access  Private
@user_bp.route('/me', methods=['DELETE'])
@auth_required
def delete_user():
    try:
        # Get user ID from request
        user_id = request.user['id']
        
        # Find user by ID
        user = User.find_by_id(user_id)
        if not user:
            return jsonify({'message': 'User not found'}), 404
        
        # Delete user
        # In a real application, you might want to implement soft delete
        # or handle related data deletion
        
        return jsonify({
            'status': True,
            'message': 'User deleted successfully'
        }), 200
    
    except Exception as e:
        print(f"Error deleting user: {str(e)}")
        return jsonify({'message': 'Server error'}), 500
