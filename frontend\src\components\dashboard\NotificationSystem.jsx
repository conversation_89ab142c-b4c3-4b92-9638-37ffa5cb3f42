import React, { useState } from 'react';

const NotificationSystem = ({ notifications }) => {
  const [showNotifications, setShowNotifications] = useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setShowNotifications(!showNotifications)}
        className="p-2 rounded-full bg-white/30 backdrop-blur-xl hover:bg-white/40 transition-all"
      >
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
          {notifications.length}
        </span>
        <svg className="w-6 h-6 text-[#412D6C]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
      </button>

      {showNotifications && (
        <div className="absolute right-0 mt-2 w-80 bg-white/80 backdrop-blur-xl rounded-lg shadow-lg border border-white/20 z-50">
          <div className="p-4">
            <h3 className="text-lg font-semibold text-[#412D6C] mb-3">Notifications</h3>
            <div className="space-y-3">
              {notifications.map((notification, index) => (
                <div
                  key={index}
                  className="p-3 rounded-lg bg-white/50 hover:bg-white/70 transition-all cursor-pointer"
                >
                  <p className="text-sm text-gray-800">{notification.message}</p>
                  <span className="text-xs text-gray-500 mt-1">{notification.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationSystem;
