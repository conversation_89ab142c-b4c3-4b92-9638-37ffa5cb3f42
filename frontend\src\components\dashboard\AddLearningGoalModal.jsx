import React, { useState } from 'react';

const AddLearningGoalModal = ({ onClose, onAddGoal }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    current: 0,
    target: 1,
    unit: 'modules',
    deadline: '',
    reminder: false,
    reminderEmail: '',
    reminderFrequency: 'weekly'
  });
  
  const [errors, setErrors] = useState({});
  
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };
  
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (formData.target <= 0) newErrors.target = 'Target must be greater than 0';
    if (formData.current < 0) newErrors.current = 'Current progress cannot be negative';
    if (formData.current > formData.target) newErrors.current = 'Current progress cannot exceed target';
    if (!formData.deadline) newErrors.deadline = 'Deadline is required';
    
    if (formData.reminder && !formData.reminderEmail) {
      newErrors.reminderEmail = 'Email is required for reminders';
    } else if (formData.reminder && !/\S+@\S+\.\S+/.test(formData.reminderEmail)) {
      newErrors.reminderEmail = 'Email is invalid';
    }
    
    return newErrors;
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    const formErrors = validateForm();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    // Convert string numbers to actual numbers
    const goalData = {
      ...formData,
      current: Number(formData.current),
      target: Number(formData.target)
    };
    
    onAddGoal(goalData);
    onClose();
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white p-6 border-b border-gray-200 flex justify-between items-center rounded-t-xl">
          <h2 className="text-xl font-bold text-gray-800">Add Learning Goal</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Goal Title *</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="e.g., Complete JavaScript Course"
              />
              {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="2"
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Brief description of your goal"
              ></textarea>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Current Progress *</label>
                <input
                  type="number"
                  name="current"
                  value={formData.current}
                  onChange={handleChange}
                  min="0"
                  className={`w-full p-2 border rounded-md ${errors.current ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.current && <p className="mt-1 text-sm text-red-600">{errors.current}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Target *</label>
                <input
                  type="number"
                  name="target"
                  value={formData.target}
                  onChange={handleChange}
                  min="1"
                  className={`w-full p-2 border rounded-md ${errors.target ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.target && <p className="mt-1 text-sm text-red-600">{errors.target}</p>}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
              <select
                name="unit"
                value={formData.unit}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="modules">Modules</option>
                <option value="hours">Hours</option>
                <option value="lessons">Lessons</option>
                <option value="projects">Projects</option>
                <option value="courses">Courses</option>
                <option value="books">Books</option>
                <option value="certification">Certification</option>
                <option value="tasks">Tasks</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Deadline *</label>
              <input
                type="date"
                name="deadline"
                value={formData.deadline}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.deadline ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.deadline && <p className="mt-1 text-sm text-red-600">{errors.deadline}</p>}
            </div>
            
            <div className="border-t border-gray-200 pt-4">
              <div className="flex items-start mb-4">
                <input
                  type="checkbox"
                  id="reminder"
                  name="reminder"
                  checked={formData.reminder}
                  onChange={handleChange}
                  className="mt-1 mr-2"
                />
                <label htmlFor="reminder" className="text-sm text-gray-700">
                  Set email reminders for this goal
                </label>
              </div>
              
              {formData.reminder && (
                <div className="space-y-4 pl-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                    <input
                      type="email"
                      name="reminderEmail"
                      value={formData.reminderEmail}
                      onChange={handleChange}
                      className={`w-full p-2 border rounded-md ${errors.reminderEmail ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="<EMAIL>"
                    />
                    {errors.reminderEmail && <p className="mt-1 text-sm text-red-600">{errors.reminderEmail}</p>}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Reminder Frequency</label>
                    <select
                      name="reminderFrequency"
                      value={formData.reminderFrequency}
                      onChange={handleChange}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="biweekly">Bi-weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#412D6C] text-white rounded-md hover:bg-[#362659]"
              >
                Add Goal
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddLearningGoalModal;
