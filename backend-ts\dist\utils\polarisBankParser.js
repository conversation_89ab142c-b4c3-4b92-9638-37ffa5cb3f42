"use strict";
/**
 * Polaris Bank Statement Parser
 *
 * This module provides specialized parsing for Polaris Bank statements,
 * which have a unique format that requires custom handling.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractPolarisBankTable = extractPolarisBankTable;
exports.extractPolarisBankInfo = extractPolarisBankInfo;
/**
 * Extract Polaris Bank statement data with improved formatting
 */
function extractPolarisBankTable(text) {
    const lines = text.split('\n').filter(line => line.trim() !== '');
    // Initialize result
    const result = {
        headers: ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'],
        rows: []
    };
    // First, look for the balance B/F entry
    let balanceEntry = null;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.includes('Balance B/F')) {
            const balanceMatch = line.match(/[\d,]+\.\d{2}/);
            if (balanceMatch) {
                balanceEntry = {
                    EntryDate: '',
                    Details: 'Balance B/F........',
                    ValueDate: '',
                    Debit: '',
                    Credit: '',
                    Balance: balanceMatch[0]
                };
                result.rows.push(balanceEntry);
                break;
            }
        }
    }
    // Extract transactions using multiple approaches
    extractTransactionsFromStructuredFormat(text, result);
    // If we didn't find enough transactions, try the flexible pattern
    if (result.rows.length <= (balanceEntry ? 1 : 0)) {
        extractTransactionsFromFlexibleFormat(text, result);
    }
    // If we still don't have enough entries, try the date-based pattern
    if (result.rows.length <= (balanceEntry ? 1 : 0)) {
        extractTransactionsFromDatePattern(text, result);
    }
    // Clean and format the data
    cleanAndFormatTransactions(result);
    // Log the first 100 characters of the text and the extracted data
    console.log("POLARIS BANK PARSER - Input text (first 100 chars):", text.substring(0, 100));
    console.log("POLARIS BANK PARSER - Extracted data (first 10 rows):", JSON.stringify(result.rows.slice(0, 10), null, 2));
    console.log("POLARIS BANK PARSER - Total rows extracted:", result.rows.length);
    return result.rows.length > 0 ? result : null;
}
/**
 * Extract transactions using the structured table format
 */
function extractTransactionsFromStructuredFormat(text, result) {
    const lines = text.split('\n').filter(line => line.trim() !== '');
    // Look for table headers
    let tableStartIndex = -1;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.includes('EntryDate') && line.includes('Details') &&
            line.includes('ValueDate') && line.includes('Debit')) {
            tableStartIndex = i;
            break;
        }
    }
    // If we found the header row, try to extract the transaction data
    if (tableStartIndex > -1) {
        // Process the table rows
        let currentEntry = null;
        let entryDate = '';
        let details = '';
        let valueDate = '';
        let debit = '';
        let credit = '';
        let balance = '';
        // Process each line after the header
        for (let i = tableStartIndex + 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line)
                continue;
            // Check if this is a new entry (starts with a date)
            const dateMatch = line.match(/^(\d{2}-[A-Z]{3}-\d{2})/);
            if (dateMatch) {
                // Save the previous entry if it exists
                if (currentEntry && entryDate) {
                    if (!isDuplicate(result.rows, currentEntry)) {
                        result.rows.push(currentEntry);
                    }
                }
                // Start a new entry
                entryDate = dateMatch[1];
                // Extract other fields from this line
                const parts = line.substring(dateMatch[0].length).trim().split(/\s{2,}/);
                if (parts.length > 0) {
                    details = parts[0].trim();
                    // Check if there are more parts on this line
                    if (parts.length > 1) {
                        valueDate = parts[1].trim();
                        if (parts.length > 2)
                            debit = parts[2].trim();
                        if (parts.length > 3)
                            credit = parts[3].trim();
                        if (parts.length > 4)
                            balance = parts[4].trim();
                    }
                }
                // Create the new entry
                currentEntry = {
                    EntryDate: entryDate,
                    Details: details,
                    ValueDate: valueDate,
                    Debit: debit,
                    Credit: credit,
                    Balance: balance
                };
            }
            else if (currentEntry) {
                // This is a continuation line
                // Check if this line contains the ValueDate, Debit, Credit, Balance
                if (line.match(/^\d{2}-[A-Z]{3}-\d{2}/)) {
                    // This looks like a ValueDate
                    const parts = line.split(/\s{2,}/);
                    valueDate = parts[0].trim();
                    if (parts.length > 1)
                        debit = parts[1].trim();
                    if (parts.length > 2)
                        credit = parts[2].trim();
                    if (parts.length > 3)
                        balance = parts[3].trim();
                    currentEntry.ValueDate = valueDate;
                    currentEntry.Debit = debit;
                    currentEntry.Credit = credit;
                    currentEntry.Balance = balance;
                }
                else if (line.match(/^[\d,]+\.\d{2}/)) {
                    // This looks like a Debit amount
                    const parts = line.split(/\s{2,}/);
                    debit = parts[0].trim();
                    if (parts.length > 1)
                        credit = parts[1].trim();
                    if (parts.length > 2)
                        balance = parts[2].trim();
                    currentEntry.Debit = debit;
                    currentEntry.Credit = credit;
                    currentEntry.Balance = balance;
                }
                else {
                    // This is probably a continuation of the Details field
                    // Only add it if it doesn't look like a date, amount, or other structured data
                    if (!line.match(/^\d{2}\.\d{2}\.\d{4}/) &&
                        !line.match(/^\d{2}-[A-Z]{3}-\d{2}/) &&
                        !line.match(/^[\d,]+\.\d{2}$/) &&
                        !line.match(/^\d+$/) && // Skip lines that are just numbers
                        !line.match(/Balance/i)) { // Skip balance lines
                        currentEntry.Details += ' ' + line;
                    }
                }
            }
        }
        // Don't forget to add the last entry
        if (currentEntry && entryDate && !isDuplicate(result.rows, currentEntry)) {
            result.rows.push(currentEntry);
        }
    }
    // Also try the regex pattern approach as a fallback
    if (result.rows.length <= 1) {
        // This pattern looks for rows with the format:
        // 03-MAR-25  01.03.2025 RANDALPHA MFB/...  03-MAR-25  960.00  0  65,897,998.14
        const tablePattern = /(\d{2}-[A-Z]{3}-\d{2})\s+(.*?)\s+(\d{2}-[A-Z]{3}-\d{2})\s+([\d,]+\.\d{2})\s+(\d+)\s+([\d,]+\.\d{2})/g;
        // Get the text
        const tableText = text;
        let tableMatch;
        while ((tableMatch = tablePattern.exec(tableText)) !== null) {
            const entry = {
                EntryDate: tableMatch[1],
                Details: tableMatch[2].trim(),
                ValueDate: tableMatch[3],
                Debit: tableMatch[4],
                Credit: tableMatch[5],
                Balance: tableMatch[6]
            };
            // Check if this is a duplicate before adding
            if (!isDuplicate(result.rows, entry)) {
                result.rows.push(entry);
            }
        }
    }
}
/**
 * Extract transactions using a more flexible pattern
 */
function extractTransactionsFromFlexibleFormat(text, result) {
    // First, try to find all entry dates
    const entryDatePattern = /(\d{2}-[A-Z]{3}-\d{2})/g;
    const entryDates = [];
    let match;
    while ((match = entryDatePattern.exec(text)) !== null) {
        entryDates.push({ date: match[1], index: match.index });
    }
    // Process each entry date
    for (let i = 0; i < entryDates.length; i++) {
        const currentDate = entryDates[i];
        const nextDate = entryDates[i + 1];
        // Get the text between this date and the next date (or end of text)
        const endIndex = nextDate ? nextDate.index : text.length;
        const entryText = text.substring(currentDate.index, endIndex);
        // Skip if this doesn't look like a transaction entry
        if (!entryText.includes('RANDALPHA MFB'))
            continue;
        // Extract details - look for the main transaction description
        const detailsMatch = entryText.match(/\d{2}\.\d{2}\.\d{4}\s+(RANDALPHA\s+MFB\/[^\n]+)/);
        if (!detailsMatch)
            continue;
        // Get the full details, including any continuation lines
        let details = detailsMatch[1].trim();
        // Look for continuation lines in the entry text
        const lines = entryText.split('\n');
        let foundMainLine = false;
        for (const line of lines) {
            if (!foundMainLine) {
                // Look for the main line with the RANDALPHA MFB pattern
                if (line.includes(details.substring(0, Math.min(20, details.length)))) {
                    foundMainLine = true;
                }
                continue;
            }
            // We're now processing lines after the main details line
            // Add any non-empty lines that don't look like dates or amounts
            const trimmedLine = line.trim();
            if (trimmedLine &&
                !trimmedLine.match(/^\d{2}\.\d{2}\.\d{4}/) &&
                !trimmedLine.match(/^\d{2}-[A-Z]{3}-\d{2}/) &&
                !trimmedLine.match(/^[\d,]+\.\d{2}$/) &&
                !trimmedLine.includes('RANDALPHA MFB/')) {
                details += ' ' + trimmedLine;
            }
        }
        // Extract value date
        const valueDateMatch = entryText.match(/(\d{2}-[A-Z]{3}-\d{2})/g);
        const valueDate = valueDateMatch && valueDateMatch.length > 1 ? valueDateMatch[1] : currentDate.date;
        // Extract amounts
        const debitMatch = entryText.match(/([\d,]+\.\d{2})/);
        const debit = debitMatch ? debitMatch[1] : '';
        // Extract credit (usually 0)
        const creditMatch = entryText.match(/\b(\d+)\b/);
        const credit = creditMatch ? creditMatch[1] : '0';
        // Extract balance
        const balanceMatch = entryText.match(/([\d,]+\.\d{2})\s*$/);
        const balance = balanceMatch ? balanceMatch[1] : '';
        // Create the entry
        const entry = {
            EntryDate: currentDate.date,
            Details: details, // Use the merged details that includes continuation lines
            ValueDate: valueDate,
            Debit: debit,
            Credit: credit,
            Balance: balance
        };
        // Check if this is a duplicate before adding
        if (!isDuplicate(result.rows, entry)) {
            result.rows.push(entry);
        }
    }
    // Also try the original regex pattern as a fallback
    if (result.rows.length <= 1) {
        const flexPattern = /(\d{2}-[A-Z]{3}-\d{2})[^\n]*?(\d{2}\.\d{2}\.\d{4}\s+[^\n]+?)[^\n]*?(\d{2}-[A-Z]{3}-\d{2})[^\n]*?([\d,]+\.\d{2})[^\n]*?(\d+)[^\n]*?([\d,]+\.\d{2})/g;
        let flexMatch;
        while ((flexMatch = flexPattern.exec(text)) !== null) {
            const entry = {
                EntryDate: flexMatch[1],
                Details: flexMatch[2].trim(),
                ValueDate: flexMatch[3],
                Debit: flexMatch[4],
                Credit: flexMatch[5],
                Balance: flexMatch[6]
            };
            // Check if this is a duplicate before adding
            if (!isDuplicate(result.rows, entry)) {
                result.rows.push(entry);
            }
        }
    }
}
/**
 * Extract transactions using date pattern matching
 */
function extractTransactionsFromDatePattern(text, result) {
    // Split the text into lines for processing
    const lines = text.split('\n');
    // This pattern looks for lines with the date format 01.03.2025 followed by RANDALPHA
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        const dateMatch = line.match(/(\d{2}\.\d{2}\.\d{4})\s+(RANDALPHA\s+MFB\/[^\n]+)/);
        if (dateMatch) {
            const dateStr = dateMatch[1];
            let details = dateMatch[2].trim();
            // Check if the details continue on the next lines
            // Keep checking subsequent lines until we hit a line that looks like a new entry
            let nextLineIndex = i + 1;
            while (nextLineIndex < lines.length &&
                !lines[nextLineIndex].match(/^\d{2}\.\d{2}\.\d{4}/) &&
                !lines[nextLineIndex].match(/^\d{2}-[A-Z]{3}-\d{2}/) &&
                !lines[nextLineIndex].match(/^[\d,]+\.\d{2}$/) &&
                !lines[nextLineIndex].includes('RANDALPHA MFB/')) {
                // Add the continuation line to the details
                if (lines[nextLineIndex].trim()) {
                    details += ' ' + lines[nextLineIndex].trim();
                }
                // Move to the next line
                nextLineIndex++;
            }
            // Skip all the lines we've processed
            i = nextLineIndex - 1;
            // Convert DD.MM.YYYY to DD-MMM-YY
            const valueDate = convertDateFormat(dateStr);
            // Look for entry date in nearby lines
            let entryDate = valueDate;
            for (let j = Math.max(0, i - 3); j <= i; j++) {
                const dateLine = lines[j].trim();
                const entryDateMatch = dateLine.match(/^(\d{2}-[A-Z]{3}-\d{2})/);
                if (entryDateMatch) {
                    entryDate = entryDateMatch[1];
                    break;
                }
            }
            // Find the debit amount and balance in nearby lines
            let debitAmount = '';
            let balance = '';
            // Look in the next few lines for amounts
            for (let j = i; j < Math.min(lines.length, i + 5); j++) {
                const amountLine = lines[j].trim();
                // Look for debit amount
                if (!debitAmount) {
                    const debitMatch = amountLine.match(/^([\d,]+\.\d{2})$/);
                    if (debitMatch) {
                        debitAmount = debitMatch[1];
                        continue;
                    }
                }
                // Look for balance
                if (!balance) {
                    const balanceMatch = amountLine.match(/([\d,]+\.\d{2})$/);
                    if (balanceMatch && amountLine !== debitAmount) {
                        balance = balanceMatch[1];
                    }
                }
            }
            // Create the entry
            const entry = {
                EntryDate: entryDate,
                Details: details,
                ValueDate: valueDate,
                Debit: debitAmount,
                Credit: '0',
                Balance: balance
            };
            // Check if this is a duplicate before adding
            if (!isDuplicate(result.rows, entry)) {
                result.rows.push(entry);
            }
        }
    }
    // Also try the original pattern as a fallback
    if (result.rows.length <= 1) {
        const transactionPattern = /(\d{2}\.\d{2}\.\d{4})\s+(RANDALPHA\s+MFB\/[^\n]+)/g;
        let transactionMatch;
        while ((transactionMatch = transactionPattern.exec(text)) !== null) {
            const dateStr = transactionMatch[1];
            const details = transactionMatch[2].trim();
            // Convert DD.MM.YYYY to DD-MMM-YY
            const valueDate = convertDateFormat(dateStr);
            // Find the debit amount by looking for patterns in the text
            const debitAmount = findDebitAmount(text, details);
            // Create the entry
            const entry = {
                EntryDate: valueDate,
                Details: details,
                ValueDate: valueDate,
                Debit: debitAmount,
                Credit: '0',
                Balance: ''
            };
            // Check if this is a duplicate before adding
            if (!isDuplicate(result.rows, entry)) {
                result.rows.push(entry);
            }
        }
    }
}
/**
 * Convert date from DD.MM.YYYY to DD-MMM-YY format
 */
function convertDateFormat(dateStr) {
    const dateParts = dateStr.split('.');
    if (dateParts.length === 3) {
        const day = dateParts[0];
        const month = parseInt(dateParts[1]);
        const year = dateParts[2].substring(2); // Get last 2 digits
        // Convert month number to abbreviated month name
        const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
            'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
        const monthName = monthNames[month - 1];
        return `${day}-${monthName}-${year}`;
    }
    return dateStr;
}
/**
 * Find debit amount for a transaction by looking for patterns in the text
 */
function findDebitAmount(text, details) {
    let debitAmount = '';
    const textLines = text.split('\n');
    for (let i = 0; i < textLines.length; i++) {
        const line = textLines[i].trim();
        if (line.includes(details.substring(0, 30))) {
            // Look for the debit amount in the next few lines
            for (let j = i - 3; j < i + 3; j++) {
                if (j >= 0 && j < textLines.length) {
                    const amountLine = textLines[j].trim();
                    const amountMatch = amountLine.match(/^[\d,]+\.\d{2}$/);
                    if (amountMatch) {
                        debitAmount = amountMatch[0];
                        break;
                    }
                }
            }
            break;
        }
    }
    return debitAmount;
}
/**
 * Check if an entry is a duplicate of an existing entry
 */
function isDuplicate(rows, entry) {
    return rows.some(row => (row.EntryDate === entry.EntryDate && row.Details === entry.Details) ||
        (row.Details === entry.Details && row.Debit === entry.Debit && entry.Debit !== ''));
}
/**
 * Clean and format the transaction data
 */
function cleanAndFormatTransactions(result) {
    // Make sure the balance entry stays at the top
    const balanceEntries = result.rows.filter(row => row.Details && row.Details.includes('Balance B/F'));
    const transactionEntries = result.rows.filter(row => !row.Details || !row.Details.includes('Balance B/F'));
    // Clean up the transaction entries
    for (const entry of transactionEntries) {
        // Make sure all fields are strings
        for (const key of Object.keys(entry)) {
            if (entry[key] === undefined || entry[key] === null) {
                entry[key] = '';
            }
        }
        // Clean up the Details field
        if (entry.Details) {
            // Remove any line breaks
            entry.Details = entry.Details.replace(/\n/g, ' ');
            // Make sure the format is consistent
            if (entry.Details.includes('RANDALPHA MFB/')) {
                // Extract the date and description parts
                const dateParts = entry.Details.match(/^(\d{2}\.\d{2}\.\d{4})\s+(.*)/);
                if (dateParts) {
                    // We only need the description part
                    const description = dateParts[2];
                    entry.Details = description;
                }
            }
            // Remove any duplicate information that might have been added from multiple extraction methods
            // For example, if the details contains the same text twice
            const words = entry.Details.split(/\s+/);
            if (words.length > 10) {
                // Check for duplicated phrases
                const firstHalf = words.slice(0, Math.floor(words.length / 2)).join(' ');
                const secondHalf = words.slice(Math.floor(words.length / 2)).join(' ');
                // If there's significant overlap, keep only the longer part
                if (firstHalf.includes(secondHalf) || secondHalf.includes(firstHalf)) {
                    entry.Details = firstHalf.length > secondHalf.length ? firstHalf : secondHalf;
                }
            }
            // Trim any extra whitespace
            entry.Details = entry.Details.replace(/\s+/g, ' ').trim();
        }
        // Make sure Debit and Credit are properly formatted
        if (entry.Debit && !entry.Debit.match(/^[\d,]+\.\d{2}$/)) {
            // Try to extract a valid amount
            const amountMatch = entry.Debit.match(/([\d,]+\.\d{2})/);
            entry.Debit = amountMatch ? amountMatch[1] : '';
        }
        if (entry.Credit && !entry.Credit.match(/^[\d,]+\.\d{2}$/)) {
            // Try to extract a valid amount or set to 0
            const amountMatch = entry.Credit.match(/([\d,]+\.\d{2})/);
            entry.Credit = amountMatch ? amountMatch[1] : '0';
        }
        // Make sure Balance is properly formatted
        if (entry.Balance && !entry.Balance.match(/^[\d,]+\.\d{2}$/)) {
            // Try to extract a valid amount
            const amountMatch = entry.Balance.match(/([\d,]+\.\d{2})/);
            entry.Balance = amountMatch ? amountMatch[1] : '';
        }
    }
    // Sort transaction entries by date
    transactionEntries.sort((a, b) => {
        // Convert dates to comparable format
        const dateA = a.EntryDate.replace(/-/g, '');
        const dateB = b.EntryDate.replace(/-/g, '');
        return dateA.localeCompare(dateB);
    });
    // Combine the entries with balance entries first
    result.rows = [...balanceEntries, ...transactionEntries];
    // Make sure we have the required headers
    result.headers = ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'];
}
/**
 * Extract bank statement information for Polaris Bank
 */
function extractPolarisBankInfo(text) {
    var _a;
    const lines = text.split('\n').filter(line => line.trim() !== '');
    const result = {
        bankName: 'Polaris Bank Limited',
        address: [],
        recipient: []
    };
    // Extract address and other metadata
    for (let i = 0; i < Math.min(30, lines.length); i++) {
        const line = lines[i].trim();
        if (line.includes('OGBOMOSHO') || line.includes('OYO')) {
            (_a = result.address) === null || _a === void 0 ? void 0 : _a.push(line);
        }
        // Extract date range
        if (line.match(/\d{2}-[A-Z]{3}-\d{2}\s+to\s+\d{2}-[A-Z]{3}-\d{2}/)) {
            result.dateRange = line;
        }
        // Extract account details
        if (line.includes('ACCOUNT - NGN')) {
            result.accountType = line;
            // Try to extract account number
            const accountMatch = line.match(/ACCOUNT\s*-\s*NGN\s*SORT\s*CODE-\s*(\d+)/i);
            if (accountMatch && accountMatch[1]) {
                result.accountNumber = accountMatch[1];
            }
        }
        else if (line.includes('SORT CODE')) {
            result.sortCode = line;
        }
        else if (line.includes('Report By')) {
            result.reportBy = line;
        }
        else if (line.includes('Staff No')) {
            result.staffInfo = line;
        }
        else if (line.includes('Branch')) {
            result.branch = line;
        }
    }
    return result;
}
exports.default = {
    extractPolarisBankTable,
    extractPolarisBankInfo
};
