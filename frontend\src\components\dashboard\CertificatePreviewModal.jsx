import React, { useState } from 'react';
import CertificateVerification from './CertificateVerification';
import CertificateShareModal from '../certificate/CertificateShareModal';
import { assets } from '../../assets/assets';

const CertificatePreviewModal = ({ certificate, onClose }) => {
  const [activeTab, setActiveTab] = useState('preview');
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  
  if (!certificate) return null;
  
  const handleDownload = () => {
    setIsDownloading(true);
    // Simulate download delay
    setTimeout(() => {
      setIsDownloading(false);
      // In a real app, this would trigger an actual download
      console.log('Certificate downloaded:', certificate);
    }, 1500);
  };
  
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };
  
  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
        {/* Certificate Header */}
        <div className="bg-[#412D6C] text-white p-6 rounded-t-xl flex justify-between items-center">
          <h3 className="text-xl font-bold">Certificate</h3>
          <button 
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('preview')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'preview'
                ? 'border-b-2 border-[#412D6C] text-[#412D6C]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Preview
          </button>
          <button
            onClick={() => setActiveTab('verify')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'verify'
                ? 'border-b-2 border-[#412D6C] text-[#412D6C]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Verify
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          {activeTab === 'preview' && (
            <div>
              <div className="bg-[#F9F8FC] border-8 border-[#412D6C]/10 rounded-lg p-8 relative overflow-hidden">
                {/* Background decoration */}
                <div className="absolute -right-20 -top-20 w-64 h-64 bg-[#412D6C]/5 rounded-full"></div>
                <div className="absolute -left-20 -bottom-20 w-64 h-64 bg-[#412D6C]/5 rounded-full"></div>

                {/* Certificate content */}
                <div className="relative text-center">
                  <div className="mb-6">
                    <img src={assets.logo} alt="Logo" className="h-12 mx-auto" />
                  </div>

                  <h2 className="text-2xl font-serif text-[#412D6C] mb-2">Certificate of Completion</h2>
                  <p className="text-gray-600 mb-6">This certifies that</p>

                  <h3 className="text-3xl font-serif text-[#412D6C] mb-6">{certificate.studentName}</h3>

                  <p className="text-gray-600 mb-6">has successfully completed the course</p>

                  <h4 className="text-2xl font-serif text-[#412D6C] mb-6">{certificate.title}</h4>

                  <div className="flex justify-center items-center space-x-12 mb-8">
                    <div className="text-center">
                      <p className="text-sm text-gray-500">Issue Date</p>
                      <p className="font-medium">{formatDate(certificate.issueDate)}</p>
                    </div>

                    <div className="text-center">
                      <p className="text-sm text-gray-500">Instructor</p>
                      <p className="font-medium">{certificate.instructor}</p>
                    </div>
                  </div>

                  <div className="flex justify-center items-center space-x-12">
                    <div className="text-center">
                      <div className="w-32 h-16 border-b border-gray-400 mb-2"></div>
                      <p className="text-sm text-gray-500">Instructor Signature</p>
                    </div>

                    <div className="text-center">
                      <div className="w-32 h-16 flex items-end justify-center">
                        <img src={assets.logo} alt="Seal" className="h-12 opacity-20" />
                      </div>
                      <p className="text-sm text-gray-500">Official Seal</p>
                    </div>
                  </div>

                  <div className="mt-8 text-sm text-gray-500">
                    Credential ID: {certificate.credentialId}
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  This certificate verifies the completion of the course on our platform.
                </div>
                
                <div className="flex space-x-4">
                  <button
                    onClick={() => setIsShareModalOpen(true)}
                    className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                    </svg>
                    Share
                  </button>
                  
                  <button 
                    onClick={handleDownload}
                    disabled={isDownloading}
                    className={`flex items-center px-4 py-2 rounded-md ${
                      isDownloading 
                        ? 'bg-gray-400 text-white cursor-not-allowed' 
                        : 'bg-[#412D6C] text-white hover:bg-[#362659]'
                    }`}
                  >
                    {isDownloading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Downloading...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download PDF
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'verify' && (
            <CertificateVerification certificate={certificate} />
          )}
        </div>
      </div>
      
      {/* Share Modal */}
      <CertificateShareModal 
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        certificate={certificate}
      />
    </div>
  );
};

export default CertificatePreviewModal;
