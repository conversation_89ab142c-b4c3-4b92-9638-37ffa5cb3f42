import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiRefreshCw, FiFileText, FiAlertCircle, FiCheck } from 'react-icons/fi';
import { motion } from 'framer-motion';
import useReconciliationStore from '../../store/reconciliationStore';
import { format } from 'date-fns';

const NewReconciliationPage = () => {
  const navigate = useNavigate();
  const { 
    statements, 
    fetchStatements, 
    createReconciliation, 
    statementsLoading, 
    statementsError,
    reconciliationsLoading,
    reconciliationsError
  } = useReconciliationStore();

  const [formData, setFormData] = useState({
    name: '',
    bank_statement_id: '',
    cashbook_statement_id: '',
  });
  const [formError, setFormError] = useState(null);
  const [createSuccess, setCreateSuccess] = useState(false);

  // Filter statements by type
  const bankStatements = statements.filter(s => s.statement_type === 'bank');
  const cashbookStatements = statements.filter(s => s.statement_type === 'cashbook');

  useEffect(() => {
    fetchStatements();
  }, [fetchStatements]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name) {
      setFormError('Please enter a reconciliation name');
      return;
    }

    if (!formData.bank_statement_id) {
      setFormError('Please select a bank statement');
      return;
    }

    if (!formData.cashbook_statement_id) {
      setFormError('Please select a cashbook statement');
      return;
    }

    try {
      setFormError(null);
      await createReconciliation(formData);
      setCreateSuccess(true);
      
      // Redirect after a short delay
      setTimeout(() => {
        navigate('/reconciliations');
      }, 2000);
    } catch (error) {
      setFormError(error.message || 'Failed to create reconciliation');
    }
  };

  // Loading state
  if (statementsLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading statements...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (statementsError) {
    return (
      <div className="bg-red-50 p-6 rounded-lg shadow-sm">
        <div className="flex items-center">
          <FiAlertCircle className="text-red-500 mr-3" size={24} />
          <h2 className="text-lg font-medium text-red-800">Error Loading Statements</h2>
        </div>
        <p className="mt-2 text-red-700">{statementsError}</p>
        <button 
          onClick={fetchStatements}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">New Reconciliation</h1>
        <button
          onClick={() => navigate('/reconciliations')}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel
        </button>
      </div>

      {createSuccess ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 p-6 rounded-lg shadow-sm mb-6"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiCheck className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-green-800">Reconciliation Created</h3>
              <div className="mt-2 text-green-700">
                <p>Your reconciliation has been created successfully. Redirecting to reconciliations list...</p>
              </div>
            </div>
          </div>
        </motion.div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error message */}
          {(formError || reconciliationsError) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 p-4 rounded-md"
            >
              <div className="flex">
                <div className="flex-shrink-0">
                  <FiAlertCircle className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{formError || reconciliationsError}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Reconciliation details */}
          <div className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Reconciliation Details</h2>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Reconciliation Name
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g. January 2023 Reconciliation"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="bank_statement_id" className="block text-sm font-medium text-gray-700">
                  Bank Statement
                </label>
                {bankStatements.length === 0 ? (
                  <div className="mt-1 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm text-yellow-700">
                      No bank statements available. Please upload a bank statement first.
                    </p>
                  </div>
                ) : (
                  <select
                    id="bank_statement_id"
                    name="bank_statement_id"
                    value={formData.bank_statement_id}
                    onChange={handleInputChange}
                    className="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    required
                  >
                    <option value="">Select a bank statement</option>
                    {bankStatements.map((statement) => (
                      <option key={statement.id} value={statement.id}>
                        {statement.name} ({format(new Date(statement.statement_date), 'MMM d, yyyy')})
                      </option>
                    ))}
                  </select>
                )}
              </div>
              
              <div>
                <label htmlFor="cashbook_statement_id" className="block text-sm font-medium text-gray-700">
                  Cashbook Statement
                </label>
                {cashbookStatements.length === 0 ? (
                  <div className="mt-1 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm text-yellow-700">
                      No cashbook statements available. Please upload a cashbook statement first.
                    </p>
                  </div>
                ) : (
                  <select
                    id="cashbook_statement_id"
                    name="cashbook_statement_id"
                    value={formData.cashbook_statement_id}
                    onChange={handleInputChange}
                    className="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    required
                  >
                    <option value="">Select a cashbook statement</option>
                    {cashbookStatements.map((statement) => (
                      <option key={statement.id} value={statement.id}>
                        {statement.name} ({format(new Date(statement.statement_date), 'MMM d, yyyy')})
                      </option>
                    ))}
                  </select>
                )}
              </div>
            </div>
          </div>

          {/* Submit button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={reconciliationsLoading || bankStatements.length === 0 || cashbookStatements.length === 0}
              className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                (reconciliationsLoading || bankStatements.length === 0 || cashbookStatements.length === 0) 
                  ? 'opacity-75 cursor-not-allowed' 
                  : ''
              }`}
            >
              {reconciliationsLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating...
                </>
              ) : (
                <>
                  <FiRefreshCw className="-ml-1 mr-2 h-5 w-5" />
                  Create Reconciliation
                </>
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default NewReconciliationPage;
