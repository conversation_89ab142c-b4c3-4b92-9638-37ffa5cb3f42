import { SPEAKER_ROUTES } from '../apiRoutes';
import { createAuthenticatedAxiosInstance } from './api';

// Create an authenticated axios instance
// We'll create the instance inside each function to ensure we have the latest token
const getApi = () => createAuthenticatedAxiosInstance();

// Speaker Service
const speakerService = {
  // Get all speakers
  getAllSpeakers: async () => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.GET_ALL_SPEAKERS);
      const api = getApi();
      const response = await api.get(SPEAKER_ROUTES.GET_ALL_SPEAKERS);
      console.log('API response:', response);

      // Return the data from the response
      return response.data;
    } catch (error) {
      console.error('Error in getAllSpeakers:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to fetch speakers');
      }

      throw new Error(error.message || 'Failed to fetch speakers');
    }
  },

  // Get verified speakers only
  getVerifiedSpeakers: async () => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.GET_VERIFIED_SPEAKERS);
      const api = getApi();
      const response = await api.get(SPEAKER_ROUTES.GET_VERIFIED_SPEAKERS);
      console.log('API response:', response);

      return response.data;
    } catch (error) {
      console.error('Error in getVerifiedSpeakers:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to fetch verified speakers');
      }

      throw new Error(error.message || 'Failed to fetch verified speakers');
    }
  },

  // Get a specific speaker by ID
  getSpeaker: async (id) => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.GET_SPEAKER(id));
      const api = getApi();
      const response = await api.get(SPEAKER_ROUTES.GET_SPEAKER(id));
      console.log('API response:', response);

      return response.data;
    } catch (error) {
      console.error('Error in getSpeaker:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to fetch speaker details');
      }

      throw new Error(error.message || 'Failed to fetch speaker details');
    }
  },

  // Get a specific speaker by slug
  getSpeakerBySlug: async (slug) => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.GET_SPEAKER_BY_SLUG(slug));
      const api = getApi();
      const response = await api.get(SPEAKER_ROUTES.GET_SPEAKER_BY_SLUG(slug));
      console.log('API response:', response);

      return response.data;
    } catch (error) {
      console.error('Error in getSpeakerBySlug:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to fetch speaker details');
      }

      throw new Error(error.message || 'Failed to fetch speaker details');
    }
  },

  // Search for speakers by name or topic
  searchSpeakers: async (query) => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.SEARCH_SPEAKERS(query));
      const api = getApi();
      const response = await api.get(SPEAKER_ROUTES.SEARCH_SPEAKERS(query));
      console.log('API response:', response);

      return response.data;
    } catch (error) {
      console.error('Error in searchSpeakers:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to search speakers');
      }

      throw new Error(error.message || 'Failed to search speakers');
    }
  },

  // Register as a new speaker
  registerSpeaker: async (speakerData) => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.REGISTER_SPEAKER);
      console.log('Register data:', speakerData);
      const api = getApi();
      const response = await api.post(SPEAKER_ROUTES.REGISTER_SPEAKER, speakerData);
      console.log('API response:', response);

      return response.data;
    } catch (error) {
      console.error('Error in registerSpeaker:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to register speaker');
      }

      throw new Error(error.message || 'Failed to register speaker');
    }
  },

  // Verify a speaker (admin only)
  verifySpeaker: async (id) => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.VERIFY_SPEAKER(id));
      const api = getApi();
      const response = await api.put(SPEAKER_ROUTES.VERIFY_SPEAKER(id));
      console.log('API response:', response);

      return response.data;
    } catch (error) {
      console.error('Error in verifySpeaker:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to verify speaker');
      }

      throw new Error(error.message || 'Failed to verify speaker');
    }
  },

  // Update speaker information
  updateSpeaker: async (id, speakerData) => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.UPDATE_SPEAKER(id));
      console.log('Update data:', speakerData);
      const api = getApi();
      const response = await api.put(SPEAKER_ROUTES.UPDATE_SPEAKER(id), speakerData);
      console.log('API response:', response);

      return response.data;
    } catch (error) {
      console.error('Error in updateSpeaker:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to update speaker');
      }

      throw new Error(error.message || 'Failed to update speaker');
    }
  },

  // Delete a speaker (admin only)
  deleteSpeaker: async (id) => {
    try {
      console.log('Calling API:', SPEAKER_ROUTES.DELETE_SPEAKER(id));
      const api = getApi();
      const response = await api.delete(SPEAKER_ROUTES.DELETE_SPEAKER(id));
      console.log('API response:', response);

      return response.data;
    } catch (error) {
      console.error('Error in deleteSpeaker:', error);

      // Check if there's a response with data
      if (error.response && error.response.data) {
        console.error('Error response data:', error.response.data);
        throw new Error(error.response.data.message || 'Failed to delete speaker');
      }

      throw new Error(error.message || 'Failed to delete speaker');
    }
  },
};

export default speakerService;
