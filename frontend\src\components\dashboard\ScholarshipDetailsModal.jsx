import React from 'react';

const ScholarshipDetailsModal = ({ scholarship, onClose, onApply }) => {
  if (!scholarship) return null;
  
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white p-6 border-b border-gray-200 flex justify-between items-center rounded-t-xl">
          <h2 className="text-xl font-bold text-gray-800">{scholarship.title}</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <div className="flex items-center">
              <div className="bg-[#412D6C]/10 p-3 rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#412D6C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M12 14l9-5-9-5-9 5 9 5z" />
                  <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                </svg>
              </div>
              <div>
                <span className="text-sm text-green-600 font-medium">{scholarship.status}</span>
                <h3 className="text-lg font-medium text-gray-900">{scholarship.title}</h3>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Award Amount</div>
              <div className="text-xl font-bold text-[#412D6C]">${scholarship.amount.toLocaleString()}</div>
            </div>
          </div>
          
          <div className="space-y-6">
            <div>
              <h4 className="text-md font-medium text-gray-800 mb-2">Description</h4>
              <p className="text-gray-600">{scholarship.description}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-md font-medium text-gray-800 mb-2">Eligibility</h4>
                <ul className="list-disc pl-5 text-gray-600 space-y-1">
                  {scholarship.eligibility.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="text-md font-medium text-gray-800 mb-2">Requirements</h4>
                <ul className="list-disc pl-5 text-gray-600 space-y-1">
                  {scholarship.requirements.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div>
              <h4 className="text-md font-medium text-gray-800 mb-2">Important Dates</h4>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500">Application Opens</div>
                    <div className="font-medium">{scholarship.dates.open}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Application Deadline</div>
                    <div className="font-medium">{scholarship.dates.deadline}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Notification Date</div>
                    <div className="font-medium">{scholarship.dates.notification}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Award Date</div>
                    <div className="font-medium">{scholarship.dates.award}</div>
                  </div>
                </div>
              </div>
            </div>
            
            {scholarship.additionalInfo && (
              <div>
                <h4 className="text-md font-medium text-gray-800 mb-2">Additional Information</h4>
                <p className="text-gray-600">{scholarship.additionalInfo}</p>
              </div>
            )}
            
            <div className="border-t border-gray-200 pt-6 flex justify-between items-center">
              <div>
                <div className="text-sm text-gray-500">Application Deadline</div>
                <div className="font-medium text-red-600">{scholarship.dates.deadline}</div>
              </div>
              
              <div className="flex space-x-4">
                <button
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Close
                </button>
                <button
                  onClick={() => onApply(scholarship)}
                  className="px-4 py-2 bg-[#412D6C] text-white rounded-md hover:bg-[#362659]"
                >
                  Apply Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScholarshipDetailsModal;
