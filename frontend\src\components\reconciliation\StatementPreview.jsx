import React, { useState, useEffect, useCallback } from 'react';
import { FiFileText, FiDownload, FiAlertCircle, FiBarChart2, FiRefreshCw } from 'react-icons/fi';
import { formatDate, formatFileSize } from '../../utils/formatters';
import api from '../../services/api';
import FilePreview from '../files/FilePreview';
import ProgressLoader from '../common/ProgressLoader';

const StatementPreview = ({
  statementId,
  cachedData = null,
  isLoading = false,
  error: externalError = null
}) => {
  const [preview, setPreview] = useState(cachedData);
  const [loading, setLoading] = useState(cachedData ? false : true);
  const [error, setError] = useState(externalError);
  const [analyzing, setAnalyzing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(cachedData ? 100 : 0);

  // Function to fetch preview data
  const fetchPreview = useCallback(async () => {
    if (!statementId) return;

    try {
      setLoading(true);
      setError(null);

      // Start with 50% progress to indicate we're fetching
      setProcessingProgress(50);

      const response = await api.statement.getStatementPreview(statementId);

      // Debug the response
      console.log('Preview response:', response);

      // Set progress to 100% since we've received data
      setProcessingProgress(100);

      // Check if the response has the expected structure
      if (response && response.status === true && response.data) {
        // The response is valid, use it directly
        setPreview(response.data);
      } else if (response && response.status === false && response.data) {
        // This is an error response, but it has data we can display
        setPreview(response.data);
        // Also set the error message
        setError(response.message || 'Failed to load preview');
      } else if (response && response.data) {
        // Legacy format - ensure we have all required fields
        setPreview({
          ...response.data,
          name: response.data.name || 'Bank Statement',
          fileName: response.data.fileName || 'statement.xlsx',
          fileType: response.data.fileType || 'excel',
          fileSize: response.data.fileSize || 0,
          uploadDate: response.data.uploadDate || new Date().toISOString(),
          processingStatus: 'completed' // Assume completed since we got data
        });
      } else {
        // Invalid response format
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching statement preview:', err);
      setProcessingProgress(0);

      // Handle different types of errors
      if (err.response && err.response.data) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const errorMessage = err.response.data?.message || `Server error: ${err.response.status}`;
        setError(errorMessage);

        // If the error response has data, use it
        if (err.response.data?.data) {
          setPreview(err.response.data.data);
        } else {
          // Create a minimal valid preview object
          setPreview({
            name: 'Error',
            fileName: 'statement.xlsx',
            fileType: 'excel',
            fileSize: 0,
            uploadDate: new Date().toISOString(),
            preview: 'No preview available',
            content: [],
            metadata: {},
            error: errorMessage,
            processingStatus: 'failed'
          });
        }
      } else if (err.request) {
        // The request was made but no response was received
        const errorMessage = 'No response from server. Please check your connection.';
        setError(errorMessage);

        // Create a minimal valid preview object
        setPreview({
          name: 'Connection Error',
          fileName: 'statement.xlsx',
          fileType: 'excel',
          fileSize: 0,
          uploadDate: new Date().toISOString(),
          preview: 'No preview available',
          content: [],
          metadata: {},
          error: errorMessage,
          processingStatus: 'failed'
        });
      } else {
        // Something happened in setting up the request that triggered an Error
        const errorMessage = err.message || 'Failed to load statement preview';
        setError(errorMessage);

        // Create a minimal valid preview object
        setPreview({
          name: 'Error',
          fileName: 'statement.xlsx',
          fileType: 'excel',
          fileSize: 0,
          uploadDate: new Date().toISOString(),
          preview: 'No preview available',
          content: [],
          metadata: {},
          error: errorMessage,
          processingStatus: 'failed'
        });
      }
    } finally {
      setLoading(false);
    }
  }, [statementId]);

  // Update state when props change
  useEffect(() => {
    if (cachedData) {
      setPreview(cachedData);
      setLoading(false);
      setProcessingProgress(100);
    }
  }, [cachedData]);

  // Update loading state when isLoading prop changes
  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  // Update error state when externalError prop changes
  useEffect(() => {
    setError(externalError);
  }, [externalError]);

  // Initial fetch only if we don't have cached data
  useEffect(() => {
    if (!cachedData) {
      console.log('No cached data, fetching preview data');
      setProcessingProgress(0);
      fetchPreview();
    } else {
      console.log('Using cached preview data');
    }
  }, [statementId, fetchPreview, cachedData]);

  const handleDownload = async () => {
    try {
      // Use the statement ID to download the file
      const downloadUrl = `/api/statements/${statementId}/download`;

      // Create a hidden link and click it to trigger the download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.target = '_blank';
      link.download = preview?.fileName || 'statement';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.error('Error downloading statement:', err);
      alert('Failed to download file: ' + (err.message || 'Unknown error'));
    }
  };

  const handleAnalyze = async () => {
    if (!preview) {
      console.error('No preview data available for analysis');
      return;
    }

    try {
      setAnalyzing(true);

      // If we have a direct file path, use it
      if (preview.filePath) {
        const response = await api.file.previewFile(preview.filePath);

        if (response.status && response.data) {
          // Update the preview with the analysis results
          setPreview({
            ...preview,
            ...response.data
          });
        }
      } else {
        // Otherwise, use the statement ID to get the preview
        const response = await api.statement.getStatementPreview(statementId);

        if (response.status && response.data) {
          // Update the preview with the analysis results
          setPreview(response.data);
        }
      }
    } catch (err) {
      console.error('Error analyzing file:', err);
      // Show error but don't set the main error state
      alert('Failed to analyze file: ' + (err.message || 'Unknown error'));
    } finally {
      setAnalyzing(false);
    }
  };

  // Handle initial loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="w-64">
          <ProgressLoader
            status="processing"
            progress={processingProgress}
            text="Loading statement data..."
            showPercentage={true}
          />
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">
            {preview?.name || 'Statement Preview'}
          </h2>
        </div>

        <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
          <div className="my-6">
            <ProgressLoader
              status="failed"
              progress={0}
              text="Failed to load statement data"
              showPercentage={false}
              onRetry={() => {
                setError(null);
                setProcessingProgress(0);
                fetchPreview();
              }}
            />
          </div>

          <div className="text-sm text-red-500 mt-4">
            <p>{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!preview) {
    return (
      <div className="text-center p-4 text-gray-500">
        No preview available
      </div>
    );
  }

  // Debug the preview data
  console.log('StatementPreview preview:', preview);

  // Check if we have content preview data
  if (preview) {
    return (
      <FilePreview
        fileData={preview}
        onDownload={handleDownload}
        onAnalyze={handleAnalyze}
      />
    );
  }

  // Fallback to basic preview if no content data is available
  return (
    <div className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium text-gray-900">{preview.name}</h2>
        <div className="flex space-x-2">
          <button
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={handleAnalyze}
            disabled={analyzing}
          >
            {analyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
                Analyzing...
              </>
            ) : (
              <>
                <FiBarChart2 className="mr-2 -ml-0.5 h-4 w-4" />
                Analyze
              </>
            )}
          </button>
          <button
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            onClick={handleDownload}
          >
            <FiDownload className="mr-2 -ml-0.5 h-4 w-4" />
            Download
          </button>
        </div>
      </div>

      <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
        <div className="flex items-center mb-4">
          <FiFileText className="h-10 w-10 text-indigo-500 mr-3" />
          <div>
            <p className="text-sm font-medium text-gray-900">{preview.fileName}</p>
            <p className="text-xs text-gray-500">
              {preview.fileType.toUpperCase().replace('.', '')} • {formatFileSize(preview.fileSize)}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500">Uploaded</p>
            <p className="font-medium">{formatDate(preview.uploadDate)}</p>
          </div>
          <div>
            <p className="text-gray-500">Type</p>
            <p className="font-medium capitalize">{preview.fileType.replace('.', '')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatementPreview;