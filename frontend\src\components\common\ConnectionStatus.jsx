import React, { useState, useEffect } from 'react';
import { FiCheckCircle, FiXCircle, FiRefreshCw, FiLock } from 'react-icons/fi';
import { API_BASE_URL } from '../../apiRoutes';
import { useNavigate } from 'react-router-dom';

const ConnectionStatus = () => {
  const navigate = useNavigate();
  const [status, setStatus] = useState({
    loading: true,
    connected: false,
    message: 'Checking connection...',
    authStatus: 'checking'
  });

  // Check if auth token exists
  const checkAuthToken = () => {
    // Check for token in multiple places
    const localStorageToken = localStorage.getItem('authToken');
    const backupToken = localStorage.getItem('auth_token_backup');
    const sessionStorageToken = sessionStorage.getItem('authToken');

    // Get token from cookie if available
    const getCookieToken = () => {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.startsWith('authToken=')) {
          return cookie.substring('authToken='.length, cookie.length);
        }
      }
      return null;
    };

    const cookieToken = getCookieToken();

    // Use any available token
    const isAuthenticated = !!(localStorageToken || backupToken || sessionStorageToken || cookieToken);

    // If we have a token in backup but not in primary, restore it
    if (!localStorageToken && (backupToken || sessionStorageToken || cookieToken)) {
      const token = backupToken || sessionStorageToken || cookieToken;
      console.log('Restoring token from backup source');
      localStorage.setItem('authToken', token);
      return true;
    }

    return isAuthenticated;
  };

  const checkConnection = async () => {
    setStatus({
      ...status,
      loading: true,
      connected: false,
      message: 'Checking connection...'
    });

    // First check if auth token exists
    const isAuthenticated = checkAuthToken();

    if (!isAuthenticated) {
      console.log('No auth token found, redirecting to login');
      setStatus({
        loading: false,
        connected: false,
        message: 'Authentication required',
        authStatus: 'unauthenticated'
      });

      // Redirect to login after a short delay
      setTimeout(() => {
        navigate('/login');
      }, 1500);

      return;
    }

    try {
      // Try to connect to the backend health check endpoint
      const response = await fetch(`${API_BASE_URL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Backend connection successful:', data);

        setStatus({
          loading: false,
          connected: true,
          message: 'Connected to backend successfully',
          authStatus: 'authenticated'
        });
      } else {
        console.error('Backend connection failed with status:', response.status);

        // Check if it's an authentication error
        if (response.status === 401) {
          setStatus({
            loading: false,
            connected: false,
            message: 'Authentication failed - redirecting to login',
            authStatus: 'unauthenticated'
          });

          // Clear tokens
          localStorage.removeItem('authToken');
          localStorage.removeItem('auth_token_backup');
          sessionStorage.removeItem('authToken');

          // Redirect to login after a short delay
          setTimeout(() => {
            navigate('/login');
          }, 1500);
        } else {
          setStatus({
            loading: false,
            connected: false,
            message: `Failed to connect to backend: ${response.status} ${response.statusText}`,
            authStatus: 'authenticated'
          });
        }
      }
    } catch (error) {
      console.error('Backend connection error:', error);

      setStatus({
        loading: false,
        connected: false,
        message: `Error connecting to backend: ${error.message}`,
        authStatus: isAuthenticated ? 'authenticated' : 'unauthenticated'
      });
    }
  };

  useEffect(() => {
    checkConnection();
  }, [navigate]);

  return (
    <div className="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-lg font-medium mb-2">System Status</h3>

      {/* Authentication Status */}
      <div className="flex items-center mb-3">
        <div className="mr-2">
          {status.authStatus === 'checking' ? (
            <FiRefreshCw className="animate-spin text-blue-500" size={20} />
          ) : status.authStatus === 'authenticated' ? (
            <FiCheckCircle className="text-green-500" size={20} />
          ) : (
            <FiLock className="text-red-500" size={20} />
          )}
        </div>

        <div className="text-sm">
          {status.authStatus === 'checking' ? 'Checking authentication...' :
           status.authStatus === 'authenticated' ? 'Authenticated' :
           'Not authenticated - redirecting to login'}
        </div>
      </div>

      {/* Connection Status */}
      <div className="flex items-center mb-3">
        <div className="mr-2">
          {status.loading ? (
            <FiRefreshCw className="animate-spin text-blue-500" size={20} />
          ) : status.connected ? (
            <FiCheckCircle className="text-green-500" size={20} />
          ) : (
            <FiXCircle className="text-red-500" size={20} />
          )}
        </div>

        <div className="text-sm">
          {status.message}
        </div>
      </div>

      {/* Only show refresh button if authenticated */}
      {status.authStatus === 'authenticated' && (
        <button
          onClick={checkConnection}
          disabled={status.loading}
          className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 disabled:opacity-50 flex items-center"
        >
          {status.loading ? (
            <>
              <FiRefreshCw className="animate-spin mr-1" size={14} />
              Checking...
            </>
          ) : (
            <>
              <FiRefreshCw className="mr-1" size={14} />
              Refresh
            </>
          )}
        </button>
      )}

      {/* Show login button if not authenticated */}
      {status.authStatus === 'unauthenticated' && (
        <button
          onClick={() => navigate('/login')}
          className="px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 flex items-center"
        >
          <FiLock className="mr-1" size={14} />
          Go to Login
        </button>
      )}
    </div>
  );
};

export default ConnectionStatus;
