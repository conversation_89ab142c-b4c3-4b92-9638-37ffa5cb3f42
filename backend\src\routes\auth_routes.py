"""
Authentication routes for the Bank Reconciliation API.
"""

from flask import Blueprint
from controllers.auth_controller import register, login

# Create blueprint
auth_bp = Blueprint('auth', __name__)

# @route   POST /api/auth/register
# @desc    Register a new user
# @access  Public
auth_bp.route('/register', methods=['POST'])(register)

# @route   POST /api/auth/login
# @desc    Login user
# @access  Public
auth_bp.route('/login', methods=['POST'])(login)
