@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Poppins', sans-serif;
  }

  body {
    @apply bg-gray-50;
  }
}

@layer components {
  .glass-card {
    @apply backdrop-blur-md bg-white/80 border border-white/20 shadow-lg;
  }

  .gradient-text {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-[#412D6C] to-[#6A4CA6];
  }

  .btn-primary {
    @apply px-6 py-3 bg-gradient-to-r from-[#412D6C] to-[#6A4CA6] text-white rounded-lg 
    hover:shadow-lg transition-all duration-300 hover:-translate-y-1;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-gradient-to-r from-[#F1D31F] to-[#F7E675] text-[#412D6C] rounded-lg 
    hover:shadow-lg transition-all duration-300 hover:-translate-y-1;
  }

  .input-primary {
    @apply w-full p-3 rounded-lg border border-gray-200 focus:ring-2 focus:ring-[#412D6C] 
    focus:border-transparent outline-none transition-all duration-300;
  }

  .card-hover {
    @apply hover:shadow-xl hover:-translate-y-1 transition-all duration-300;
  }

  .glass-morphism {
    @apply backdrop-blur-md bg-white/80 border border-white/20 shadow-lg;
  }
}

/* Animation Classes */
.fade-up {
  animation: fadeUp 0.5s ease-out forwards;
}

.slide-in {
  animation: slideIn 0.5s ease-out forwards;
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(145deg, #412D6C, #6A4CA6);
}

.gradient-secondary {
  background: linear-gradient(145deg, #F1D31F, #F7E675);
}

/* Glass Effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Swiper Gallery Styles */
.gallery-swiper .swiper-slide {
  height: 300px;
}

.gallery-swiper .swiper-button-next,
.gallery-swiper .swiper-button-prev {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2rem;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
}

.gallery-swiper .swiper-button-next::after,
.gallery-swiper .swiper-button-prev::after {
  font-size: 1rem;
}

.swiper-zoom-container {
  cursor: zoom-in;
}

/* Event Gallery Styles */
.event-swiper {
  padding: 1rem 0 3rem 0;
}

.event-swiper .swiper-button-next,
.event-swiper .swiper-button-prev {
  color: #412D6C;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 1.5rem;
  border-radius: 50%;
  width: 1rem;
  height: 1rem;
}

.event-swiper .swiper-button-next::after,
.event-swiper .swiper-button-prev::after {
  font-size: 1rem;
  font-weight: bold;
}

.event-swiper .swiper-pagination-bullet {
  background: #412D6C;
}

.event-swiper .swiper-pagination-bullet-active {
  background: #412D6C;
  transform: scale(1.2);
}
