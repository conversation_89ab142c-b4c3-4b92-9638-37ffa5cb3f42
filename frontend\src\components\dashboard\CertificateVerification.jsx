import React, { useState } from 'react';
import certificateService from '../../services/certificateService';

const CertificateVerification = ({ certificate }) => {
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState(null);
  
  const handleVerify = async () => {
    setIsVerifying(true);
    setVerificationError(null);
    
    try {
      // Use the certificate service to verify the certificate
      const response = await certificateService.verifyCertificate(certificate.credentialId);
      
      if (response.status) {
        setVerificationStatus('verified');
      } else {
        setVerificationStatus('failed');
        setVerificationError(response.error || 'Certificate could not be verified');
      }
    } catch (error) {
      console.error('Error verifying certificate:', error);
      setVerificationStatus('failed');
      setVerificationError('An error occurred during verification');
    } finally {
      setIsVerifying(false);
    }
  };
  
  const handleCopyLink = () => {
    const verificationUrl = `${window.location.origin}/certificates/verify/${certificate.credentialId}`;
    
    navigator.clipboard.writeText(verificationUrl)
      .then(() => {
        alert('Verification link copied to clipboard!');
      })
      .catch(err => {
        console.error('Failed to copy verification link:', err);
        alert('Failed to copy verification link. Please try again.');
      });
  };
  
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
      <h3 className="text-lg font-medium text-gray-800 mb-4">Certificate Verification</h3>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            <span className="font-medium">Credential ID:</span> {certificate.credentialId}
          </div>
          
          <button 
            onClick={handleCopyLink}
            className="text-sm text-[#412D6C] hover:underline flex items-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
            </svg>
            Copy Verification Link
          </button>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            <span className="font-medium">Issued:</span> {formatDate(certificate.issueDate)}
          </div>
          
          <div className="text-sm text-gray-600">
            <span className="font-medium">Valid Until:</span> {certificate.validUntil ? formatDate(certificate.validUntil) : 'Lifetime'}
          </div>
        </div>
        
        {certificate.blockchain && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">Blockchain:</span> {certificate.blockchain.network}
            {certificate.blockchain.transactionId && (
              <span className="ml-2 text-xs text-gray-500">
                (TX: {certificate.blockchain.transactionId.substring(0, 8)}...)
              </span>
            )}
          </div>
        )}
        
        <div className="pt-4 border-t border-gray-200">
          {verificationStatus === null ? (
            <button
              onClick={handleVerify}
              disabled={isVerifying}
              className={`w-full py-2 rounded-lg text-white ${
                isVerifying 
                  ? 'bg-gray-400 cursor-not-allowed' 
                  : 'bg-[#412D6C] hover:bg-[#362659] transition-colors'
              }`}
            >
              {isVerifying ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Verifying...
                </span>
              ) : (
                'Verify Certificate'
              )}
            </button>
          ) : verificationStatus === 'verified' ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center">
              <div className="bg-green-100 rounded-full p-1 mr-3">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <p className="text-green-700 font-medium">Certificate Verified</p>
                <p className="text-green-600 text-sm">This certificate is authentic and was issued by our platform.</p>
              </div>
            </div>
          ) : (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
              <div className="bg-red-100 rounded-full p-1 mr-3">
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <div>
                <p className="text-red-700 font-medium">Verification Failed</p>
                <p className="text-red-600 text-sm">{verificationError || 'This certificate could not be verified.'}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CertificateVerification;
