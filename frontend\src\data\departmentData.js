export const departmentConfigs = {
  admin: {
    color: '#412D6C',
    menuItems: [
      { label: 'Overview', path: '/dashboard/admin', icon: '📊' },
      {
        label: 'User Management',
        path: '/dashboard/projects/users',
        icon: '👥',
        subItems: [
          { label: 'All Users', path: '/dashboard/projects/users/all' },
          { label: 'Students', path: '/dashboard/projects/users/students' },
          { label: 'Instructors', path: '/dashboard/projects/users/instructors' },
          { label: 'Admins', path: '/dashboard/projects/users/admins' }
        ]
      },
      {
        label: 'Course Management',
        path: '/dashboard/projects/courses',
        icon: '📚',
        subItems: [
          { label: 'All Courses', path: '/dashboard/projects/courses/all' },
          { label: 'Published', path: '/dashboard/projects/courses/published' },
          { label: 'Drafts', path: '/dashboard/projects/courses/drafts' },
          { label: 'Categories', path: '/dashboard/projects/courses/categories' }
        ]
      },
      {
        label: 'Certificate Management',
        path: '/dashboard/projects/certificates',
        icon: '🏆',
        subItems: [
          { label: 'All Certificates', path: '/dashboard/projects/certificates' },
          { label: 'Issued', path: '/dashboard/projects/certificates/issued' },
          { label: 'Pending', path: '/dashboard/projects/certificates/pending' },
          { label: 'Templates', path: '/dashboard/projects/certificates/templates' }
        ]
      },
      {
        label: 'Scholarship Management',
        path: '/dashboard/projects/scholarships',
        icon: '🎓',
        subItems: [
          { label: 'All Scholarships', path: '/dashboard/projects/scholarships/all' },
          { label: 'Active', path: '/dashboard/projects/scholarships/active' },
          { label: 'Applications', path: '/dashboard/projects/scholarships/applications' },
          { label: 'Awards', path: '/dashboard/projects/scholarships/awards' }
        ]
      },
      {
        label: 'Internship Management',
        path: '/dashboard/projects/internships',
        icon: '💼',
        subItems: [
          { label: 'All Internships', path: '/dashboard/projects/internships/all' },
          { label: 'Active', path: '/dashboard/projects/internships/active' },
          { label: 'Applications', path: '/dashboard/projects/internships/applications' },
          { label: 'Partners', path: '/dashboard/projects/internships/partners' }
        ]
      },
      {
        label: 'Speaker Management',
        path: '/dashboard/projects/speakers',
        icon: '🎤',
        subItems: [
          { label: 'All Speakers', path: '/dashboard/projects/speakers' },
          { label: 'Verified Speakers', path: '/dashboard/projects/speakers/verified' },
          { label: 'Pending Speakers', path: '/dashboard/projects/speakers/pending' },
          { label: 'Add Speaker', path: '/dashboard/projects/speakers/add' }
        ]
      },
      { label: 'Settings', path: '/dashboard/admin/settings', icon: '⚙️' }
    ]
  },
  projects: {
    color: '#4B5563',
    menuItems: [
      { label: 'Overview', path: '/dashboard/projects', icon: '📊' },
      {
        label: 'User Management',
        path: '/dashboard/projects/users',
        icon: '👥',
        subItems: [
          { label: 'All Users', path: '/dashboard/projects/users/all' },
          { label: 'Students', path: '/dashboard/projects/users/students' },
          { label: 'Instructors', path: '/dashboard/projects/users/instructors' },
          { label: 'Admins', path: '/dashboard/projects/users/admins' }
        ]
      },
      {
        label: 'Course Management',
        path: '/dashboard/projects/courses',
        icon: '📚',
        subItems: [
          { label: 'All Courses', path: '/dashboard/projects/courses/all' },
          { label: 'Published', path: '/dashboard/projects/courses/published' },
          { label: 'Drafts', path: '/dashboard/projects/courses/drafts' },
          { label: 'Categories', path: '/dashboard/projects/courses/categories' }
        ]
      },
      {
        label: 'Certificate Management',
        path: '/dashboard/projects/certificates',
        icon: '🏆',
        subItems: [
          { label: 'All Certificates', path: '/dashboard/projects/certificates' },
          { label: 'Issued', path: '/dashboard/projects/certificates/issued' },
          { label: 'Pending', path: '/dashboard/projects/certificates/pending' },
          { label: 'Templates', path: '/dashboard/projects/certificates/templates' }
        ]
      },
      {
        label: 'Scholarship Management',
        path: '/dashboard/projects/scholarships',
        icon: '🎓',
        subItems: [
          { label: 'All Scholarships', path: '/dashboard/projects/scholarships/all' },
          { label: 'Active', path: '/dashboard/projects/scholarships/active' },
          { label: 'Applications', path: '/dashboard/projects/scholarships/applications' },
          { label: 'Awards', path: '/dashboard/projects/scholarships/awards' }
        ]
      },
      {
        label: 'Internship Management',
        path: '/dashboard/projects/internships',
        icon: '💼',
        subItems: [
          { label: 'All Internships', path: '/dashboard/projects/internships/all' },
          { label: 'Active', path: '/dashboard/projects/internships/active' },
          { label: 'Applications', path: '/dashboard/projects/internships/applications' },
          { label: 'Partners', path: '/dashboard/projects/internships/partners' }
        ]
      },
      {
        label: 'Speaker Management',
        path: '/dashboard/projects/speakers',
        icon: '🎤',
        subItems: [
          { label: 'All Speakers', path: '/dashboard/projects/speakers' },
          { label: 'Verified Speakers', path: '/dashboard/projects/speakers/verified' },
          { label: 'Pending Speakers', path: '/dashboard/projects/speakers/pending' },
          { label: 'Add Speaker', path: '/dashboard/projects/speakers/add' }
        ]
      }
    ]
  },
  ceo: {
    color: '#412D6C',
    menuItems: [
      { label: 'Overview', path: '/dashboard/ceo', icon: '📊' },
      { label: 'Departments', path: '/dashboard/ceo/departments', icon: '🏢' },
      { label: 'Analytics', path: '/dashboard/ceo/analytics', icon: '📈' },
      { label: 'Strategy', path: '/dashboard/ceo/strategy', icon: '🎯', badge: 'New' },
      { label: 'Reports', path: '/dashboard/ceo/reports', icon: '📑' }
    ]
  },
  engineering: {
    color: '#2563EB',
    menuItems: [
      { label: 'Overview', path: '/dashboard/engineering', icon: '💻' },
      { label: 'Projects', path: '/dashboard/engineering/projects', icon: '📁' },
      { label: 'Deployments', path: '/dashboard/engineering/deployments', icon: '🚀', badge: '3' },
      { label: 'Pull Requests', path: '/dashboard/engineering/pull-requests', icon: '🔄' },
      { label: 'Documentation', path: '/dashboard/engineering/docs', icon: '📚' }
    ]
  },
  marketing: {
    color: '#DC2626',
    menuItems: [
      { label: 'Overview', path: '/dashboard/marketing', icon: '📢' },
      { label: 'Campaigns', path: '/dashboard/marketing/campaigns', icon: '🎯', badge: 'Active' },
      { label: 'Social Media', path: '/dashboard/marketing/social', icon: '🌐' },
      { label: 'Content', path: '/dashboard/marketing/content', icon: '✍️' },
      { label: 'Analytics', path: '/dashboard/marketing/analytics', icon: '📊' }
    ]
  },
  hr: {
    color: '#059669',
    menuItems: [
      { label: 'Overview', path: '/dashboard/hr', icon: '👥' },
      { label: 'Employees', path: '/dashboard/hr/employees', icon: '👤' },
      { label: 'Recruitment', path: '/dashboard/hr/recruitment', icon: '🎯', badge: '5' },
      { label: 'Training', path: '/dashboard/hr/training', icon: '📚' },
      { label: 'Reports', path: '/dashboard/hr/reports', icon: '📊' }
    ]
  },
  ambassadors: {
    color: '#7C3AED',
    menuItems: [
      { label: 'Overview', path: '/dashboard/ambassadors', icon: '🌟' },
      { label: 'Events', path: '/dashboard/ambassadors/events', icon: '📅', badge: 'Live' },
      { label: 'Community', path: '/dashboard/ambassadors/community', icon: '🤝' },
      { label: 'Resources', path: '/dashboard/ambassadors/resources', icon: '📚' },
      { label: 'Reports', path: '/dashboard/ambassadors/reports', icon: '📊' }
    ]
  },
  growth: {
    color: '#0891B2',
    menuItems: [
      { label: 'Overview', path: '/dashboard/growth', icon: '📈' },
      { label: 'Metrics', path: '/dashboard/growth/metrics', icon: '📊' },
      { label: 'Experiments', path: '/dashboard/growth/experiments', icon: '🧪', badge: 'New' },
      { label: 'User Analytics', path: '/dashboard/growth/users', icon: '👥' },
      { label: 'Reports', path: '/dashboard/growth/reports', icon: '📑' }
    ]
  },
  accounts: {
    color: '#0F766E',
    menuItems: [
      { id: 'overview', label: 'Overview', icon: '📊', path: '/dashboard/accounts' },
      { id: 'projects', label: 'Projects', icon: '⚙️', path: '/dashboard/accounts/projects' },
      { id: 'transactions', label: 'Transactions', icon: '🔄', path: '/dashboard/accounts/transactions' },
      { id: 'invoices', label: 'Invoices', icon: '📃', path: '/dashboard/accounts/invoices' },
      { id: 'expenses', label: 'Expenses', icon: '💰', path: '/dashboard/accounts/expenses' },
      { id: 'budgets', label: 'Budgets', icon: '📈', path: '/dashboard/accounts/budgets' },
      { id: 'payroll', label: 'Payroll', icon: '💵', path: '/dashboard/accounts/payroll' },
      { id: 'reports', label: 'Reports', icon: '📊', path: '/dashboard/accounts/reports' },
      { id: 'settings', label: 'Settings', icon: '⚙️', path: '/dashboard/accounts/settings' },
      // { id: 'activity', label: 'Activity', icon: '📱', path: '/dashboard/accounts/activity' },

    ],
  },
  operations: {
    color: '#9333EA',
    menuItems: [
      { label: 'Overview', path: '/dashboard/operations', icon: '⚙️' },
      { label: 'Projects', path: '/dashboard/operations/projects', icon: '📋', badge: 'Urgent' },
      { label: 'Resources', path: '/dashboard/operations/resources', icon: '🔧' },
      { label: 'Schedule', path: '/dashboard/operations/schedule', icon: '📅' },
      { label: 'Reports', path: '/dashboard/operations/reports', icon: '📊' }
    ]
  }
};
