import { useEffect } from 'react';
import { useUser } from '../../contexts/UserContext.jsx';
import useStore from '../../store/useStore';

// This component syncs the user data between the UserContext and the global store
const UserDataSync = () => {
  const { user } = useUser();
  const setUser = useStore((state) => state.setUser);
  const clearUser = useStore((state) => state.clearUser);

  // Sync user data from context to store
  useEffect(() => {
    console.log('UserDataSync: Syncing user data to store:', user);
    if (user) {
      setUser(user);
    } else {
      clearUser();
    }
  }, [user, setUser, clearUser]);

  // This component doesn't render anything
  return null;
};

export default UserDataSync;
