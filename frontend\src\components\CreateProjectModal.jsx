import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import TeamMembersModal from './TeamMembersModal'; // Import the TeamMembersModal

const CreateProjectModal = ({ isOpen, onClose, onCreate, selectedMembers = [], teamMembers = [], onSelectTeamMembers }) => {
  const [formData, setFormData] = useState({
    name: '',
    client: '',
    startDate: '',
    endDate: '',
    contractValue: 0,
    paymentSchedule: [{ date: '', amount: 0 }],
  });
  const [isTeamMembersModalOpen, setIsTeamMembersModalOpen] = useState(false); // State for team members modal
  const [selectedTeamMembers, setSelectedTeamMembers] = useState([]); // Selected team members
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handlePaymentScheduleChange = (index, field, value) => {
    const updatedSchedule = [...formData.paymentSchedule];
    updatedSchedule[index][field] = value;
    setFormData((prev) => ({ ...prev, paymentSchedule: updatedSchedule }));
  };

  const addPaymentMilestone = () => {
    setFormData((prev) => ({
      ...prev,
      paymentSchedule: [...prev.paymentSchedule, { date: '', amount: 0 }],
    }));
  };

  const handleTeamMemberSelection = (selectedMembers) => {
    setSelectedTeamMembers(selectedMembers); // Update selected team members
    setIsTeamMembersModalOpen(false); // Close the team members modal
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const projectData = {
      ...formData,
      teamMembers: selectedMembers, // Use the renamed prop here
    };
    onCreate(projectData);
  };

  return (
    <>
      <Dialog open={isOpen} onClose={onClose} className="relative z-50">
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-3xl bg-white rounded-xl p-6">
            <Dialog.Title className="text-xl font-bold mb-4">New Project</Dialog.Title>
            <form className="space-y-4" onSubmit={handleSubmit}>
              <div>
                <label className="block text-sm font-medium mb-1">Project Name</label>
                <input
                  name="name"
                  type="text"
                  className="w-full px-3 py-2 border rounded-lg"
                  placeholder="Enter project name"
                  required
                  onChange={handleChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Client</label>
                <input
                  name="client"
                  type="text"
                  className="w-full px-3 py-2 border rounded-lg"
                  placeholder="Enter client name"
                  required
                  onChange={handleChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Start Date</label>
                <input
                  name="startDate"
                  type="date"
                  className="w-full px-3 py-2 border rounded-lg"
                  required
                  onChange={handleChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">End Date</label>
                <input
                  name="endDate"
                  type="date"
                  className="w-full px-3 py-2 border rounded-lg"
                  required
                  onChange={handleChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Contract Value</label>
                <input
                  name="contractValue"
                  type="number"
                  className="w-full px-3 py-2 border rounded-lg"
                  placeholder="0.00"
                  required
                  onChange={handleChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Payment Schedule</label>
                {formData.paymentSchedule.map((milestone, index) => (
                  <div key={index} className="flex gap-4 mb-2">
                    <input
                      type="date"
                      value={milestone.date}
                      onChange={(e) => handlePaymentScheduleChange(index, 'date', e.target.value)}
                      className="w-full px-3 py-2 border rounded-lg"
                      required
                    />
                    <input
                      type="number"
                      value={milestone.amount}
                      onChange={(e) => handlePaymentScheduleChange(index, 'amount', e.target.value)}
                      className="w-full px-3 py-2 border rounded-lg"
                      placeholder="Amount"
                      required
                    />
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addPaymentMilestone}
                  className="text-teal-700 hover:underline"
                >
                  Add Payment Milestone
                </button>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Team Members</label>
                <button
                  type="button"
                  onClick={() => setIsTeamMembersModalOpen(true)} // Open team members modal
                  className="bg-teal-700 text-white px-4 py-2 rounded-lg hover:bg-teal-800"
                >
                  Fetch Team Members
                </button>
                <div className="mt-2">
                  {selectedMembers.length > 0 ? (
                    selectedMembers.map((member) => (
                      <div key={member._id} className="flex items-center">
                        <span>{`${member.firstName} ${member.lastName} (${member.department})`}</span>
                      </div>
                    ))
                  ) : (
                    <p>No team members selected.</p>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
                  onClick={onClose}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-teal-700 text-white rounded-lg hover:bg-teal-800"
                >
                  Create Project
                </button>
              </div>
            </form>
          </Dialog.Panel>
        </div>
      </Dialog>

      {/* Team Members Modal */}
      <TeamMembersModal
        isOpen={isTeamMembersModalOpen}
        onClose={() => setIsTeamMembersModalOpen(false)}
        onSelect={handleTeamMemberSelection}
      />
    </>
  );
};

export default CreateProjectModal;