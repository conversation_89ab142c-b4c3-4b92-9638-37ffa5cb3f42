import express, { Express } from 'express';
import cors from 'cors';
import morgan from 'morgan';
import config from './config';
import connectDB from './config/database';
import { conditionalAuth } from './middleware/auth.middleware';
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import transactionRoutes from './routes/transaction.routes';
import statementRoutes from './routes/statement.routes';
import reconciliationRoutes from './routes/reconciliation.routes';
import healthRoutes from './routes/health.routes';
import fileRoutes from './routes/file.routes';

// Initialize express app
const app: Express = express();

// Connect to MongoDB
connectDB();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configure CORS for development
app.use(cors({
  origin: 'http://localhost:5173', // Specify the exact frontend origin
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With']
}));

// Logging middleware
app.use(morgan('dev'));

// Apply conditional auth middleware to all routes
app.use(conditionalAuth);

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/statements', statementRoutes);
app.use('/api/reconciliations', reconciliationRoutes);
app.use('/api/health', healthRoutes);
app.use('/api/files', fileRoutes);

// Start the server
const PORT = config.port;
app.listen(PORT, () => {
  console.log(`Server running in ${config.nodeEnv} mode on port ${PORT}`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  process.exit(1);
});
