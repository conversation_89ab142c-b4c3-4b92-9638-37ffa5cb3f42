import React from 'react';
import { FiCheckCircle, FiAlertCircle, FiClock } from 'react-icons/fi';

/**
 * ProgressLoader component for displaying loading progress with percentage
 * 
 * @param {Object} props - Component props
 * @param {string} props.status - Current status ('processing', 'completed', 'failed')
 * @param {number} props.progress - Progress percentage (0-100)
 * @param {string} props.text - Text to display below the progress bar
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showPercentage - Whether to show percentage text
 * @param {Function} props.onRetry - Function to call when retry button is clicked
 */
const ProgressLoader = ({ 
  status = 'processing', 
  progress = 0, 
  text = 'Loading...', 
  className = '',
  showPercentage = true,
  onRetry = null
}) => {
  // Determine color based on status
  let barColor = 'bg-indigo-600';
  let textColor = 'text-indigo-700';
  let icon = <FiClock className="h-5 w-5 text-indigo-500" />;
  
  if (status === 'completed') {
    barColor = 'bg-green-600';
    textColor = 'text-green-700';
    icon = <FiCheckCircle className="h-5 w-5 text-green-500" />;
  } else if (status === 'failed') {
    barColor = 'bg-red-600';
    textColor = 'text-red-700';
    icon = <FiAlertCircle className="h-5 w-5 text-red-500" />;
  }

  // Calculate progress percentage (ensure it's between 0-100)
  const progressValue = Math.max(0, Math.min(100, progress));
  
  // For processing status with 0 progress, show indeterminate progress bar
  const isIndeterminate = status === 'processing' && progressValue === 0;
  
  return (
    <div className={`flex flex-col items-center ${className}`}>
      {/* Status icon */}
      <div className="mb-2">
        {icon}
      </div>
      
      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2 relative overflow-hidden">
        <div 
          className={`h-full rounded-full ${barColor} ${isIndeterminate ? 'animate-progress-indeterminate' : ''}`}
          style={{ 
            width: isIndeterminate ? '100%' : `${progressValue}%`,
            backgroundSize: isIndeterminate ? '200% 100%' : 'initial'
          }}
        ></div>
      </div>
      
      {/* Progress text */}
      <div className="flex items-center justify-between w-full">
        <p className={`text-sm font-medium ${textColor}`}>{text}</p>
        {showPercentage && (
          <p className="text-sm font-medium text-gray-600">
            {isIndeterminate ? '' : `${Math.round(progressValue)}%`}
          </p>
        )}
      </div>
      
      {/* Retry button for failed status */}
      {status === 'failed' && onRetry && (
        <button 
          onClick={onRetry}
          className="mt-3 px-3 py-1 bg-red-100 text-red-700 text-sm rounded hover:bg-red-200 flex items-center"
        >
          <FiAlertCircle className="mr-1 h-3 w-3" />
          Retry
        </button>
      )}
    </div>
  );
};

export default ProgressLoader;
