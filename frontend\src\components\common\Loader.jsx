import React from 'react';

const Loader = ({ text = 'Loading...', fullScreen = false }) => {
  const loaderContent = (
    <div className="flex flex-col items-center justify-center">
      <div className="relative">
        <div className="h-16 w-16 rounded-full border-t-4 border-b-4 border-[#412D6C]"></div>
        <div className="h-16 w-16 rounded-full border-t-4 border-b-4 border-[#412D6C]/30 animate-spin absolute inset-0"></div>
      </div>
      <p className="mt-4 text-gray-600 font-medium">{text}</p>
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50">
        {loaderContent}
      </div>
    );
  }

  return loaderContent;
};

export default Loader;
