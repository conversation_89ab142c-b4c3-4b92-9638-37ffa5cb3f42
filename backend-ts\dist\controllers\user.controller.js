"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteUser = exports.updateUser = exports.getCurrentUser = void 0;
const user_model_1 = __importDefault(require("../models/user.model"));
/**
 * Get current user
 * @route GET /api/users/me
 */
const getCurrentUser = async (req, res) => {
    try {
        const user = await user_model_1.default.findById(req.user.sub).select('-password');
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }
        res.status(200).json({
            status: true,
            data: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
            },
        });
    }
    catch (error) {
        console.error('Error getting current user:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getCurrentUser = getCurrentUser;
/**
 * Update user
 * @route PUT /api/users/me
 */
const updateUser = async (req, res) => {
    try {
        const { firstName, lastName, email } = req.body;
        // Find user by ID
        const user = await user_model_1.default.findById(req.user.sub);
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }
        // Update user fields
        if (firstName)
            user.firstName = firstName;
        if (lastName)
            user.lastName = lastName;
        if (email)
            user.email = email;
        // Save updated user
        await user.save();
        res.status(200).json({
            status: true,
            message: 'User updated successfully',
            data: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
            },
        });
    }
    catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.updateUser = updateUser;
/**
 * Delete user
 * @route DELETE /api/users/me
 */
const deleteUser = async (req, res) => {
    try {
        const user = await user_model_1.default.findByIdAndDelete(req.user.sub);
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }
        res.status(200).json({
            status: true,
            message: 'User deleted successfully',
        });
    }
    catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.deleteUser = deleteUser;
