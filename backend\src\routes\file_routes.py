"""
File routes for the Bank Reconciliation API.
"""

from flask import Blueprint
from controllers.file_controller import upload_file, preview_file, download_file, analyze_file
from middleware.auth_middleware import auth_required

# Create blueprint
file_bp = Blueprint('file', __name__)

# @route   POST /api/files/upload
# @desc    Upload a file
# @access  Private
file_bp.route('/upload', methods=['POST'])(auth_required(upload_file))

# @route   GET /api/files/preview/:filePath
# @desc    Preview file content
# @access  Private
file_bp.route('/preview/<path:file_path>', methods=['GET'])(auth_required(preview_file))

# @route   GET /api/files/download/:filePath
# @desc    Download file
# @access  Private
file_bp.route('/download/<path:file_path>', methods=['GET'])(auth_required(download_file))

# @route   POST /api/files/analyze
# @desc    Analyze file content and extract insights
# @access  Private
file_bp.route('/analyze', methods=['POST'])(auth_required(analyze_file))
