import React from 'react';

/**
 * A reusable clickable card component with hover effects
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Card content
 * @param {Function} props.onClick - Click handler function
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement} Clickable card component
 */
const ClickableCard = ({ children, onClick, className = '' }) => {
  return (
    <div 
      className={`transition-all duration-200 hover:shadow-md cursor-pointer ${className}`}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export default ClickableCard;
