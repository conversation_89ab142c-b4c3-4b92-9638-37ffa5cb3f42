import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-indigo-800 text-white py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">Bank Reconciliation</h3>
            <p className="text-indigo-200">
              A powerful tool for reconciling bank statements and managing financial transactions.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/dashboard" className="text-indigo-200 hover:text-white transition-colors">
                  Dashboard
                </Link>
              </li>
              <li>
                <Link to="/statements" className="text-indigo-200 hover:text-white transition-colors">
                  Statements
                </Link>
              </li>
              <li>
                <Link to="/transactions" className="text-indigo-200 hover:text-white transition-colors">
                  Transactions
                </Link>
              </li>
              <li>
                <Link to="/reconciliations" className="text-indigo-200 hover:text-white transition-colors">
                  Reconciliations
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <p className="text-indigo-200">
              Need help with your reconciliation?
            </p>
            <p className="text-indigo-200">
              Email: <EMAIL>
            </p>
          </div>
        </div>
        <div className="border-t border-indigo-700 mt-8 pt-6 text-center text-indigo-300">
          <p>&copy; {currentYear} Bank Reconciliation. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
