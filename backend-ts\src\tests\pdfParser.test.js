/**
 * Test script for PDF parsing functionality
 * 
 * This script tests the enhanced PDF parsing functionality for bank statements
 * with the specific format containing date patterns like "03-MAR-25".
 * 
 * To run this test:
 * 1. Place a sample statement.pdf file in the same directory as this script
 * 2. Run: node pdfParser.test.js
 */

const fs = require('fs');
const path = require('path');
const pdfParse = require('pdf-parse');

// Sample PDF content for testing
const samplePdfText = `RANDALPHA MICROFINANCE BANK
123 Main Street, Lagos, Nigeria

ACCOUNT STATEMENT

03-MAR-25
01.03.2025 RANDALPHA MFB/OLALERE IDOWU MARY/'090496250301121728413602636378
03-MAR-25
960.000
65,897,998.14

05-MAR-25
03.03.2025 RANDALPHA MFB/JOHN DOE/'090496250301121728413602636379
05-MAR-25
1,200.000
0
67,097,998.14

07-MAR-25
05.03.2025 RANDALPHA MFB/JANE SMITH/'090496250301121728413602636380
07-MAR-25
500.000
0
67,597,998.14`;

/**
 * Parse bank statement data from text using the new approach
 */
function parseStatementData(text) {
  const lines = text.split('\n').filter(line => line.trim() !== '');
  
  // Parse rows using known date pattern at start
  const entries = [];
  let current = null;

  for (const line of lines) {
    const dateMatch = line.match(/^\d{2}-[A-Z]{3}-\d{2}/); // e.g., 03-MAR-25

    if (dateMatch) {
      // New row starting with EntryDate
      if (current) entries.push(current);

      const [entryDate, ...rest] = line.split(/\s{2,}|\t/);
      current = {
        entryDate,
        details: rest.join(' '),
        valueDate: '',
        debit: '',
        credit: '',
        balance: ''
      };
    } else if (current) {
      // Handle continuation lines or table columns
      const parts = line.split(/\s{2,}|\t/);
      if (parts.length >= 4) {
        [current.valueDate, current.debit, current.credit, current.balance] = parts;
      } else {
        // If continuation line, append to details
        current.details += ' ' + line;
      }
    }
  }

  // Don't forget to add the last entry
  if (current) entries.push(current);

  return entries;
}

/**
 * Test the parsing with sample text
 */
function testWithSampleText() {
  console.log('Testing with sample text...');
  const entries = parseStatementData(samplePdfText);
  console.log('Parsed entries:', JSON.stringify(entries, null, 2));
  console.log(`Found ${entries.length} entries`);
}

/**
 * Test with a real PDF file if available
 */
async function testWithRealPdf() {
  const pdfPath = path.join(__dirname, 'statement.pdf');
  
  try {
    if (fs.existsSync(pdfPath)) {
      console.log('Testing with real PDF file...');
      const dataBuffer = fs.readFileSync(pdfPath);
      const data = await pdfParse(dataBuffer);
      
      const entries = parseStatementData(data.text);
      console.log('Parsed entries from PDF:', JSON.stringify(entries, null, 2));
      console.log(`Found ${entries.length} entries in the PDF`);
    } else {
      console.log('No statement.pdf file found for testing with real PDF');
    }
  } catch (error) {
    console.error('Error testing with real PDF:', error);
  }
}

/**
 * Run the tests
 */
async function runTests() {
  console.log('=== PDF Parser Test ===');
  testWithSampleText();
  await testWithRealPdf();
  console.log('=== Test Complete ===');
}

// Run the tests
runTests();
