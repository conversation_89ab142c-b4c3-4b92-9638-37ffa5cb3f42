import React from 'react';

/**
 * LoadingSpinner component
 * @param {Object} props - Component props
 * @param {string} props.size - Size of the spinner (sm, md, lg)
 * @param {string} props.color - Color of the spinner (default: indigo)
 * @param {string} props.className - Additional CSS classes
 */
const LoadingSpinner = ({ size = 'md', color = 'indigo', className = '' }) => {
  // Determine size class
  let sizeClass = 'h-8 w-8';
  if (size === 'sm') sizeClass = 'h-5 w-5';
  if (size === 'lg') sizeClass = 'h-12 w-12';

  // Determine color class
  let colorClass = 'border-indigo-600';
  if (color === 'blue') colorClass = 'border-blue-600';
  if (color === 'green') colorClass = 'border-green-600';
  if (color === 'red') colorClass = 'border-red-600';
  if (color === 'yellow') colorClass = 'border-yellow-600';
  if (color === 'gray') colorClass = 'border-gray-600';
  if (color === 'white') colorClass = 'border-white';

  return (
    <div className={`animate-spin rounded-full border-b-2 ${sizeClass} ${colorClass} ${className}`}></div>
  );
};

export default LoadingSpinner;
