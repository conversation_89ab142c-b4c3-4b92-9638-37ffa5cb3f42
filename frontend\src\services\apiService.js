import axios from 'axios';
import { API_BASE_URL, AMBASSADOR_ROUTES } from '../apiRoutes';
import { createAuthenticatedAxiosInstance, createPublicAxiosInstance } from './api';
import { shouldMakeApiCalls, getAuthToken } from '../utils/authUtils';

// Create authenticated axios instance
const api = createAuthenticatedAxiosInstance();

// Application service for ambassador program
export const applicationService = {
  // Get application status
  getApplicationStatus: async (userId, type) => {
    try {
      // Don't make API calls if we're on the login page
      if (!shouldMakeApiCalls()) {
        console.log('On login page, skipping application status API call');
        return {
          status: false,
          error: 'On login page, API calls skipped'
        };
      }

      // Ensure auth token is included
      const token = getAuthToken();
      if (!token) {
        return {
          status: false,
          error: 'Authentication required. Please log in to check your application status.'
        };
      }

      // Use the authenticated axios instance (no need to add headers manually)
      const response = await api.get(`/applications/status/${userId}/${type}`);

      return {
        status: true,
        data: response.data.data
      };
    } catch (error) {
      console.error('Error getting application status:', error);

      // If 404, it means no application found, which is not an error for the user
      if (error.response?.status === 404) {
        return {
          status: false,
          notFound: true
        };
      }

      // Handle other errors
      return {
        status: false,
        error: error.response?.data?.meta?.error ||
               error.response?.data?.message ||
               'Failed to get application status'
      };
    }
  },

  // Submit ambassador application
  submitAmbassadorApplication: async (applicationData) => {
    try {
      // Use the public API - no authentication required
      const publicApi = createPublicAxiosInstance();
      const response = await publicApi.post('/applications/ambassador', applicationData);

      return {
        status: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error submitting ambassador application:', error);
      if (error.response?.data?.meta?.error) {
        throw new Error(error.response.data.meta.error);
      } else {
        throw new Error(error.message || 'Failed to submit application. Please try again.');
      }
    }
  },

  // Register student for ambassador program
  registerAmbassadorStudent: async (registrationData) => {
    try {
      // Use the public API - no authentication required
      const publicApi = createPublicAxiosInstance();
      const response = await publicApi.post('/auth/student-registration', registrationData);

      return {
        status: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error registering ambassador student:', error);
      if (error.response?.data?.meta?.error) {
        throw new Error(error.response.data.meta.error);
      } else {
        throw new Error(error.message || 'Failed to register. Please try again.');
      }
    }
  }
};

// Ambassador service for student ambassadors
export const ambassadorService = {
  // Get ambassador application status
  getAmbassadorStatus: async (userId) => {
    try {
      // If userId is not provided, try to get it from localStorage
      const userIdToUse = userId || localStorage.getItem("userId");
      console.log('getAmbassadorStatus called with userId:', userIdToUse);
      console.log('Current URL:', window.location.href);
      console.log('Current path:', window.location.pathname);

      // Don't make API calls if we're on the login page
      if (!shouldMakeApiCalls()) {
        console.log('On login page, skipping ambassador status API call');
        return {
          status: false,
          error: 'On login page, API calls skipped'
        };
      }

      // Ensure auth token is included
      const token = getAuthToken();
      if (!token) {
        console.log('No auth token found');
        return {
          status: false,
          error: 'Authentication required. Please log in to check your ambassador status.'
        };
      }

      console.log('Using token:', token);

      // If no userId is available, return an error
      if (!userIdToUse) {
        console.log('No user ID available');
        return {
          status: false,
          error: 'User ID is required to check ambassador status.'
        };
      }

      try {
        // Use the correct API endpoint from AMBASSADOR_ROUTES
        const statusUrl = AMBASSADOR_ROUTES.GET_STATUS(userIdToUse);
        console.log('Using ambassador status URL:', statusUrl);

        // Use the authenticated axios instance
        const api = createAuthenticatedAxiosInstance();
        console.log('Making API request to:', statusUrl);
        console.log('AMBASSADOR_ROUTES:', JSON.stringify(AMBASSADOR_ROUTES));

        // Make the API request
        const response = await api.get(statusUrl);
        console.log('Ambassador status API response:', response.data);
        return {
          status: true,
          data: response.data.data
        };
      } catch (apiError) {
        console.error('API error:', apiError);
        console.error('API error details:', apiError.response?.data || 'No response data');

        // If 404, it means no application found, which is not an error for the user
        if (apiError.response?.status === 404) {
          return {
            status: false,
            notFound: true
          };
        }

        // Return the error
        return {
          status: false,
          error: apiError.response?.data?.meta?.error ||
                 apiError.response?.data?.message ||
                 'Failed to get ambassador status'
        };
      }
    } catch (error) {
      console.error('Error getting ambassador status:', error);

      // If 404, it means no application found, which is not an error for the user
      if (error.response?.status === 404) {
        return {
          status: false,
          notFound: true
        };
      }

      // Handle other errors
      return {
        status: false,
        error: error.response?.data?.meta?.error ||
               error.response?.data?.message ||
               'Failed to get ambassador status'
      };
    }
  },

  // Get ambassador resources
  getAmbassadorResources: async (userId) => {
    try {
      console.log('getAmbassadorResources called with userId:', userId);

      // Don't make API calls if we're on the login page
      if (!shouldMakeApiCalls()) {
        console.log('On login page, skipping ambassador resources API call');
        return {
          status: false,
          error: 'On login page, API calls skipped'
        };
      }

      try {
        // Use the correct API endpoint from AMBASSADOR_ROUTES
        const resourcesUrl = AMBASSADOR_ROUTES.GET_RESOURCES(userId);
        console.log('Using ambassador resources URL:', resourcesUrl);

        // Use the authenticated axios instance
        const api = createAuthenticatedAxiosInstance();
        const response = await api.get(resourcesUrl);

        console.log('Ambassador resources API response:', response.data);

        return {
          status: true,
          data: response.data.data
        };
      } catch (apiError) {
        console.error('API error:', apiError);
        console.error('API error details:', apiError.response?.data || 'No response data');

        // If API fails, use mock data for now
        console.log('API failed, returning mock ambassador resources data');
        return {
          status: true,
          data: [
            { id: 1, title: 'Ambassador Handbook', type: 'PDF', url: '#', description: 'Complete guide to being a successful ambassador' },
            { id: 2, title: 'Marketing Materials', type: 'ZIP', url: '#', description: 'Logos, banners, and social media templates' },
            { id: 3, title: 'Presentation Template', type: 'PPTX', url: '#', description: 'Official presentation template for events' }
          ]
        };
      }
    } catch (error) {
      console.error('Error getting ambassador resources:', error);
      return {
        status: false,
        error: error.response?.data?.meta?.error ||
               error.response?.data?.message ||
               'Failed to get ambassador resources'
      };
    }
  },

  // Get ambassador tasks
  getAmbassadorTasks: async (userId) => {
    try {
      console.log('getAmbassadorTasks called with userId:', userId);

      // Don't make API calls if we're on the login page
      if (!shouldMakeApiCalls()) {
        console.log('On login page, skipping ambassador tasks API call');
        return {
          status: false,
          error: 'On login page, API calls skipped'
        };
      }

      try {
        // Use the correct API endpoint from AMBASSADOR_ROUTES
        const tasksUrl = AMBASSADOR_ROUTES.GET_TASKS(userId);
        console.log('Using ambassador tasks URL:', tasksUrl);

        // Use the authenticated axios instance
        const api = createAuthenticatedAxiosInstance();
        const response = await api.get(tasksUrl);

        console.log('Ambassador tasks API response:', response.data);

        return {
          status: true,
          data: response.data.data
        };
      } catch (apiError) {
        console.error('API error:', apiError);
        console.error('API error details:', apiError.response?.data || 'No response data');

        // If API fails, use mock data for now
        console.log('API failed, returning mock ambassador tasks data');
        return {
          status: true,
          data: [
            { id: 1, title: 'Complete Onboarding', dueDate: '2023-05-15T23:59:59Z', status: 'Completed', points: 10 },
            { id: 2, title: 'Host Information Session', dueDate: '2023-06-01T23:59:59Z', status: 'In Progress', points: 25 },
            { id: 3, title: 'Social Media Campaign', dueDate: '2023-06-15T23:59:59Z', status: 'Not Started', points: 15 }
          ]
        };
      }
    } catch (error) {
      console.error('Error getting ambassador tasks:', error);
      return {
        status: false,
        error: error.response?.data?.meta?.error ||
               error.response?.data?.message ||
               'Failed to get ambassador tasks'
      };
    }
  },

  // Get ambassador events
  getAmbassadorEvents: async (userId) => {
    try {
      console.log('getAmbassadorEvents called with userId:', userId);

      // Don't make API calls if we're on the login page
      if (!shouldMakeApiCalls()) {
        console.log('On login page, skipping ambassador events API call');
        return {
          status: false,
          error: 'On login page, API calls skipped'
        };
      }

      try {
        // Use the correct API endpoint from AMBASSADOR_ROUTES
        const eventsUrl = AMBASSADOR_ROUTES.GET_EVENTS(userId);
        console.log('Using ambassador events URL:', eventsUrl);

        // Use the authenticated axios instance
        const api = createAuthenticatedAxiosInstance();
        const response = await api.get(eventsUrl);

        console.log('Ambassador events API response:', response.data);

        return {
          status: true,
          data: response.data.data
        };
      } catch (apiError) {
        console.error('API error:', apiError);
        console.error('API error details:', apiError.response?.data || 'No response data');

        // If API fails, use mock data for now
        console.log('API failed, returning mock ambassador events data');
        return {
          status: true,
          data: [
            { id: 1, title: 'Virtual Open House', date: '2023-05-20T18:00:00Z', location: 'Zoom', status: 'Upcoming' },
            { id: 2, title: 'Campus Recruitment Fair', date: '2023-06-05T10:00:00Z', location: 'Main Campus', status: 'Upcoming' },
            { id: 3, title: 'Ambassador Training Workshop', date: '2023-04-15T14:00:00Z', location: 'Online', status: 'Completed' }
          ]
        };
      }
    } catch (error) {
      console.error('Error getting ambassador events:', error);
      return {
        status: false,
        error: error.response?.data?.meta?.error ||
               error.response?.data?.message ||
               'Failed to get ambassador events'
      };
    }
  },

  // Get ambassador metrics
  getAmbassadorMetrics: async (userId) => {
    try {
      console.log('getAmbassadorMetrics called with userId:', userId);

      // Don't make API calls if we're on the login page
      if (!shouldMakeApiCalls()) {
        console.log('On login page, skipping ambassador metrics API call');
        return {
          status: false,
          error: 'On login page, API calls skipped'
        };
      }

      try {
        // Use the correct API endpoint from AMBASSADOR_ROUTES
        const metricsUrl = AMBASSADOR_ROUTES.GET_METRICS(userId);
        console.log('Using ambassador metrics URL:', metricsUrl);

        // Use the authenticated axios instance
        const api = createAuthenticatedAxiosInstance();
        const response = await api.get(metricsUrl);

        console.log('Ambassador metrics API response:', response.data);

        return {
          status: true,
          data: response.data.data
        };
      } catch (apiError) {
        console.error('API error:', apiError);
        console.error('API error details:', apiError.response?.data || 'No response data');

        // If API fails, use mock data for now
        console.log('API failed, returning mock ambassador metrics data');
        return {
          status: true,
          data: {
            totalPoints: 35,
            eventsHosted: 2,
            studentsReferred: 8,
            conversionRate: 75,
            rank: 5,
            level: 'Silver'
          }
        };
      }
    } catch (error) {
      console.error('Error getting ambassador metrics:', error);
      return {
        status: false,
        error: error.response?.data?.meta?.error ||
               error.response?.data?.message ||
               'Failed to get ambassador metrics'
      };
    }
  },

  // Submit ambassador task
  submitAmbassadorTask: async (taskData) => {
    try {
      console.log('submitAmbassadorTask called with data:', taskData);

      // Don't make API calls if we're on the login page
      if (!shouldMakeApiCalls()) {
        console.log('On login page, skipping ambassador task submission API call');
        return {
          status: false,
          error: 'On login page, API calls skipped'
        };
      }

      try {
        // Use the correct API endpoint from AMBASSADOR_ROUTES
        const submitUrl = AMBASSADOR_ROUTES.SUBMIT_TASK;
        console.log('Using ambassador task submission URL:', submitUrl);

        // Use the authenticated axios instance
        const api = createAuthenticatedAxiosInstance();
        const response = await api.post(submitUrl, taskData);

        console.log('Ambassador task submission API response:', response.data);

        return {
          status: true,
          data: response.data.data
        };
      } catch (apiError) {
        console.error('API error:', apiError);
        console.error('API error details:', apiError.response?.data || 'No response data');

        // If API fails, return a mock success response for now
        console.log('API failed, returning mock task submission response');
        return {
          status: true,
          data: {
            taskId: taskData.taskId,
            status: 'Submitted',
            submissionDate: new Date().toISOString()
          }
        };
      }
    } catch (error) {
      console.error('Error submitting ambassador task:', error);
      return {
        status: false,
        error: error.response?.data?.meta?.error ||
               error.response?.data?.message ||
               'Failed to submit ambassador task'
      };
    }
  },

  // Get ambassador by MongoDB ID
  getAmbassadorById: async (id) => {
    try {
      console.log('getAmbassadorById called with id:', id);
      console.log('Current URL:', window.location.href);
      console.log('Current path:', window.location.pathname);
      console.log('AuthToken in localStorage:', localStorage.getItem("authToken"));

      // Don't make API calls if we're on the login page
      if (!shouldMakeApiCalls()) {
        console.log('On login page, skipping ambassador by ID API call');
        return {
          status: false,
          error: 'On login page, API calls skipped'
        };
      }

      // Ensure auth token is included
      const token = getAuthToken();
      if (!token) {
        console.log('No auth token found');
        return {
          status: false,
          error: 'Authentication required. Please log in to view ambassador details.'
        };
      }

      try {
        // Use the correct API endpoint from AMBASSADOR_ROUTES
        const url = AMBASSADOR_ROUTES.GET_BY_ID(id);
        console.log('Using ambassador by ID URL:', url);
        console.log('AMBASSADOR_ROUTES:', JSON.stringify(AMBASSADOR_ROUTES));

        // Use the authenticated axios instance
        const api = createAuthenticatedAxiosInstance();
        console.log('Making API request to:', url);

        // Make the API request
        const response = await api.get(url);
        console.log('Ambassador by ID API response:', response.data);
        return {
          status: true,
          data: response.data.data
        };
      } catch (apiError) {
        console.error('API error:', apiError);
        console.error('API error details:', apiError.response?.data || 'No response data');

        // If 404, it means no ambassador found, which is not an error for the user
        if (apiError.response?.status === 404) {
          return {
            status: false,
            notFound: true
          };
        }

        // Return the error
        return {
          status: false,
          error: apiError.response?.data?.meta?.error ||
                 apiError.response?.data?.message ||
                 'Failed to get ambassador details'
        };
      }
    } catch (error) {
      console.error('Error getting ambassador by ID:', error);
      return {
        status: false,
        error: error.response?.data?.meta?.error ||
               error.response?.data?.message ||
               'Failed to get ambassador details'
      };
    }
  }
};

export default api;
