"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionStatus = exports.TransactionSource = void 0;
const mongoose_1 = __importStar(require("mongoose"));
// Transaction source enum
var TransactionSource;
(function (TransactionSource) {
    TransactionSource["BANK"] = "BANK";
    TransactionSource["INTERNAL"] = "INTERNAL";
})(TransactionSource || (exports.TransactionSource = TransactionSource = {}));
// Transaction status enum
var TransactionStatus;
(function (TransactionStatus) {
    TransactionStatus["UNMATCHED"] = "UNMATCHED";
    TransactionStatus["MATCHED"] = "MATCHED";
    TransactionStatus["RECONCILED"] = "RECONCILED";
})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));
// Transaction schema
const transactionSchema = new mongoose_1.Schema({
    date: {
        type: Date,
        required: [true, 'Transaction date is required'],
    },
    description: {
        type: String,
        required: [true, 'Transaction description is required'],
        trim: true,
    },
    amount: {
        type: Number,
        required: [true, 'Transaction amount is required'],
    },
    reference: {
        type: String,
        trim: true,
    },
    source: {
        type: String,
        enum: Object.values(TransactionSource),
        required: [true, 'Transaction source is required'],
    },
    status: {
        type: String,
        enum: Object.values(TransactionStatus),
        default: TransactionStatus.UNMATCHED,
    },
    statementId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Statement',
    },
    matchedWith: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Transaction',
    },
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User ID is required'],
    },
}, {
    timestamps: true,
});
// Create and export Transaction model
exports.default = mongoose_1.default.model('Transaction', transactionSchema);
