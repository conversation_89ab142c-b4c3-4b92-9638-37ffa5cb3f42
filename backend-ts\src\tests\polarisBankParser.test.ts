/**
 * Tests for the Polaris Bank Parser
 */

import { extractPolarisBankTable, extractPolarisBankInfo } from '../utils/polarisBankParser.js';
import { Table, BankStatementInfo } from '../utils/pdfTableExtractor.js';

describe('Polaris Bank Parser', () => {
  // Sample text from a Polaris Bank statement
  const sampleText = `
Polaris Bank Limited
OGBOMOSHO
OPBAPTIST MEDICAL CENTRE
OYO ILORIN ROAD, OGBOMOSHO
OYO , OYO

RANDALPHA MICROFINANCE BANK NIG LT
BEHIND N.B.T.S., P.O. BOX 1921
, OGBOMOSHO
OYO

01-MAR-25 to 31-MAR-25
**********
POLARIS XCEL CORPORATE
ACCOUNT - NGN
SORT CODE:- ********

Report By:-Abimbola
Oluwatosin Akinduumi
Staff No:-06336
Branch:-Ogbomosho

EntryDate                  Details                                ValueDate      Debit        Credit      Balance
01-MAR-
25      Balance B/F.......                                                                              65,898,958.14

03-MAR-  01.03.2025 RANDALPHA MFB/OLALERE IDOWU                  03-MAR-
25      MARY/'090496250301121728413602636378                     25             960.00       0          65,897,998.14

03-MAR-  01.03.2025 RANDALPHA                                    03-MAR-
25      MFB/ALABI:AKINLOYE/'090496250301135827287356351730      25             1,200.00     0          65,896,798.14

03-MAR-  01.03.2025 RANDALPHA MFB/IYANDA MARY                    03-MAR-
25      TEMITOPE/'090496250301185806083135063265                 25             2,000.00     0          65,894,798.14

03-MAR-  01.03.2025 RANDALPHA MFB/ADETUNJI MATTHEW               03-MAR-
25      SEGUN/'090496250301152550624131645648                    25             2,000.00     0          65,892,798.14

03-MAR-  01.03.2025 RANDALPHA MFB/OLADEJO OLAGOKE                03-MAR-
25      PETER/'090496250301094908077580018638                    25             2,000.00     0          65,890,798.14

03-MAR-  01.03.2025 RANDALPHA                                    03-MAR-
25      MFB/AJAGBE:ADEWALE/'090496250301140432433243405437      25             3,200.00     0          65,887,598.14

03-MAR-  01.03.2025 RANDALPHA MFB/OGUNWOLE JANET                 03-MAR-
25      ABIDEMI/'090496250301073451656812584025                  25             3,300.00     0          65,884,298.14

03-MAR-  01.03.2025 RANDALPHA                                    03-MAR-
25      MFB/AJAGBE:ADEWALE/'090496250301111214761460360235      25             4,000.00     0          65,880,298.14
`;

  describe('extractPolarisBankTable', () => {
    it('should extract table data from Polaris Bank statement text', () => {
      const result = extractPolarisBankTable(sampleText);
      
      // Verify the result is not null
      expect(result).not.toBeNull();
      
      if (result) {
        // Verify headers
        expect(result.headers).toEqual(['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance']);
        
        // Verify we have the expected number of rows
        expect(result.rows.length).toBeGreaterThanOrEqual(9); // 8 transactions + 1 balance B/F
        
        // Verify the balance B/F entry
        const balanceEntry = result.rows.find(row => row.Details && row.Details.includes('Balance B/F'));
        expect(balanceEntry).toBeDefined();
        if (balanceEntry) {
          expect(balanceEntry.Balance).toBe('65,898,958.14');
        }
        
        // Verify a transaction entry
        const transaction = result.rows.find(row => 
          row.Details && row.Details.includes('RANDALPHA MFB/OLALERE IDOWU')
        );
        expect(transaction).toBeDefined();
        if (transaction) {
          expect(transaction.EntryDate).toBe('03-MAR-25');
          expect(transaction.ValueDate).toBe('03-MAR-25');
          expect(transaction.Debit).toBe('960.00');
          expect(transaction.Credit).toBe('0');
          expect(transaction.Balance).toBe('65,897,998.14');
        }
      }
    });
  });

  describe('extractPolarisBankInfo', () => {
    it('should extract bank information from Polaris Bank statement text', () => {
      const result = extractPolarisBankInfo(sampleText);
      
      // Verify bank name
      expect(result.bankName).toBe('Polaris Bank Limited');
      
      // Verify date range
      expect(result.dateRange).toBe('01-MAR-25 to 31-MAR-25');
      
      // Verify account type
      expect(result.accountType).toContain('ACCOUNT - NGN');
      
      // Verify sort code
      expect(result.sortCode).toContain('SORT CODE');
      
      // Verify branch
      expect(result.branch).toContain('Branch:-Ogbomosho');
    });
  });
});
