/**
 * Utility functions for styling components
 */

/**
 * Adds cursor pointer class to a className string if it doesn't already have it
 * @param {string} className - The original className string
 * @returns {string} - The className string with cursor-pointer added
 */
export const addCursorPointer = (className = '') => {
  return className.includes('cursor-pointer') 
    ? className 
    : `${className} cursor-pointer`.trim();
};

/**
 * Creates a className string for a button with standard styling
 * @param {string} variant - The button variant (primary, secondary, danger, etc.)
 * @param {string} additionalClasses - Additional classes to add
 * @returns {string} - The complete className string
 */
export const buttonClass = (variant = 'primary', additionalClasses = '') => {
  const baseClasses = 'px-4 py-2 rounded transition-colors cursor-pointer';
  
  let variantClasses = '';
  switch (variant) {
    case 'primary':
      variantClasses = 'bg-blue-600 text-white hover:bg-blue-700';
      break;
    case 'secondary':
      variantClasses = 'bg-gray-200 text-gray-800 hover:bg-gray-300';
      break;
    case 'danger':
      variantClasses = 'bg-red-600 text-white hover:bg-red-700';
      break;
    case 'success':
      variantClasses = 'bg-green-600 text-white hover:bg-green-700';
      break;
    case 'warning':
      variantClasses = 'bg-yellow-500 text-white hover:bg-yellow-600';
      break;
    default:
      variantClasses = 'bg-blue-600 text-white hover:bg-blue-700';
  }
  
  return `${baseClasses} ${variantClasses} ${additionalClasses}`.trim();
};

/**
 * Creates a className string for a select input with standard styling
 * @param {boolean} isError - Whether the input has an error
 * @param {string} additionalClasses - Additional classes to add
 * @returns {string} - The complete className string
 */
export const selectClass = (isError = false, additionalClasses = '') => {
  const baseClasses = 'px-3 py-2 border rounded-md focus:outline-none focus:ring-2 cursor-pointer';
  const stateClasses = isError 
    ? 'border-red-300 focus:ring-red-500' 
    : 'border-gray-300 focus:ring-blue-500';
  
  return `${baseClasses} ${stateClasses} ${additionalClasses}`.trim();
};

/**
 * Creates a className string for a clickable card with hover effects
 * @param {string} additionalClasses - Additional classes to add
 * @returns {string} - The complete className string
 */
export const clickableCardClass = (additionalClasses = '') => {
  const baseClasses = 'transition-all duration-200 hover:shadow-md cursor-pointer';
  
  return `${baseClasses} ${additionalClasses}`.trim();
};
