"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const statement_controller_1 = require("../controllers/statement.controller");
const router = express_1.default.Router();
// @route   GET /api/statements
// @desc    Get all statements
// @access  Private
router.get('/', statement_controller_1.getAllStatements);
// @route   GET /api/statements/:id
// @desc    Get statement by ID
// @access  Private
router.get('/:id', statement_controller_1.getStatementById);
// @route   DELETE /api/statements/:id
// @desc    Delete statement
// @access  Private
router.delete('/:id', statement_controller_1.deleteStatement);
// @route   POST /api/statements/upload
// @desc    Upload statement
// @access  Private
router.post('/upload', statement_controller_1.uploadStatement);
// @route   POST /api/statements/parse
// @desc    Parse statement file
// @access  Private
router.post('/parse', statement_controller_1.parseStatementFile);
// @route   GET /api/statements/:id/preview
// @desc    Get statement preview
// @access  Private
router.get('/:id/preview', statement_controller_1.getStatementPreview);
// @route   GET /api/statements/:id/download
// @desc    Download statement file
// @access  Private
router.get('/:id/download', statement_controller_1.downloadStatementFile);
exports.default = router;
