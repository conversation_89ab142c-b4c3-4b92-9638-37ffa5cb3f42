/**
 * Centralized API Service
 *
 * This file provides a centralized way to make API calls using the routes defined in apiRoutes.js
 */

import axios from 'axios';
import {
  API_BASE_URL,
  AUTH_ROUTES,
  USER_ROUTES,
  BANK_ROUTES,
  STATEMENT_ROUTES,
  FILE_ROUTES,
  TRANSACTION_ROUTES,
  RECONCILIATION_ROUTES,
  ANALYTICS_ROUTES,
  DEV_ROUTES
} from '../apiRoutes';
import { shouldMakeApiCalls, isLoginPage, isDashboardPage } from '../utils/authUtils';

// Create a function to get a configured axios instance with authentication
export const createAuthenticatedAxiosInstance = (baseURL = API_BASE_URL, contentType = 'application/json') => {
  const instance = axios.create({
    baseURL,
    headers: {
      'Content-Type': contentType,
    },
    // Don't use withCredentials when using Bearer token auth
    withCredentials: false,
  });

  // Add auth token to requests if available
  instance.interceptors.request.use(config => {
    // Don't make API calls if we're on the login page
    if (!shouldMakeApiCalls()) {
      console.log('On login page, cancelling API request to:', config.url);
      // Cancel the request
      const error = new Error('API call cancelled - on login page');
      error.isLoginPageCancel = true;
      return Promise.reject(error);
    }

    const token = localStorage.getItem('authToken');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // No need to add CORS headers in the client - these are set by the server

    return config;
  });

  return instance;
};

// Create a function to get a configured axios instance without authentication
export const createPublicAxiosInstance = (baseURL = API_BASE_URL, contentType = 'application/json') => {
  return axios.create({
    baseURL,
    headers: {
      'Content-Type': contentType,
    },
    // Don't use withCredentials when using Bearer token auth
    withCredentials: false,
  });
};

// Create a base authenticated axios instance for dashboard APIs
const api = createAuthenticatedAxiosInstance();

// Create a base public axios instance for public-facing APIs
const publicApi = createPublicAxiosInstance();

// Response interceptor that handles auth errors
api.interceptors.response.use(
  response => response,
  async error => {
    // Handle 401 Unauthorized errors or specific token invalid message
    if (error.response?.status === 401 ||
        error.response?.data?.message === 'Token is not valid') {

      console.error('Authentication error: Token may be expired or invalid');

      // Check if we're already on the login page or dashboard to avoid redirect loops
      // Use the utility functions
      const onLoginPage = isLoginPage();
      const onDashboardPage = isDashboardPage();

      if (!onLoginPage && !onDashboardPage) {
        // Clear all auth data
        localStorage.removeItem('authToken');
        localStorage.removeItem('userId');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userEmail');
        localStorage.removeItem('userDepartment');

        // Show a message to the user
        console.log('Session expired or invalid, redirecting to login page');

        // Redirect to login page with session expired parameter
        window.location.href = '/login?session=expired';
      }
    }

    // Always return the rejected promise to let components handle errors
    return Promise.reject(error);
  }
);

// Auth Service
export const authService = {
  login: async (email, password) => {
    try {
      // For login, we create a new axios instance without auth interceptor
      // since we don't have a token yet
      const response = await axios.post(AUTH_ROUTES.LOGIN, { email, password });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  },

  register: async (userData) => {
    try {
      // For registration, we create a new axios instance without auth interceptor
      // since we don't have a token yet
      const response = await axios.post(AUTH_ROUTES.REGISTER, userData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  },

  getUserDetails: async (userId) => {
    try {
      // Use the api instance which already has the auth token in headers
      const response = await api.get(AUTH_ROUTES.GET_USER(userId));
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user details');
    }
  }
};

// User Service
export const userService = {
  getUserProfile: async (userId) => {
    try {
      const response = await api.get(USER_ROUTES.GET_USER_PROFILE(userId));
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch user profile');
    }
  },

  updateUserProfile: async (userId, userData) => {
    try {
      const response = await api.put(USER_ROUTES.UPDATE_USER_PROFILE(userId), userData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update user profile');
    }
  }
};

// Bank Service
export const bankService = {
  analyzeStatement: async (file) => {
    try {
      const formData = new FormData();
      formData.append('statement', file);

      // Create a special instance for multipart/form-data
      const multipartInstance = createAuthenticatedAxiosInstance(API_BASE_URL, 'multipart/form-data');
      const response = await multipartInstance.post(BANK_ROUTES.ANALYZE_STATEMENT, formData);

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to analyze bank statement');
    }
  }
};

// File Service
export const fileService = {
  uploadFile: async (file, metadata = {}) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      // Add any additional metadata
      Object.keys(metadata).forEach(key => {
        formData.append(key, metadata[key]);
      });

      // Create a special instance for multipart/form-data
      const multipartInstance = createAuthenticatedAxiosInstance(API_BASE_URL, 'multipart/form-data');
      const response = await multipartInstance.post(FILE_ROUTES.UPLOAD, formData);

      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to upload file');
    }
  },

  previewFile: async (filePath) => {
    try {
      const response = await api.get(FILE_ROUTES.PREVIEW(filePath));
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to preview file');
    }
  },

  downloadFile: async (filePath) => {
    try {
      // For file downloads, we need to use a different approach
      // to handle the binary data
      const response = await api.get(FILE_ROUTES.DOWNLOAD(filePath), {
        responseType: 'blob'
      });

      // Create a URL for the blob
      const url = window.URL.createObjectURL(new Blob([response.data]));

      // Create a temporary link and click it to trigger download
      const link = document.createElement('a');
      link.href = url;

      // Try to get filename from Content-Disposition header
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'download';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return true;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to download file');
    }
  }
};

// Analytics Service
export const analyticsService = {
  postAnalytics: async (analyticsData) => {
    try {
      const response = await api.post(ANALYTICS_ROUTES.POST_ANALYTICS, analyticsData);
      return response.data;
    } catch (error) {
      console.error('Failed to post analytics:', error);
      // Don't throw error for analytics to prevent disrupting user experience
    }
  }
};

// Dev Service
export const devService = {
  decodeToken: async (token) => {
    try {
      const response = await axios.post(DEV_ROUTES.DECODE_TOKEN, { token });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to decode token');
    }
  },

  // Validate token and redirect to login if expired
  validateToken: async () => {
    try {
      const token = localStorage.getItem('authToken');

      if (!token) {
        // No token found, redirect to login
        window.location.href = '/login?session=expired';
        return false;
      }

      // Try to validate the token
      const response = await axios.post(DEV_ROUTES.DECODE_TOKEN, { token });

      if (!response.data || !response.data.status) {
        // Token is invalid or expired
        localStorage.removeItem('authToken');
        localStorage.removeItem('userId');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userEmail');
        localStorage.removeItem('userDepartment');

        window.location.href = '/login?session=expired';
        return false;
      }

      // Token is valid
      return true;
    } catch (error) {
      console.error('Error validating token:', error);

      // Clear auth data and redirect
      localStorage.removeItem('authToken');
      localStorage.removeItem('userId');
      localStorage.removeItem('userRole');
      localStorage.removeItem('userEmail');
      localStorage.removeItem('userDepartment');

      window.location.href = '/login?session=expired';
      return false;
    }
  }
};

// Import reconciliation services
import reconciliationServices from './reconciliationService';

// Export a default API object with all services
export default {
  auth: authService,
  user: userService,
  bank: bankService,
  file: fileService,
  analytics: analyticsService,
  dev: devService,
  // Add reconciliation services
  statement: reconciliationServices.statement,
  transaction: reconciliationServices.transaction,
  reconciliation: reconciliationServices.reconciliation
};
