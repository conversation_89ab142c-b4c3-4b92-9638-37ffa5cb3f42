"""
Statement routes for the Bank Reconciliation API.
"""

from flask import Blueprint
from controllers.statement_controller import (
    get_all_statements,
    get_statement_by_id,
    delete_statement,
    upload_statement,
    parse_statement_file,
    get_statement_preview,
    download_statement_file
)
from middleware.auth_middleware import auth_required

# Create blueprint
statement_bp = Blueprint('statement', __name__)

# @route   GET /api/statements
# @desc    Get all statements
# @access  Private
statement_bp.route('/', methods=['GET'])(auth_required(get_all_statements))

# @route   GET /api/statements/:id
# @desc    Get statement by ID
# @access  Private
statement_bp.route('/<statement_id>', methods=['GET'])(auth_required(get_statement_by_id))

# @route   DELETE /api/statements/:id
# @desc    Delete statement
# @access  Private
statement_bp.route('/<statement_id>', methods=['DELETE'])(auth_required(delete_statement))

# @route   POST /api/statements/upload
# @desc    Upload statement
# @access  Private
statement_bp.route('/upload', methods=['POST'])(auth_required(upload_statement))

# @route   POST /api/statements/parse
# @desc    Parse statement file
# @access  Private
statement_bp.route('/parse', methods=['POST'])(auth_required(parse_statement_file))

# @route   GET /api/statements/:id/preview
# @desc    Get statement preview
# @access  Private
statement_bp.route('/<statement_id>/preview', methods=['GET'])(auth_required(get_statement_preview))

# @route   GET /api/statements/:id/download
# @desc    Download statement file
# @access  Private
statement_bp.route('/<statement_id>/download', methods=['GET'])(auth_required(download_statement_file))
