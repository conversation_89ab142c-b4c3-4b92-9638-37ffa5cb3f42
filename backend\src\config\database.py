"""
MongoDB database connection module.
"""

import pymongo
from pymongo import MongoClient
from config import config
import logging

def connect_db():
    """
    Connect to MongoDB database.
    """
    try:
        client = MongoClient(config['mongo_uri'])
        db = client[config['mongo_database']]
        print(f"MongoDB Connected: {client.address[0]}")
        return db
    except Exception as e:
        print(f"Error connecting to MongoDB: {str(e)}")
        logging.error(f"Error connecting to MongoDB: {str(e)}")
        # In a production environment, you might want to exit
        # import sys
        # sys.exit(1)
        return None
