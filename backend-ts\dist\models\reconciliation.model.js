"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReconciliationStatus = void 0;
const mongoose_1 = __importStar(require("mongoose"));
// Reconciliation status enum
var ReconciliationStatus;
(function (ReconciliationStatus) {
    ReconciliationStatus["IN_PROGRESS"] = "IN_PROGRESS";
    ReconciliationStatus["COMPLETED"] = "COMPLETED";
})(ReconciliationStatus || (exports.ReconciliationStatus = ReconciliationStatus = {}));
// Reconciliation schema
const reconciliationSchema = new mongoose_1.Schema({
    name: {
        type: String,
        required: [true, 'Reconciliation name is required'],
        trim: true,
    },
    description: {
        type: String,
        trim: true,
    },
    startDate: {
        type: Date,
        required: [true, 'Start date is required'],
    },
    endDate: {
        type: Date,
        required: [true, 'End date is required'],
    },
    status: {
        type: String,
        enum: Object.values(ReconciliationStatus),
        default: ReconciliationStatus.IN_PROGRESS,
    },
    totalTransactions: {
        type: Number,
        default: 0,
    },
    matchedTransactions: {
        type: Number,
        default: 0,
    },
    unmatchedTransactions: {
        type: Number,
        default: 0,
    },
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User ID is required'],
    },
}, {
    timestamps: true,
});
// Create and export Reconciliation model
exports.default = mongoose_1.default.model('Reconciliation', reconciliationSchema);
