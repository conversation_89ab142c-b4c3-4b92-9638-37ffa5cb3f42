/**
 * CORS Test Utility
 * 
 * This file provides functions to test CORS configuration between frontend and backend.
 */

import { API_BASE_URL } from '../apiRoutes';

/**
 * Test the CORS configuration by making a request to the health endpoint
 * @returns {Promise<Object>} The response from the health endpoint
 */
export const testCorsConfiguration = async () => {
  try {
    console.log('Testing CORS configuration...');
    console.log('Making request to:', `${API_BASE_URL}/health`);
    
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('CORS test successful:', data);
    return {
      success: true,
      data,
      message: 'CORS configuration is working correctly'
    };
  } catch (error) {
    console.error('CORS test failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'CORS configuration is not working correctly'
    };
  }
};

/**
 * Test the authentication endpoints with CORS
 * @returns {Promise<Object>} The response from the test
 */
export const testAuthEndpoint = async () => {
  try {
    console.log('Testing authenticated endpoint with CORS...');
    
    // Get token from localStorage
    const token = localStorage.getItem('authToken');
    
    if (!token) {
      return {
        success: false,
        message: 'No authentication token found. Please log in first.'
      };
    }
    
    console.log('Making authenticated request to:', `${API_BASE_URL}/reconciliations`);
    
    const response = await fetch(`${API_BASE_URL}/reconciliations`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Authenticated CORS test successful:', data);
    return {
      success: true,
      data,
      message: 'Authenticated CORS configuration is working correctly'
    };
  } catch (error) {
    console.error('Authenticated CORS test failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'Authenticated CORS configuration is not working correctly'
    };
  }
};
