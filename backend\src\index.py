"""
Main application file for the Bank Reconciliation API.
This is the entry point for the application.
"""

from flask import Flask, jsonify
from flask_cors import CORS
import os

# Import routes
from routes.auth_routes import auth_bp
from routes.user_routes import user_bp
from routes.transaction_routes import transaction_bp
from routes.statement_routes import statement_bp
from routes.reconciliation_routes import reconciliation_bp
from routes.health_routes import health_bp
from routes.file_routes import file_bp

# Import database connection
from config.database import connect_db

# Initialize Flask app
app = Flask(__name__)

# Configure Flask to handle trailing slashes
app.url_map.strict_slashes = False

# Connect to MongoDB
connect_db()

# Configure CORS for development
CORS(app, resources={
    r"/api/*": {
        "origins": "*",  # Allow all origins in development
        "supports_credentials": True,
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        "allow_headers": ["Content-Type", "Authorization", "Accept", "Origin", "X-Requested-With"]
    }
})

# Register blueprints (routes)
app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(user_bp, url_prefix='/api/users')
app.register_blueprint(transaction_bp, url_prefix='/api/transactions')
app.register_blueprint(statement_bp, url_prefix='/api/statements')
app.register_blueprint(reconciliation_bp, url_prefix='/api/reconciliations')
app.register_blueprint(health_bp, url_prefix='/api/health')
app.register_blueprint(file_bp, url_prefix='/api/files')

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"message": "Resource not found"}), 404

@app.errorhandler(500)
def server_error(error):
    return jsonify({"message": "Internal server error"}), 500

# Catch-all route for OPTIONS requests to handle CORS preflight requests
@app.route('/', defaults={'path': ''}, methods=['OPTIONS'])
@app.route('/<path:path>', methods=['OPTIONS'])
def handle_options(path):
    return '', 200

# Start the server
if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=True)
