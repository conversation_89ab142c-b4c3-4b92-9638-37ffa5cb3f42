import React, { useState } from 'react';
import { FiFileText, FiDownload, FiAlertCircle, FiChevronLeft, FiChevronRight, FiInfo, FiBarChart2, FiCheckCircle, FiXCircle } from 'react-icons/fi';
import PDFPreview from './PDFPreview';
import CSVPreview from './CSVPreview';
import ExcelPreview from './ExcelPreview';
import { formatDate, formatFileSize } from '../../utils/formatters';

const FilePreview = ({ fileData, onDownload, onAnalyze }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [showMetadata, setShowMetadata] = useState(false);
  const [showInsights, setShowInsights] = useState(false);

  if (!fileData) {
    return (
      <div className="text-center p-4 text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
        <FiFileText className="mx-auto h-12 w-12 text-gray-400 mb-2" />
        <p className="text-sm font-medium">No preview available</p>
        <p className="text-xs mt-1">The file preview could not be loaded</p>
      </div>
    );
  }

  // Check if there's an error in the fileData
  if (fileData.error || (fileData.status === false && fileData.message)) {
    return (
      <div className="bg-red-50 p-4 rounded-md border border-red-200">
        <div className="flex">
          <div className="flex-shrink-0">
            <FiAlertCircle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading file preview</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{fileData.message || fileData.error || 'Failed to load file preview'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleDownload = () => {
    if (onDownload) {
      onDownload(fileData);
    }
  };

  const handleAnalyze = () => {
    if (onAnalyze) {
      onAnalyze(fileData);
    } else {
      setShowInsights(!showInsights);
    }
  };

  const renderPreviewContent = () => {
    const fileType = fileData.metadata?.fileType?.toLowerCase() || fileData.fileType?.toLowerCase() || '';
    const processingStatus = fileData.processingStatus || 'completed';
    const fetchTime = fileData.fetchTime || 0;

    if (fileType === '.pdf' || fileType === 'pdf') {
      return <PDFPreview
        data={fileData}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        processingStatus={processingStatus}
      />;
    } else if (fileType === '.csv' || fileType === 'csv') {
      return <CSVPreview
        data={fileData}
        processingStatus={processingStatus}
        fetchTime={fetchTime}
      />;
    } else if (fileType === '.xlsx' || fileType === 'xlsx' || fileType === '.xls' || fileType === 'xls') {
      return <ExcelPreview
        data={fileData}
        processingStatus={processingStatus}
        fetchTime={fetchTime}
      />;
    } else {
      return (
        <div className="p-4 bg-gray-100 rounded-md text-gray-700">
          <p>Preview not available for this file type.</p>
        </div>
      );
    }
  };

  const renderMetadata = () => {
    const metadata = fileData.metadata || {};

    return (
      <div className="mt-4 border border-gray-200 rounded-md p-4 bg-gray-50">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-md font-medium text-gray-900">Metadata</h3>
          <button
            className="text-sm text-indigo-600 hover:text-indigo-800"
            onClick={() => setShowMetadata(!showMetadata)}
          >
            {showMetadata ? 'Hide' : 'Show'}
          </button>
        </div>

        {showMetadata && (
          <div className="grid grid-cols-2 gap-4 text-sm mt-2">
            {metadata.author && (
              <div>
                <p className="text-gray-500">Author</p>
                <p className="font-medium">{metadata.author}</p>
              </div>
            )}
            {metadata.creationDate && (
              <div>
                <p className="text-gray-500">Created</p>
                <p className="font-medium">{formatDate(metadata.creationDate)}</p>
              </div>
            )}
            {metadata.lastModified && (
              <div>
                <p className="text-gray-500">Modified</p>
                <p className="font-medium">{formatDate(metadata.lastModified)}</p>
              </div>
            )}
            {metadata.pageCount && (
              <div>
                <p className="text-gray-500">Pages</p>
                <p className="font-medium">{metadata.pageCount}</p>
              </div>
            )}
            {metadata.title && (
              <div className="col-span-2">
                <p className="text-gray-500">Title</p>
                <p className="font-medium">{metadata.title}</p>
              </div>
            )}
            {metadata.subject && (
              <div className="col-span-2">
                <p className="text-gray-500">Subject</p>
                <p className="font-medium">{metadata.subject}</p>
              </div>
            )}
            {metadata.keywords && metadata.keywords.length > 0 && (
              <div className="col-span-2">
                <p className="text-gray-500">Keywords</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {metadata.keywords.map((keyword, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-200 rounded-full text-xs">
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderInsights = () => {
    const insights = fileData.insights || {};
    const summary = fileData.summary || {};

    if (!insights.suggestions && !insights.anomalies && !insights.patterns && !summary.rowCount) {
      return null;
    }

    return (
      <div className="mt-4 border border-gray-200 rounded-md p-4 bg-gray-50">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-md font-medium text-gray-900">Insights</h3>
          <button
            className="text-sm text-indigo-600 hover:text-indigo-800"
            onClick={() => setShowInsights(!showInsights)}
          >
            {showInsights ? 'Hide' : 'Show'}
          </button>
        </div>

        {showInsights && (
          <div className="space-y-4 mt-2">
            {/* Summary */}
            {(summary.rowCount || summary.columnCount || summary.sheetCount) && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <FiBarChart2 className="mr-1" /> Summary
                </h4>
                <div className="grid grid-cols-3 gap-2">
                  {summary.rowCount !== undefined && (
                    <div className="bg-white p-2 rounded border border-gray-200">
                      <p className="text-xs text-gray-500">Rows</p>
                      <p className="text-sm font-medium">{summary.rowCount.toLocaleString()}</p>
                    </div>
                  )}
                  {summary.columnCount !== undefined && (
                    <div className="bg-white p-2 rounded border border-gray-200">
                      <p className="text-xs text-gray-500">Columns</p>
                      <p className="text-sm font-medium">{summary.columnCount}</p>
                    </div>
                  )}
                  {summary.sheetCount !== undefined && (
                    <div className="bg-white p-2 rounded border border-gray-200">
                      <p className="text-xs text-gray-500">Sheets</p>
                      <p className="text-sm font-medium">{summary.sheetCount}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Suggestions */}
            {insights.suggestions && insights.suggestions.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <FiCheckCircle className="mr-1 text-green-500" /> Suggestions
                </h4>
                <ul className="space-y-1">
                  {insights.suggestions.map((suggestion, index) => (
                    <li key={index} className="text-sm bg-white p-2 rounded border border-gray-200">
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Anomalies */}
            {insights.anomalies && insights.anomalies.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <FiXCircle className="mr-1 text-red-500" /> Anomalies
                </h4>
                <ul className="space-y-1">
                  {insights.anomalies.map((anomaly, index) => (
                    <li key={index} className="text-sm bg-white p-2 rounded border border-gray-200">
                      {anomaly.message || `${anomaly.type}: ${anomaly.count || anomaly.columns?.join(', ')}`}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Patterns */}
            {insights.patterns && insights.patterns.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <FiInfo className="mr-1 text-blue-500" /> Detected Patterns
                </h4>
                <div className="space-y-2">
                  {insights.patterns.map((pattern, index) => (
                    <div key={index} className="text-sm bg-white p-2 rounded border border-gray-200">
                      <p className="font-medium capitalize">{pattern.type} ({pattern.count})</p>
                      {pattern.samples && (
                        <div className="mt-1 flex flex-wrap gap-1">
                          {pattern.samples.map((sample, i) => (
                            <span key={i} className="px-2 py-0.5 bg-gray-100 rounded text-xs">
                              {sample}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Get file name and type from either the new or old data structure
  const fileName = fileData.metadata?.fileName || fileData.fileName || 'statement.xlsx';
  const fileType = fileData.metadata?.fileType || fileData.fileType || 'excel';
  const fileSize = fileData.metadata?.fileSize || fileData.fileSize || 0;
  const uploadDate = fileData.metadata?.lastModified || fileData.uploadDate || new Date().toISOString();

  // Check if we have a content array but no preview
  if (Array.isArray(fileData.content) && !fileData.preview) {
    // Create a simple preview from the content
    fileData.preview = `${fileData.content.length} rows of data`;
  }

  return (
    <div className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium text-gray-900">{fileData.name}</h2>
        <div className="flex space-x-2">
          <button
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={handleAnalyze}
          >
            <FiBarChart2 className="mr-2 -ml-0.5 h-4 w-4" />
            Analyze
          </button>
          <button
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            onClick={handleDownload}
          >
            <FiDownload className="mr-2 -ml-0.5 h-4 w-4" />
            Download
          </button>
        </div>
      </div>

      <div className="border border-gray-200 rounded-md p-4 bg-gray-50 mb-4">
        <div className="flex items-center mb-4">
          <FiFileText className="h-10 w-10 text-indigo-500 mr-3" />
          <div>
            <p className="text-sm font-medium text-gray-900">{fileName}</p>
            <p className="text-xs text-gray-500">
              {fileType?.toUpperCase().replace('.', '')} • {formatFileSize(fileSize)}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500">Uploaded</p>
            <p className="font-medium">{formatDate(uploadDate)}</p>
          </div>
          <div>
            <p className="text-gray-500">Type</p>
            <p className="font-medium capitalize">{fileType?.replace('.', '')}</p>
          </div>
        </div>
      </div>

      {/* Metadata section */}
      {fileData.metadata && Object.keys(fileData.metadata).length > 0 && renderMetadata()}

      {/* Insights section */}
      {(fileData.insights || fileData.summary) && renderInsights()}

      <div className="mt-4">
        <h3 className="text-md font-medium text-gray-900 mb-2">Preview</h3>
        {renderPreviewContent()}
      </div>
    </div>
  );
};

export default FilePreview;
