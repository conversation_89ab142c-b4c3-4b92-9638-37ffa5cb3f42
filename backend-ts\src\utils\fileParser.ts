import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import pdfParse from 'pdf-parse';
import * as XLSX from 'xlsx';
import csvParser from 'csv-parser';
import { extractTablesFromPDF } from './pdfTableExtractor';

// Convert fs functions to Promise-based
const readFileAsync = promisify(fs.readFile);
const statAsync = promisify(fs.stat);

/**
 * Interface for file preview data
 */
export interface FilePreviewData {
  metadata: {
    fileName: string;
    fileSize: number;
    fileType: string;
    lastModified?: Date;
    mimeType?: string;
    pageCount?: number;
    author?: string;
    creationDate?: Date;
    keywords?: string[];
    title?: string;
    subject?: string;
  };
  content: any; // Will be different based on file type
  preview: string; // Text preview for display
  summary?: {
    rowCount?: number;
    columnCount?: number;
    sheetCount?: number;
    dataTypes?: Record<string, string | Record<string, string>>;
    statistics?: Record<string, any>;
  };
  insights?: any; // Insights extracted from the file
}

/**
 * Detect MIME type from file content
 */
export async function detectMimeType(filePath: string): Promise<string> {
  try {
    const fileExt = path.extname(filePath).toLowerCase();
    const buffer = await readFileAsync(filePath, { encoding: null });

    // Check for PDF signature
    if (buffer.subarray(0, 4).toString() === '%PDF') {
      return 'application/pdf';
    }

    // Check for Excel signatures
    if (buffer.subarray(0, 8).toString('hex') === 'd0cf11e0a1b11ae1') {
      return 'application/vnd.ms-excel'; // XLS
    }

    if (buffer.subarray(0, 4).toString('hex') === '504b0304') {
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; // XLSX
    }

    // Check for CSV by examining content
    const sample = buffer.subarray(0, 1000).toString('utf8');
    const lines = sample.split('\n').slice(0, 5);
    const commaCount = lines.reduce((count, line) => count + (line.match(/,/g) || []).length, 0);
    const tabCount = lines.reduce((count, line) => count + (line.match(/\t/g) || []).length, 0);

    if (commaCount > tabCount && commaCount > lines.length * 2) {
      return 'text/csv';
    }

    // Default to extension-based detection
    switch (fileExt) {
      case '.pdf': return 'application/pdf';
      case '.csv': return 'text/csv';
      case '.xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.xls': return 'application/vnd.ms-excel';
      default: return 'application/octet-stream';
    }
  } catch (error) {
    console.error('Error detecting MIME type:', error);
    return 'application/octet-stream';
  }
}

/**
 * Parse PDF file and extract content with advanced features
 */
export async function parsePDF(filePath: string): Promise<FilePreviewData> {
  try {
    const stats = await statAsync(filePath);
    const fileName = path.basename(filePath);

    // Use our enhanced PDF table extractor
    const extractedData = await extractTablesFromPDF(filePath);

    // Extract text content
    const text = extractedData.text;

    // Create a preview (first 1000 characters)
    const preview = text.substring(0, 1000) + (text.length > 1000 ? '...' : '');

    // Extract metadata
    const info = extractedData.metadata || {};

    // Get tables and bank statement info
    const tables = extractedData.tables;
    const bankStatementInfo = extractedData.bankInfo;

    // Merge PDF metadata with bank statement metadata
    const enhancedMetadata = {
      fileName,
      fileSize: stats.size,
      fileType: 'pdf',
      lastModified: stats.mtime,
      mimeType: 'application/pdf',
      pageCount: extractedData.metadata?.Pages || 0,
      author: info.Author,
      creationDate: info.CreationDate ? new Date(info.CreationDate) : undefined,
      title: bankStatementInfo.bankName || info.Title,
      subject: bankStatementInfo.accountNumber ?
        `Account: ${bankStatementInfo.accountNumber}` : info.Subject,
      keywords: [
        ...(info.Keywords ? info.Keywords.split(',').map((k: string) => k.trim()) : []),
        ...(bankStatementInfo.dateRange ? [`Period: ${bankStatementInfo.dateRange}`] : []),
        ...(bankStatementInfo.accountType ? [`Account Type: ${bankStatementInfo.accountType}`] : [])
      ]
    };

    return {
      metadata: enhancedMetadata,
      content: {
        text,
        pages: extractedData.metadata?.Pages || 0,
        info: extractedData.metadata,
        version: extractedData.metadata?.PDFFormatVersion,
        metadata: extractedData.metadata,
        tables,
        bankInfo: bankStatementInfo
      },
      preview,
      summary: {
        rowCount: tables.reduce((count, table) => count + table.rows.length, 0),
        columnCount: tables.length > 0 ? Math.max(...tables.map(t => t.headers.length)) : 0
      }
    };
  } catch (error) {
    console.error('Error parsing PDF:', error);

    // Fallback to basic metadata if parsing fails
    try {
      const stats = await statAsync(filePath);
      const fileName = path.basename(filePath);

      return {
        metadata: {
          fileName,
          fileSize: stats.size,
          fileType: 'pdf',
          lastModified: stats.mtime,
          mimeType: 'application/pdf'
        },
        content: {
          text: "PDF parsing failed. The file may be corrupted or password-protected.",
          pages: 0
        },
        preview: "PDF parsing failed. The file may be corrupted or password-protected."
      };
    } catch (fallbackError) {
      throw new Error(`Failed to parse PDF file: ${(error as Error).message || 'Unknown error'}`);
    }
  }
}

/**
 * This function is now replaced by the implementation in pdfTableExtractor.ts
 * @deprecated Use the extractTablesFromPDF function from pdfTableExtractor.ts instead
 */
function _extractTablesFromPDF_deprecated(text: string): Array<{ headers: string[], rows: any[] }> {
  const tables: Array<{ headers: string[], rows: any[] }> = [];
  const lines = text.split('\n');

  // Look for potential table patterns
  let currentTable: { headers: string[], rows: any[] } | null = null;
  let tableStartLine = -1;

  // First, try to detect bank statement tables specifically
  const bankStatementTable = _extractBankStatementTable_deprecated(text);
  if (bankStatementTable && bankStatementTable.rows.length > 0) {
    tables.push(bankStatementTable);
  }

  // Then continue with general table detection
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Skip empty lines
    if (!line) continue;

    // Check if this line could be a header row (contains multiple words separated by spaces)
    const words = line.split(/\s{2,}/g).filter(w => w.trim());

    // Look for common table header patterns in bank statements and financial documents
    const isHeaderRow =
      (words.length >= 3 && !currentTable) ||
      (line.includes('Date') && line.includes('Description') && line.includes('Amount')) ||
      (line.includes('Date') && line.includes('Details') && line.includes('Debit')) ||
      (line.includes('EntryDate') && line.includes('ValueDate')) ||
      (line.includes('Transaction') && line.includes('Amount')) ||
      (line.match(/Entry\s+Date/) && line.match(/Value\s+Date/));

    if (isHeaderRow) {
      // If we already have a table in progress, save it before starting a new one
      if (currentTable && currentTable.rows.length > 0) {
        tables.push(currentTable);
      }

      // Potential table header found
      currentTable = { headers: words, rows: [] };
      tableStartLine = i;
      continue;
    }

    // If we're in a table, try to parse rows
    if (currentTable) {
      // Check if this line matches the header pattern
      const values = line.split(/\s{2,}/g).filter(w => w.trim());

      // If values count is close to headers count, consider it a row
      // More flexible matching for bank statements which often have irregular spacing
      const isRowData =
        (values.length >= Math.max(2, currentTable.headers.length - 2) &&
         values.length <= currentTable.headers.length + 2) ||
        (line.match(/\d{2}[\/\-\.]\d{2}[\/\-\.]\d{2,4}/) && values.length >= 3); // Has date format and enough columns

      if (isRowData) {
        // Create a row object
        const row: Record<string, string> = {};
        for (let j = 0; j < Math.min(values.length, currentTable.headers.length); j++) {
          row[currentTable.headers[j]] = values[j];
        }

        // If we have more values than headers, add the last value as "Balance" or "Amount"
        if (values.length > currentTable.headers.length) {
          const lastHeader = currentTable.headers.some(h =>
            h.toLowerCase().includes('balance') ||
            h.toLowerCase().includes('amount')
          ) ? 'Additional' : 'Balance';

          row[lastHeader] = values[values.length - 1];
        }

        currentTable.rows.push(row);
      } else if (currentTable.rows.length > 0 && (line.match(/total/i) || line.match(/balance/i))) {
        // This might be a footer row with totals - add it to the last row with a special marker
        const lastRow = currentTable.rows[currentTable.rows.length - 1];
        lastRow['_footer'] = line;

        // End of table detected
        tables.push(currentTable);
        currentTable = null;
      } else if (currentTable.rows.length > 0 && line.length < 10) {
        // Short line after rows - likely end of table
        tables.push(currentTable);
        currentTable = null;
      } else if (i > tableStartLine + 5 && currentTable.rows.length === 0) {
        // Not enough rows found after several lines, probably not a table
        currentTable = null;
      }
    }
  }

  // Add the last table if it exists
  if (currentTable && currentTable.rows.length > 0) {
    tables.push(currentTable);
  }

  return tables;
}

/**
 * Extract bank statement metadata from PDF text
 * @deprecated Use the extractTablesFromPDF function from pdfTableExtractor.ts instead
 */
function _extractBankStatementMetadata_deprecated(text: string): {
  bankName: string;
  accountNumber: string;
  accountType: string;
  dateRange: string;
  sortCode: string;
  branch: string;
  openingBalance?: number;
  closingBalance?: number;
} {
  const lines = text.split('\n').filter(line => line.trim() !== '');
  const result = {
    bankName: '',
    accountNumber: '',
    accountType: '',
    dateRange: '',
    sortCode: '',
    branch: '',
    openingBalance: undefined as number | undefined,
    closingBalance: undefined as number | undefined
  };

  // Look for bank name in the first few lines
  for (let i = 0; i < Math.min(10, lines.length); i++) {
    const line = lines[i].trim();
    if (line.includes('BANK') || line.includes('Bank') || line.match(/[A-Z]+\s+BANK/i)) {
      result.bankName = line;
      break;
    }
  }

  // Look for account information
  for (let i = 0; i < Math.min(30, lines.length); i++) {
    const line = lines[i].trim();

    // Account number patterns
    if (line.match(/ACCOUNT\s*:?\s*\d+/i) || line.match(/A\/C\s*:?\s*\d+/i)) {
      const match = line.match(/\d+/);
      if (match) {
        result.accountNumber = match[0];
      }

      // Also check for account type in the same line
      if (line.match(/CURRENT|SAVINGS|CORPORATE|BUSINESS/i)) {
        result.accountType = line.replace(/ACCOUNT\s*:?\s*\d+/i, '').trim();
      }
    }

    // Account type patterns
    if (line.match(/CURRENT\s*ACCOUNT/i) || line.match(/SAVINGS\s*ACCOUNT/i) ||
        line.match(/CORPORATE\s*ACCOUNT/i) || line.match(/BUSINESS\s*ACCOUNT/i)) {
      result.accountType = line;
    }

    // Sort code patterns
    if (line.match(/SORT\s*CODE/i) || line.match(/SORT\s*CODE\s*:?\s*\d+/i)) {
      const match = line.match(/\d+-\d+-\d+/) || line.match(/\d+/);
      if (match) {
        result.sortCode = match[0];
      } else {
        result.sortCode = line.replace(/SORT\s*CODE\s*:?/i, '').trim();
      }
    }

    // Branch patterns
    if (line.match(/BRANCH\s*:?/i) || line.includes('BRANCH')) {
      result.branch = line.replace(/BRANCH\s*:?/i, '').trim();
    }

    // Date range patterns
    if (line.match(/\d{2}[\/\-\.][A-Za-z]{3}[\/\-\.]\d{2,4}\s+[Tt][Oo]\s+\d{2}[\/\-\.][A-Za-z]{3}[\/\-\.]\d{2,4}/) ||
        line.match(/\d{2}[\/\-\.]\d{2}[\/\-\.]\d{2,4}\s+[Tt][Oo]\s+\d{2}[\/\-\.]\d{2}[\/\-\.]\d{2,4}/)) {
      result.dateRange = line;
    } else if (line.match(/STATEMENT\s+PERIOD/i) || line.match(/PERIOD\s*:/i)) {
      result.dateRange = line.replace(/STATEMENT\s+PERIOD\s*:?/i, '').replace(/PERIOD\s*:?/i, '').trim();
    }

    // Balance patterns
    if (line.match(/OPENING\s+BALANCE/i) || line.match(/BALANCE\s+B\/F/i)) {
      const match = line.match(/[\d,]+\.\d{2}/);
      if (match) {
        result.openingBalance = parseFloat(match[0].replace(/,/g, ''));
      }
    }

    if (line.match(/CLOSING\s+BALANCE/i) || line.match(/BALANCE\s+C\/F/i)) {
      const match = line.match(/[\d,]+\.\d{2}/);
      if (match) {
        result.closingBalance = parseFloat(match[0].replace(/,/g, ''));
      }
    }
  }

  return result;
}

/**
 * Specialized function to extract bank statement tables
 * @deprecated Use the extractTablesFromPDF function from pdfTableExtractor.ts instead
 */
function _extractBankStatementTable_deprecated(text: string): { headers: string[], rows: any[] } | null {
  const lines = text.split('\n').filter(line => line.trim() !== '');

  // Initialize result
  const result: {
    headers: string[],
    rows: Array<Record<string, string>>
  } = {
    headers: ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'],
    rows: []
  };

  // Check if this is a Polaris Bank statement
  const isPolarisBank = text.includes('Polaris Bank Limited') || text.includes('POLARIS BANK');

  // Special handling for Polaris Bank format
  if (isPolarisBank) {
    return _extractPolarisBankStatement_deprecated(text);
  }

  // Look for table headers and transaction data
  let tableStartIndex = -1;
  let tableHeaders: string[] = [];

  // Find the table header row (typically contains EntryDate, Details, ValueDate, etc.)
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (line.includes('EntryDate') ||
        line.includes('ENTRYDATE') ||
        (line.includes('Date') && line.includes('Details') && line.includes('Debit')) ||
        (line.includes('DATE') && line.includes('DETAILS') && line.includes('DEBIT')) ||
        (line.includes('Date') && line.includes('Description') && line.includes('Amount'))) {

      // Split the header by multiple spaces to get column names
      tableHeaders = line.split(/\s{2,}/).filter(h => h.trim() !== '');

      // If we have headers in all caps, convert them to proper case
      tableHeaders = tableHeaders.map(header => {
        const upperHeader = header.toUpperCase();
        if (upperHeader === 'ENTRYDATE') return 'EntryDate';
        if (upperHeader === 'VALUEDATE') return 'ValueDate';
        if (upperHeader === 'DETAILS') return 'Details';
        if (upperHeader === 'DEBIT') return 'Debit';
        if (upperHeader === 'CREDIT') return 'Credit';
        if (upperHeader === 'BALANCE') return 'Balance';
        return header;
      });

      tableStartIndex = i;
      result.headers = tableHeaders;
      break;
    }
  }

  // First try the standard approach if we found headers
  if (tableStartIndex > -1 && tableHeaders.length > 0) {
    // Parse transaction entries
    for (let i = tableStartIndex + 1; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) continue;

      // Check if this looks like a transaction entry (has date and amount)
      if (line.match(/\d{2}-[A-Z]{3}-\d{2}/) ||
          line.match(/\d{2}\/\d{2}\/\d{4}/) ||
          line.match(/\d{1,2}\.\d{2}\.\d{2,4}/)) {

        // Split by multiple spaces to get column values
        const values = line.split(/\s{2,}/).filter(v => v.trim() !== '');

        // Only process if we have enough values
        if (values.length >= 3) {
          const entry: Record<string, string> = {};

          // Map values to headers
          for (let j = 0; j < Math.min(tableHeaders.length, values.length); j++) {
            entry[tableHeaders[j]] = values[j];
          }

          // Add balance if it's the last column and we have more values than headers
          if (values.length > tableHeaders.length) {
            entry['Balance'] = values[values.length - 1];
          }

          result.rows.push(entry);
        }
      } else if (result.rows.length > 0 && (
        line.includes('Total') ||
        line.includes('Balance') ||
        line.match(/closing\s+balance/i)
      )) {
        // We've reached the end of the transaction table
        break;
      }
    }
  }

  // If we have rows from the standard approach, return them
  if (result.rows.length > 0) {
    return result;
  }

  // Otherwise, try the new approach for the specific format with date pattern "03-MAR-25"
  // Parse rows using known date pattern at start
  const entries: Record<string, string>[] = [];
  let current: Record<string, string> | null = null;

  for (const line of lines) {
    const dateMatch = line.match(/^\d{2}-[A-Z]{3}-\d{2}/); // e.g., 03-MAR-25

    if (dateMatch) {
      // New row starting with EntryDate
      if (current) entries.push(current);

      const [entryDate, ...rest] = line.split(/\s{2,}|\t/);
      current = {
        EntryDate: entryDate,
        Details: rest.join(' '),
        ValueDate: '',
        Debit: '',
        Credit: '',
        Balance: ''
      };
    } else if (current) {
      // Handle continuation lines or table columns
      const parts = line.split(/\s{2,}|\t/);
      if (parts.length >= 4) {
        [current.ValueDate, current.Debit, current.Credit, current.Balance] = parts;
      } else {
        // If continuation line, append to details
        current.Details += ' ' + line;
      }
    }
  }

  // Don't forget to add the last entry
  if (current) entries.push(current);

  // If we found entries with the new approach, return them
  if (entries.length > 0) {
    result.rows = entries;
    return result;
  }

  // If we still don't have entries, try a more aggressive pattern matching approach
  const dataBuffer = text;

  // Try to find patterns like:
  // 03-MAR-25
  // 01.03.2025 RANDALPHA MFB/OLALERE IDOWU MARY/'090496250301121728413602636378
  // 03-MAR-25
  // 960.000
  // 65,897,998.14
  const pattern = /(\d{2}-[A-Z]{3}-\d{2})\s+(\d{2}\.\d{2}\.\d{4}\s+RANDALPHA[^\n]+)\s+(\d{2}-[A-Z]{3}-\d{2})\s+([\d,]+\.\d{2})\s+(\d+)\s+([\d,]+\.\d{2})/g;

  let match;
  while ((match = pattern.exec(dataBuffer)) !== null) {
    const entry = {
      EntryDate: match[1],
      Details: match[2],
      ValueDate: match[3],
      Debit: match[4],
      Credit: match[5],
      Balance: match[6]
    };

    result.rows.push(entry);
  }

  // If we found entries with the pattern matching approach, return them
  if (result.rows.length > 0) {
    return result;
  }

  // If all approaches failed, return null
  return null;
}

/**
 * Specialized function to extract Polaris Bank statement data
 * @deprecated Use the extractTablesFromPDF function from pdfTableExtractor.ts instead
 */
function _extractPolarisBankStatement_deprecated(text: string): { headers: string[], rows: any[] } | null {
  const lines = text.split('\n').filter(line => line.trim() !== '');

  // Initialize result
  const result: {
    headers: string[],
    rows: Array<Record<string, string>>
  } = {
    headers: ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'],
    rows: []
  };

  // First, look for the balance B/F entry
  let balanceEntry: Record<string, string> | null = null;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.includes('Balance B/F') || line.match(/Balance\s+B\/F/i)) {
      const balanceMatch = line.match(/[\d,]+\.\d{2}/);
      if (balanceMatch) {
        // Look for the date in nearby lines
        let entryDate = '';
        for (let j = Math.max(0, i-3); j < Math.min(i+3, lines.length); j++) {
          const dateLine = lines[j].trim();
          if (dateLine.match(/^\d{2}-[A-Z]{3}-\d{2}$/)) {
            entryDate = dateLine;
            break;
          }
        }

        balanceEntry = {
          EntryDate: entryDate,
          Details: 'Balance B/F.......',
          ValueDate: entryDate,
          Debit: '',
          Credit: '',
          Balance: balanceMatch[0]
        };

        // Add the balance entry to the results
        result.rows.push(balanceEntry);
        break;
      }
    }
  }

  // Now look for the table header row
  let tableStartIndex = -1;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Look for the header row
    if (line.includes('EntryDate') && line.includes('Details') &&
        line.includes('ValueDate') && line.includes('Debit') &&
        line.includes('Credit') && line.includes('Balance')) {
      tableStartIndex = i;
      break;
    }
  }

  // If we found the header row, try to extract the transaction data
  if (tableStartIndex > -1) {
    // First approach: Try to extract transactions using the table structure
    // This pattern looks for rows with the format:
    // 03-MAR-25  01.03.2025 RANDALPHA MFB/...  03-MAR-25  960.00  0  65,897,998.14
    const tablePattern = /(\d{2}-[A-Z]{3}-\d{2})\s+(.*?)\s+(\d{2}-[A-Z]{3}-\d{2})\s+([\d,]+\.\d{2})\s+(\d+)\s+([\d,]+\.\d{2})/g;

    // Get the text after the header row
    const tableText = lines.slice(tableStartIndex + 1).join('\n');

    let tableMatch;
    while ((tableMatch = tablePattern.exec(tableText)) !== null) {
      const entry = {
        EntryDate: tableMatch[1],
        Details: tableMatch[2].trim(),
        ValueDate: tableMatch[3],
        Debit: tableMatch[4],
        Credit: tableMatch[5],
        Balance: tableMatch[6]
      };

      result.rows.push(entry);
    }
  }

  // If we didn't find any transactions with the first approach, try a more flexible pattern
  if (result.rows.length <= (balanceEntry ? 1 : 0)) {
    // This pattern is more flexible and looks for the transaction data in a less structured format
    const flexPattern = /(\d{2}-[A-Z]{3}-\d{2})[^\n]*?(\d{2}\.\d{2}\.\d{4}\s+[^\n]+?)[^\n]*?(\d{2}-[A-Z]{3}-\d{2})[^\n]*?([\d,]+\.\d{2})[^\n]*?(\d+)[^\n]*?([\d,]+\.\d{2})/g;

    let flexMatch;
    while ((flexMatch = flexPattern.exec(text)) !== null) {
      const entry = {
        EntryDate: flexMatch[1],
        Details: flexMatch[2].trim(),
        ValueDate: flexMatch[3],
        Debit: flexMatch[4],
        Credit: flexMatch[5],
        Balance: flexMatch[6]
      };

      // Check if this is a duplicate
      const isDuplicate = result.rows.some(row =>
        row.EntryDate === entry.EntryDate &&
        row.Details === entry.Details &&
        row.Balance === entry.Balance
      );

      if (!isDuplicate) {
        result.rows.push(entry);
      }
    }
  }

  // If we still don't have enough entries, try a third approach
  if (result.rows.length <= (balanceEntry ? 1 : 0)) {
    // This approach looks for patterns in the raw text where transaction data might be spread across multiple lines
    const datePattern = /\d{2}-[A-Z]{3}-\d{2}/g;
    const dates = [];
    let dateMatch;

    // Find all dates in the text
    while ((dateMatch = datePattern.exec(text)) !== null) {
      dates.push({
        date: dateMatch[0],
        index: dateMatch.index
      });
    }

    // Now try to find transaction details between dates
    for (let j = 0; j < dates.length - 1; j++) {
      const currentDate = dates[j];
      const nextDate = dates[j + 1];

      // Extract the text between these dates
      const segment = text.substring(currentDate.index, nextDate.index);

      // Look for transaction details
      if (segment.includes('RANDALPHA') || segment.includes('MFB/')) {
        // Find the details line
        const detailsMatch = segment.match(/(\d{2}\.\d{2}\.\d{4}\s+[^\n]+)/);

        if (detailsMatch) {
          // Find amount patterns
          const amountMatches = segment.match(/([\d,]+\.\d{2})/g);

          if (amountMatches && amountMatches.length >= 2) {
            const entry = {
              EntryDate: currentDate.date,
              Details: detailsMatch[1],
              ValueDate: nextDate.date,
              Debit: amountMatches[0],
              Credit: '0',
              Balance: amountMatches[amountMatches.length - 1]
            };

            // Check if this is a duplicate
            const isDuplicate = result.rows.some(row =>
              row.EntryDate === entry.EntryDate &&
              row.Details === entry.Details
            );

            if (!isDuplicate) {
              result.rows.push(entry);
            }
          }
        }
      }
    }
  }

  // Sort the entries by date if we have any
  if (result.rows.length > 0) {
    // Make sure the balance entry stays at the top
    const balanceEntries = result.rows.filter(row =>
      row.Details && row.Details.includes('Balance B/F')
    );

    const transactionEntries = result.rows.filter(row =>
      !row.Details || !row.Details.includes('Balance B/F')
    );

    // Sort transaction entries by date
    transactionEntries.sort((a, b) => {
      // Convert dates to comparable format
      const dateA = a.EntryDate.replace(/-/g, '');
      const dateB = b.EntryDate.replace(/-/g, '');
      return dateA.localeCompare(dateB);
    });

    // Combine balance entries and transaction entries
    result.rows = [...balanceEntries, ...transactionEntries];
  }

  return result;
}

/**
 * Parse CSV file and extract content with advanced features
 */
export async function parseCSV(filePath: string): Promise<FilePreviewData> {
  try {
    const stats = await statAsync(filePath);
    const fileName = path.basename(filePath);

    // Use csv-parser for robust CSV parsing
    const rows: any[] = [];
    const dataTypes: Record<string, Set<string>> = {};
    const statistics: Record<string, { min?: number, max?: number, sum: number, count: number }> = {};

    // Create a readable stream from the file
    const fileStream = fs.createReadStream(filePath);

    // Parse the CSV
    await new Promise<void>((resolve, reject) => {
      fileStream
        .pipe(csvParser())
        .on('data', (row: Record<string, string>) => {
          rows.push(row);

          // Analyze data types and collect statistics for each column
          Object.entries(row).forEach(([key, value]) => {
            // Initialize data type tracking for this column
            if (!dataTypes[key]) {
              dataTypes[key] = new Set();
            }

            // Determine data type
            const type = getValueType(value);
            dataTypes[key].add(type);

            // Collect statistics for numeric columns
            if (type === 'number') {
              const numValue = parseFloat(value as string);
              if (!statistics[key]) {
                statistics[key] = { min: numValue, max: numValue, sum: numValue, count: 1 };
              } else {
                statistics[key].min = Math.min(statistics[key].min!, numValue);
                statistics[key].max = Math.max(statistics[key].max!, numValue);
                statistics[key].sum += numValue;
                statistics[key].count++;
              }
            }
          });
        })
        .on('end', () => {
          resolve();
        })
        .on('error', (error: Error) => {
          reject(error);
        });
    });

    // Get headers from the first row
    const headers = rows.length > 0 ? Object.keys(rows[0]) : [];

    // Create a text preview
    const preview = rows.slice(0, 10).map(row =>
      headers.map(header => row[header]).join(',')
    ).join('\n');

    // Convert data type sets to strings
    const dataTypeSummary: Record<string, string> = {};
    Object.entries(dataTypes).forEach(([key, typeSet]) => {
      dataTypeSummary[key] = Array.from(typeSet).join('/');
    });

    // Calculate averages for numeric columns
    const statisticsSummary: Record<string, any> = {};
    Object.entries(statistics).forEach(([key, stats]) => {
      statisticsSummary[key] = {
        min: stats.min,
        max: stats.max,
        average: stats.sum / stats.count,
        count: stats.count
      };
    });

    return {
      metadata: {
        fileName,
        fileSize: stats.size,
        fileType: 'csv',
        lastModified: stats.mtime,
        mimeType: 'text/csv'
      },
      content: {
        headers,
        rows,
        totalRows: rows.length
      },
      preview,
      summary: {
        rowCount: rows.length,
        columnCount: headers.length,
        dataTypes: dataTypeSummary,
        statistics: statisticsSummary
      }
    };
  } catch (error) {
    console.error('Error parsing CSV:', error);

    // Fallback to basic parsing if csv-parser fails
    try {
      const fileBuffer = await readFileAsync(filePath, 'utf8');
      const stats = await statAsync(filePath);
      const fileName = path.basename(filePath);

      // Basic CSV parsing
      const lines = fileBuffer.split('\n');
      const headers = lines[0].split(',').map(header => header.trim());

      // Get a sample of rows for preview
      const rows = lines.slice(1, 11).map(line => {
        const values = line.split(',').map(value => value.trim());
        return headers.reduce((obj, header, index) => {
          obj[header] = values[index] || '';
          return obj;
        }, {} as Record<string, string>);
      });

      // Create a text preview
      const preview = lines.slice(0, 10).join('\n');

      return {
        metadata: {
          fileName,
          fileSize: stats.size,
          fileType: 'csv',
          lastModified: stats.mtime,
          mimeType: 'text/csv'
        },
        content: {
          headers,
          rows,
          totalRows: lines.length - 1
        },
        preview
      };
    } catch (fallbackError) {
      throw new Error(`Failed to parse CSV file: ${(error as Error).message || 'Unknown error'}`);
    }
  }
}

/**
 * Parse Excel (XLSX/XLS) file and extract content with advanced features
 */
export async function parseExcel(filePath: string): Promise<FilePreviewData> {
  try {
    const stats = await statAsync(filePath);
    const fileName = path.basename(filePath);
    const fileExt = path.extname(filePath).toLowerCase();

    // Read the Excel file with more options for better parsing
    const workbook = XLSX.readFile(filePath, {
      cellFormula: true,  // Parse formulas
      cellHTML: false,    // Don't generate HTML
      cellText: true,     // Generate formatted text
      cellStyles: true,   // Parse styles
      cellDates: true,    // Convert dates
      dateNF: 'yyyy-mm-dd', // Date format
      raw: false          // Don't use raw values
    });

    // Get all sheet names
    const sheetNames = workbook.SheetNames;

    // Process each sheet
    const sheets: Record<string, any[]> = {};
    const rawSheets: Record<string, any> = {}; // Store raw worksheet data
    const dataTypes: Record<string, Record<string, Set<string>>> = {};
    const statistics: Record<string, Record<string, { min?: number, max?: number, sum: number, count: number }>> = {};

    // Get sheet dimensions for each sheet
    const sheetDimensions: Record<string, { startRow: number, endRow: number, startCol: number, endCol: number }> = {};

    sheetNames.forEach(sheetName => {
      // Get the worksheet
      const worksheet = workbook.Sheets[sheetName];

      // Store raw worksheet data
      rawSheets[sheetName] = worksheet;

      // Get sheet dimensions
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      sheetDimensions[sheetName] = {
        startRow: range.s.r,
        endRow: range.e.r,
        startCol: range.s.c,
        endCol: range.e.c
      };

      // Convert to JSON with header: 1 option to get array of arrays
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // Try to detect header row by looking for the first row with multiple columns
      let headerRowIndex = 0;
      for (let i = 0; i < Math.min(10, jsonData.length); i++) {
        const row = jsonData[i] as any[];
        if (row && row.length > 1 && row.some(cell => typeof cell === 'string' && cell.trim() !== '')) {
          headerRowIndex = i;
          break;
        }
      }

      // Now convert to JSON with the detected header row
      const jsonDataWithHeaders = XLSX.utils.sheet_to_json(worksheet, {
        range: headerRowIndex,
        defval: '' // Use empty string for empty cells
      });

      sheets[sheetName] = jsonDataWithHeaders;

      // Initialize data type tracking for this sheet
      dataTypes[sheetName] = {};
      statistics[sheetName] = {};

      // Analyze data types and collect statistics
      if (jsonDataWithHeaders.length > 0) {
        const firstRow = jsonDataWithHeaders[0] as Record<string, unknown>;
        const headers = Object.keys(firstRow);

        (jsonDataWithHeaders as Record<string, unknown>[]).forEach((row: Record<string, unknown>) => {
          headers.forEach(header => {
            const value = row[header];

            // Initialize data type tracking for this column
            if (!dataTypes[sheetName][header]) {
              dataTypes[sheetName][header] = new Set();
            }

            // Determine data type
            const type = getValueType(value);
            dataTypes[sheetName][header].add(type);

            // Collect statistics for numeric columns
            if (type === 'number') {
              const numValue = parseFloat(value as string);
              if (!isNaN(numValue)) {
                if (!statistics[sheetName][header]) {
                  statistics[sheetName][header] = { min: numValue, max: numValue, sum: numValue, count: 1 };
                } else {
                  statistics[sheetName][header].min = Math.min(statistics[sheetName][header].min!, numValue);
                  statistics[sheetName][header].max = Math.max(statistics[sheetName][header].max!, numValue);
                  statistics[sheetName][header].sum += numValue;
                  statistics[sheetName][header].count++;
                }
              }
            }
          });
        });
      }
    });

    // Create a formatted preview using the raw worksheet data
    let preview = '';
    // Array to store preview rows for later use
    const allPreviewRows: string[][] = [];

    if (sheetNames.length > 0) {
      const firstSheetName = sheetNames[0];
      const worksheet = rawSheets[firstSheetName];
      const dimensions = sheetDimensions[firstSheetName];

      // Get column widths for better formatting
      const colWidths: number[] = [];

      // Process all rows in the file - no limit
      // This ensures we capture all data for large Excel files
      for (let r = dimensions.startRow; r <= dimensions.endRow; r++) {
        const rowData: string[] = [];

        // Process each column
        for (let c = dimensions.startCol; c <= dimensions.endCol; c++) {
          const cellRef = XLSX.utils.encode_cell({ r, c });
          const cell = worksheet[cellRef];

          let cellValue = '';
          if (cell) {
            // Get formatted value
            cellValue = XLSX.utils.format_cell(cell) || '';

            // Update column width
            if (colWidths[c] === undefined || cellValue.length > colWidths[c]) {
              colWidths[c] = cellValue.length;
            }
          }

          rowData.push(cellValue);
        }

        allPreviewRows.push(rowData);
      }

      // Format the preview with proper spacing
      preview = allPreviewRows.map(row =>
        row.map((cell, idx) => cell.padEnd(colWidths[idx + dimensions.startCol] || 10)).join('\t')
      ).join('\n');
    }

    // Convert data type sets to strings
    const dataTypeSummary: Record<string, Record<string, string>> = {};
    Object.entries(dataTypes).forEach(([sheet, columns]) => {
      dataTypeSummary[sheet] = {};
      Object.entries(columns).forEach(([column, typeSet]) => {
        dataTypeSummary[sheet][column] = Array.from(typeSet).join('/');
      });
    });

    // Calculate averages for numeric columns
    const statisticsSummary: Record<string, Record<string, any>> = {};
    Object.entries(statistics).forEach(([sheet, columns]) => {
      statisticsSummary[sheet] = {};
      Object.entries(columns).forEach(([column, stats]) => {
        statisticsSummary[sheet][column] = {
          min: stats.min,
          max: stats.max,
          average: stats.sum / stats.count,
          count: stats.count
        };
      });
    });

    // Get total row count across all sheets
    const totalRows = Object.values(sheets).reduce((sum, sheet) => sum + sheet.length, 0);

    // Get max column count
    const maxColumns = Object.values(sheets).reduce((max, sheet) => {
      if (sheet.length > 0) {
        return Math.max(max, Object.keys(sheet[0]).length);
      }
      return max;
    }, 0);

    // Extract bank statement specific information if possible
    let bankInfo = {
      bankName: '',
      accountNumber: '',
      dateRange: '',
      openingBalance: null as number | null,
      closingBalance: null as number | null
    };

    // Try to extract bank statement information from the first sheet
    if (sheetNames.length > 0 && allPreviewRows.length > 0) {
      // Look for bank name in the first few rows
      for (let i = 0; i < Math.min(5, allPreviewRows.length); i++) {
        const rowText = allPreviewRows[i].join(' ');
        if (rowText.includes('BANK') || rowText.includes('MICROFINANCE')) {
          bankInfo.bankName = rowText.trim();
          break;
        }
      }

      // Look for account number
      for (let i = 0; i < Math.min(10, allPreviewRows.length); i++) {
        const rowText = allPreviewRows[i].join(' ');
        if (rowText.includes('ACCOUNT') || rowText.includes('A/C') || rowText.includes('ACC')) {
          const match = rowText.match(/\d{5,}/);
          if (match) {
            bankInfo.accountNumber = match[0];
          }
          break;
        }
      }

      // Look for date range
      for (let i = 0; i < Math.min(10, allPreviewRows.length); i++) {
        const rowText = allPreviewRows[i].join(' ');
        if (rowText.includes('DATE RANGE') || rowText.includes('PERIOD') || rowText.includes('FROM')) {
          const dateMatch = rowText.match(/\d{1,2}[-\/]\w{3}[-\/]\d{2,4}/g);
          if (dateMatch && dateMatch.length >= 2) {
            bankInfo.dateRange = `${dateMatch[0]} to ${dateMatch[1]}`;
          }
          break;
        }
      }
    }

    return {
      metadata: {
        fileName,
        fileSize: stats.size,
        fileType: fileExt === '.xlsx' ? 'xlsx' : 'xls',
        lastModified: stats.mtime,
        mimeType: fileExt === '.xlsx'
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'application/vnd.ms-excel',
        title: bankInfo.bankName || undefined,
        subject: bankInfo.accountNumber ? `Account: ${bankInfo.accountNumber}` : undefined,
        keywords: bankInfo.dateRange ? [`Date Range: ${bankInfo.dateRange}`] : undefined
      },
      content: {
        sheets: sheetNames,
        data: sheets,
        rawPreview: allPreviewRows,
        dimensions: sheetDimensions,
        bankInfo
      },
      preview,
      summary: {
        rowCount: totalRows,
        columnCount: maxColumns,
        sheetCount: sheetNames.length,
        dataTypes: dataTypeSummary,
        statistics: statisticsSummary
      }
    };
  } catch (error) {
    console.error('Error parsing Excel:', error);

    // Fallback to basic metadata if parsing fails
    try {
      const stats = await statAsync(filePath);
      const fileName = path.basename(filePath);
      const fileExt = path.extname(filePath).toLowerCase();

      return {
        metadata: {
          fileName,
          fileSize: stats.size,
          fileType: fileExt === '.xlsx' ? 'xlsx' : 'xls',
          lastModified: stats.mtime,
          mimeType: fileExt === '.xlsx'
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/vnd.ms-excel'
        },
        content: {
          sheets: ['Sheet1'],
          data: {}
        },
        preview: "Excel parsing failed. The file may be corrupted or in an unsupported format."
      };
    } catch (fallbackError) {
      throw new Error(`Failed to parse Excel file: ${(error as Error).message || 'Unknown error'}`);
    }
  }
}

/**
 * Determine the type of a value
 */
function getValueType(value: any): string {
  if (value === null || value === undefined) {
    return 'null';
  }

  if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
    return 'number';
  }

  if (typeof value === 'boolean' || (typeof value === 'string' && (value.toLowerCase() === 'true' || value.toLowerCase() === 'false'))) {
    return 'boolean';
  }

  if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
    return 'date';
  }

  return 'string';
}

/**
 * Parse file based on file type with intelligent detection
 */
export async function parseFile(filePath: string): Promise<FilePreviewData> {
  try {
    // Detect MIME type for more accurate file type detection
    const mimeType = await detectMimeType(filePath);
    const fileExt = path.extname(filePath).toLowerCase();

    // Determine parser based on MIME type and extension
    if (mimeType === 'application/pdf' || fileExt === '.pdf') {
      return parsePDF(filePath);
    } else if (mimeType === 'text/csv' || fileExt === '.csv') {
      return parseCSV(filePath);
    } else if (
      mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      mimeType === 'application/vnd.ms-excel' ||
      fileExt === '.xlsx' ||
      fileExt === '.xls'
    ) {
      return parseExcel(filePath);
    } else {
      // Try to determine file type by examining content
      try {
        // Read a sample of the file
        const buffer = await readFileAsync(filePath, { encoding: null });
        const sample = buffer.subarray(0, 1000).toString('utf8');

        // Check for CSV-like content
        if (sample.split('\n').some(line => line.includes(','))) {
          return parseCSV(filePath);
        }

        // If we can't determine the type, throw an error
        throw new Error(`Unsupported file type: ${fileExt} (${mimeType})`);
      } catch (error) {
        throw new Error(`Unsupported file type: ${fileExt} (${mimeType})`);
      }
    }
  } catch (error) {
    console.error('Error parsing file:', error);
    throw error;
  }
}
