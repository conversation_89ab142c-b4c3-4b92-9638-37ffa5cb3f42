import express from 'express';
import {
  getAllReconciliations,
  getReconciliationById,
  createReconciliation,
  getReconciliationTransactions,
  completeReconciliation
} from '../controllers/reconciliation.controller';

const router = express.Router();

// @route   GET /api/reconciliations
// @desc    Get all reconciliations
// @access  Private
router.get('/', getAllReconciliations);

// @route   GET /api/reconciliations/:id
// @desc    Get reconciliation by ID
// @access  Private
router.get('/:id', getReconciliationById);

// @route   POST /api/reconciliations
// @desc    Create a new reconciliation
// @access  Private
router.post('/', createReconciliation);

// @route   GET /api/reconciliations/:id/transactions
// @desc    Get transactions for a reconciliation
// @access  Private
router.get('/:id/transactions', getReconciliationTransactions);

// @route   POST /api/reconciliations/:id/complete
// @desc    Complete a reconciliation
// @access  Private
router.post('/:id/complete', completeReconciliation);

export default router;
