#!/usr/bin/env python
"""
PDF Bridge - A bridge between TypeScript and Python for PDF table extraction

This script serves as a bridge between the TypeScript backend and Python PDF extraction.
It takes a PDF file path as input, extracts tables and text using the pdf_extractor module,
and returns the result as JSON.

Usage:
    python pdf_bridge.py <path-to-pdf-file>
"""

import os
import sys
import json
import traceback

# Simple version for testing
def main():
    """Main entry point for the script."""
    # Check if a PDF file path was provided
    if len(sys.argv) < 2:
        result = {
            'success': False,
            'error': 'No PDF file path provided'
        }
        print(json.dumps(result))
        return

    # Get the PDF file path from command line arguments
    pdf_path = sys.argv[1]

    # For testing, just return basic info about the file
    if os.path.exists(pdf_path):
        result = {
            'success': True,
            'text': 'This is sample text from the PDF',
            'tables': [
                {
                    'headers': ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'],
                    'rows': [
                        {
                            'EntryDate': '01-JAN-23',
                            'Details': 'Sample transaction',
                            'ValueDate': '01-JAN-23',
                            'Debit': '',
                            'Credit': '1000.00',
                            'Balance': '1000.00'
                        }
                    ]
                }
            ],
            'metadata': {
                'fileName': os.path.basename(pdf_path),
                'fileSize': os.path.getsize(pdf_path),
                'pageCount': 1,
                'bankName': 'Test Bank',
                'accountNumber': '**********'
            }
        }
    else:
        result = {
            'success': False,
            'error': f'File not found: {pdf_path}'
        }

    # Output the result as JSON
    print(json.dumps(result))

if __name__ == '__main__':
    main()
