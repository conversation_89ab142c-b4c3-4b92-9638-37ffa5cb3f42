/**
 * API Routes Configuration
 *
 * This file contains all API routes used in the application.
 * Frontend can import these routes to ensure consistency between frontend and backend.
 */

// Base API URL - should match the one used in frontend
const API_BASE_URL = 'http://localhost:8080/api/v1';

// Auth Routes
const AUTH_ROUTES = {
  LOGIN: `${API_BASE_URL}/auth/login`,
  REGISTER: `${API_BASE_URL}/auth/student-registeration`,
  GET_USER: (userId) => `${API_BASE_URL}/auth/${userId}/get-user`,
};

// User Routes
const USER_ROUTES = {
  GET_EMPLOYEES: `${API_BASE_URL}/user/employees`,
  GET_USER_PROFILE: (userId) => `${API_BASE_URL}/user/${userId}`,
  UPDATE_USER_PROFILE: (userId) => `${API_BASE_URL}/user/${userId}`,
};

// Dashboard Routes
const DASHBOARD_ROUTES = {
  GET_STUDENT_DASHBOARD: (userId) => `${API_BASE_URL}/student/dashboard/${userId}`,
  ADD_LEARNING_GOAL: (userId) => `${API_BASE_URL}/student/dashboard/${userId}/goals`,
  UPDATE_LEARNING_GOAL: (userId, goalId) => `${API_BASE_URL}/student/dashboard/${userId}/goals/${goalId}`,
  DELETE_LEARNING_GOAL: (userId, goalId) => `${API_BASE_URL}/student/dashboard/${userId}/goals/${goalId}`,
};

// Accounting Routes
const ACCOUNTING_ROUTES = {
  GET_FINANCIAL_PROJECTS: `${API_BASE_URL}/accounting/financial-projects`,
  GET_FINANCIAL_PROJECT: (id) => `${API_BASE_URL}/accounting/financial-projects/${id}`,
  CREATE_FINANCIAL_PROJECT: `${API_BASE_URL}/accounting/financial-projects`,
  UPDATE_FINANCIAL_PROJECT: (id) => `${API_BASE_URL}/accounting/financial-projects/${id}`,
  DELETE_FINANCIAL_PROJECT: (id) => `${API_BASE_URL}/accounting/financial-projects/${id}`,
};

// Bank Routes
const BANK_ROUTES = {
  ANALYZE_STATEMENT: `${API_BASE_URL}/bank/analyze-statement`,
};

// Analytics Routes
const ANALYTICS_ROUTES = {
  POST_ANALYTICS: `${API_BASE_URL}/analytics`,
  TRACK_SHARE: `${API_BASE_URL}/track-share`,
};

// Course Routes
const COURSE_ROUTES = {
  GET_ALL_COURSES: `${API_BASE_URL}/courses`,
  GET_COURSE: (id) => `${API_BASE_URL}/courses/${id}`,
  CREATE_COURSE: `${API_BASE_URL}/courses`,
  UPDATE_COURSE: (id) => `${API_BASE_URL}/courses/${id}`,
  DELETE_COURSE: (id) => `${API_BASE_URL}/courses/${id}`,
  GET_COURSE_ENROLLMENTS: (id) => `${API_BASE_URL}/courses/${id}/enrollments`,
};

// Enrollment Routes
const ENROLLMENT_ROUTES = {
  GET_USER_ENROLLMENTS: (userId) => `${API_BASE_URL}/enrollments/user/${userId}`,
  ENROLL_IN_COURSE: `${API_BASE_URL}/enrollments`,
  UPDATE_ENROLLMENT: (id) => `${API_BASE_URL}/enrollments/${id}`,
};

// Scholarship Routes
const SCHOLARSHIP_ROUTES = {
  APPLY: `${API_BASE_URL}/scholarships/apply`,
  GET_ALL: `${API_BASE_URL}/scholarships`,
  GET_BY_ID: (id) => `${API_BASE_URL}/scholarships/${id}`,
  GET_USER_APPLICATIONS: (userId) => `${API_BASE_URL}/scholarships/user/${userId}`,
};

// Internship Routes
const INTERNSHIP_ROUTES = {
  APPLY: `${API_BASE_URL}/internships/apply`,
  GET_ALL: `${API_BASE_URL}/internships`,
  GET_BY_ID: (id) => `${API_BASE_URL}/internships/${id}`,
  GET_USER_APPLICATIONS: (userId) => `${API_BASE_URL}/internships/user/${userId}`,
};

// Blog Routes
const BLOG_ROUTES = {
  GET_ALL_POSTS: `${API_BASE_URL}/blog`,
  GET_POST: (id) => `${API_BASE_URL}/blog/${id}`,
  CREATE_POST: `${API_BASE_URL}/blog`,
  UPDATE_POST: (id) => `${API_BASE_URL}/blog/${id}`,
  DELETE_POST: (id) => `${API_BASE_URL}/blog/${id}`,
};

// Export all routes
export {
  API_BASE_URL,
  AUTH_ROUTES,
  USER_ROUTES,
  DASHBOARD_ROUTES,
  ACCOUNTING_ROUTES,
  BANK_ROUTES,
  ANALYTICS_ROUTES,
  COURSE_ROUTES,
  ENROLLMENT_ROUTES,
  SCHOLARSHIP_ROUTES,
  INTERNSHIP_ROUTES,
  BLOG_ROUTES,
};
