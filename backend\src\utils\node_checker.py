"""
Node.js checker utility.
"""

import subprocess
import os
import sys

def check_node_installed():
    """
    Check if Node.js is installed and available.
    
    Returns:
        bool: True if Node.js is installed, False otherwise
    """
    try:
        # Try to run 'node --version'
        result = subprocess.run(['node', '--version'], 
                               stdout=subprocess.PIPE, 
                               stderr=subprocess.PIPE,
                               check=False)
        
        # Check if the command was successful
        if result.returncode == 0:
            version = result.stdout.decode('utf-8').strip()
            print(f"Node.js is installed: {version}")
            return True
        else:
            print("Node.js is not installed or not in PATH")
            return False
    except Exception as e:
        print(f"Error checking Node.js: {str(e)}")
        return False

def install_node_instructions():
    """
    Print instructions for installing Node.js.
    """
    print("\nTo use the JavaScript engine for Excel and CSV parsing, Node.js must be installed.")
    print("\nInstallation instructions:")
    
    if sys.platform.startswith('win'):
        print("Windows:")
        print("1. Download the installer from https://nodejs.org/")
        print("2. Run the installer and follow the instructions")
        print("3. Restart your computer")
    elif sys.platform.startswith('darwin'):
        print("macOS:")
        print("1. Using Homebrew: brew install node")
        print("2. Or download the installer from https://nodejs.org/")
    else:
        print("Linux:")
        print("1. Using apt: sudo apt install nodejs npm")
        print("2. Using yum: sudo yum install nodejs npm")
        print("3. Or download from https://nodejs.org/")
    
    print("\nAfter installation, restart your application.")

if __name__ == "__main__":
    # Check if Node.js is installed when this script is run directly
    if check_node_installed():
        print("Node.js is available for the JavaScript engine.")
    else:
        print("Node.js is not available. The application will fall back to Python implementations.")
        install_node_instructions()
