import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const Spinner = () => (
  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
);

const ForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [email, setEmail] = useState('');

  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email) {
      setError('Please enter your email address.');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch(`${apiBaseUrl}/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.message || 'Failed to process request');
        return;
      }

      setSuccess('Password reset instructions have been sent to your email.');
      setEmail('');
    } catch (error) {
      console.error('Forgot password error:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-b from-purple-100 to-white">
      <div className="bg-white/70 backdrop-blur-md rounded-lg shadow-2xl p-8 max-w-md w-full border border-white/50">
        <h2 className="text-2xl font-bold mb-6 text-center text-[#412D6C]">Forgot Password</h2>

        {error && (
          <div className="p-3 bg-red-100 text-red-700 rounded-lg text-sm mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="p-3 bg-green-100 text-green-700 rounded-lg text-sm mb-4">
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <input
              type="email"
              placeholder="Email"
              className="w-full p-3 border border-gray-300 bg-white/50 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-[#412D6C] text-white py-3 rounded-lg hover:bg-[#362659] transition-colors flex items-center justify-center cursor-pointer"
          >
            {loading ? <Spinner /> : 'Reset Password'}
          </button>

          <div className="text-center mt-4">
            <p className="text-gray-600">
              Remember your password?{' '}
              <Link to="/login" className="text-[#412D6C] font-medium hover:underline">
                Back to Login
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ForgotPassword;
