import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import useStore from '../../store/useStore';

const Spinner = () => (
  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
);

const Signup = () => {
  // Removed navigate for troubleshooting
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user' // Default role for new users
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const setUser = useStore((state) => state.setUser);

  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

  // Simple function to get dashboard route based on role
  const getDashboardRoute = (role) => {
    // For the bank reconciliation app, all users go to the same dashboard
    return '/dashboard';
  };

  const handleSignup = async (e) => {
    e.preventDefault();

    if (!formData.firstName || !formData.lastName ||
        !formData.email || !formData.password || !formData.confirmPassword) {
      setError('Please fill in all fields.');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('Sending registration request to:', `${apiBaseUrl}/auth/register`);

      const response = await fetch(`${apiBaseUrl}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          password: formData.password,
          role: formData.role
        }),
      });

      const data = await response.json();
      console.log('Registration response:', data);

      if (!response.ok) {
        setError(data.error || data.message || 'Registration failed');
        console.error('Registration failed:', data);
        return;
      }

      // Automatically log in after successful registration
      console.log('Sending login after registration request to:', `${apiBaseUrl}/auth/login`);

      const loginResponse = await fetch(`${apiBaseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          email: formData.email,
          password: formData.password
        }),
      });

      const loginData = await loginResponse.json();
      console.log('Login after registration response:', loginData);

      if (!loginResponse.ok) {
        setError('Registration successful but login failed. Please login manually.');
        console.error('Login after registration failed:', loginData);
        // Redirect to login page
        window.location.href = '/login';
        return;
      }

      // Store token and user ID in localStorage
      localStorage.setItem('authToken', loginData.token);
      localStorage.setItem('userId', loginData.data.id);
      localStorage.setItem('lastLoginTime', Date.now().toString());

      // No need to fetch user data separately as it's already included in the login response
      const userData = loginData.data;

      // Update Zustand store with user data
      setUser({
        id: loginData.data.id,
        firstName: loginData.data.firstName,
        lastName: loginData.data.lastName,
        email: loginData.data.email,
        role: loginData.data.role,
        token: loginData.token
      });

      // Log success information
      console.log('=== SIGNUP SUCCESSFUL ===');
      console.log('User data:', userData);
      console.log('Token stored in localStorage');

      // Redirect to dashboard
      window.location.href = getDashboardRoute(loginData.data.role);

    } catch (error) {
      console.error('Signup error:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-b from-purple-100 to-white">
      <div className="bg-white/70 backdrop-blur-md rounded-lg shadow-2xl p-8 max-w-md w-full border border-white/50">
        <h2 className="text-2xl font-bold mb-6 text-center text-[#412D6C]">Create an Account</h2>

        {error && (
          <div className={`p-3 ${error.startsWith('✅') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'} rounded-lg text-sm mb-4`}>
            {error}
          </div>
        )}

        <form onSubmit={handleSignup} className="space-y-4">
          <div>
            <input
              type="text"
              placeholder="First Name"
              className="w-full p-3 border border-gray-300 bg-white/50 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              value={formData.firstName}
              onChange={(e) => setFormData({...formData, firstName: e.target.value})}
            />
          </div>

          <div>
            <input
              type="text"
              placeholder="Last Name"
              className="w-full p-3 border border-gray-300 bg-white/50 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              value={formData.lastName}
              onChange={(e) => setFormData({...formData, lastName: e.target.value})}
            />
          </div>



          <div>
            <input
              type="email"
              placeholder="Email"
              className="w-full p-3 border border-gray-300 bg-white/50 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
            />
          </div>

          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              className="w-full p-3 border border-gray-300 bg-white/50 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 cursor-pointer"
            >
              {showPassword ? <FaEyeSlash size={20} /> : <FaEye size={20} />}
            </button>
          </div>

          <div className="relative">
            <input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm Password"
              className="w-full p-3 border border-gray-300 bg-white/50 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              value={formData.confirmPassword}
              onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 cursor-pointer"
            >
              {showConfirmPassword ? <FaEyeSlash size={20} /> : <FaEye size={20} />}
            </button>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-[#412D6C] text-white py-3 rounded-lg hover:bg-[#362659] transition-colors flex items-center justify-center cursor-pointer"
          >
            {loading ? <Spinner /> : 'Sign Up'}
          </button>

          <div className="text-center mt-4">
            <p className="text-gray-600">
              Already have an account?{' '}
              <Link to="/login" className="text-[#412D6C] font-medium hover:underline">
                Login
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Signup;
