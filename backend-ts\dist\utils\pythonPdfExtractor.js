"use strict";
/**
 * Python PDF Table Extractor Wrapper
 *
 * This module provides a TypeScript wrapper for the Python PDF table extractor.
 * It spawns a Python process to extract tables from PDF files and returns the result.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractTablesWithPython = extractTablesWithPython;
const child_process_1 = require("child_process");
const path_1 = __importDefault(require("path"));
const util_1 = require("util");
const fs_1 = __importDefault(require("fs"));
// Convert fs functions to Promise-based
const statAsync = (0, util_1.promisify)(fs_1.default.stat);
/**
 * Extract tables from PDF using the Python PDF table extractor
 */
async function extractTablesWithPython(filePath) {
    try {
        // Check if the file exists
        await statAsync(filePath);
        // Path to the Python script (relative to the project root)
        const scriptPath = path_1.default.resolve(process.cwd(), '../pdf_bridge.py');
        return new Promise((resolve, reject) => {
            // Spawn a Python process
            const pythonProcess = (0, child_process_1.spawn)('python', [scriptPath, filePath]);
            let outputData = '';
            let errorData = '';
            // Collect data from stdout
            pythonProcess.stdout.on('data', (data) => {
                outputData += data.toString();
            });
            // Collect data from stderr
            pythonProcess.stderr.on('data', (data) => {
                errorData += data.toString();
            });
            // Handle process completion
            pythonProcess.on('close', (code) => {
                if (code !== 0) {
                    console.error('Python process exited with code', code);
                    console.error('Error output:', errorData);
                    reject(new Error(`Python process failed with code ${code}: ${errorData}`));
                    return;
                }
                try {
                    // Parse the JSON output
                    const result = JSON.parse(outputData);
                    if (!result.success) {
                        reject(new Error(result.error || 'Unknown error in Python script'));
                        return;
                    }
                    // Convert to the expected format
                    const extractedData = {
                        text: result.text || '',
                        tables: result.tables || [],
                        bankInfo: {
                            bankName: '',
                            address: [],
                            recipient: []
                        },
                        metadata: result.metadata || {}
                    };
                    resolve(extractedData);
                }
                catch (error) {
                    console.error('Error parsing Python output:', error);
                    reject(error);
                }
            });
        });
    }
    catch (error) {
        console.error('Error extracting tables with Python:', error);
        throw error;
    }
}
exports.default = {
    extractTablesWithPython
};
