import { API_BASE_URL } from '../apiRoutes';
import axios from 'axios';

// Get application status directly without using the api instance
// This will allow us to handle 404 responses properly
export const getApplicationStatus = async (userId, applicationType) => {
  try {
    // Get the auth token
    const token = localStorage.getItem('authToken');

    if (!token) {
      return {
        status: false,
        error: 'Authentication required. Please log in to check your application status.'
      };
    }

    // Make a direct axios request with the token
    const response = await axios.get(
      `${API_BASE_URL}/applications/status/${userId}/${applicationType}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        // This is important - it tells axios to resolve the promise even for error status codes
        validateStatus: function (status) {
          return status >= 200 && status < 500; // Resolve for any status code less than 500
        }
      }
    );

    // If the response indicates not found (either through status code or notFound flag)
    if (response.status === 404 || (response.data && response.data.notFound)) {
      return {
        status: false,
        notFound: true,
        message: response.data.message || 'APPLICATION_NOT_FOUND',
        meta: response.data.meta || {
          error: 'No application found for this user and type.',
          suggestions: ['Check the user ID and application type.']
        }
      };
    }

    // For successful responses
    if (response.status === 200 && response.data.status) {
      return {
        status: true,
        data: response.data.data
      };
    }

    // For other non-success responses
    return {
      status: false,
      error: response.data.meta?.error || response.data.message || 'Failed to get application status'
    };
  } catch (error) {
    console.error('Error getting application status:', error);

    // Handle network errors or other exceptions
    return {
      status: false,
      error: 'Network error or server unavailable. Please try again later.'
    };
  }
};

// Export the service as an object
export const applicationService = {
  getApplicationStatus
};

export default applicationService;
