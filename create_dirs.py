import os

# Create directory structure
directories = [
    'backend/src/config',
    'backend/src/controllers',
    'backend/src/middleware',
    'backend/src/models',
    'backend/src/routes',
    'backend/src/services',
    'backend/src/utils'
]

for directory in directories:
    os.makedirs(directory, exist_ok=True)
    print(f"Created directory: {directory}")

print("Directory structure created successfully!")
