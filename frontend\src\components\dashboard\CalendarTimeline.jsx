import React from 'react';

const CalendarTimeline = ({ events = [] }) => {
  // Sort events by date with safety check
  const sortedEvents = Array.isArray(events)
    ? [...events].sort((a, b) => new Date(a?.date || 0) - new Date(b?.date || 0))
    : [];

  // Group events by month with safety checks
  const groupedEvents = sortedEvents.reduce((groups, event) => {
    if (!event?.date) return groups;

    const date = new Date(event.date);
    const monthYear = `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;

    if (!groups[monthYear]) {
      groups[monthYear] = [];
    }

    groups[monthYear].push(event);
    return groups;
  }, {});

  return (
    <div className="bg-white/30 backdrop-blur-xl p-6 rounded-xl shadow-lg border border-white/20">
      <h3 className="text-[#412D6C] font-semibold mb-4">Upcoming Deadlines</h3>

      <div className="space-y-6">
        {Object.keys(groupedEvents).length > 0 ? (
          Object.entries(groupedEvents).map(([monthYear, monthEvents]) => {
            return (
              <div key={monthYear} className="space-y-3">
                <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider">{monthYear}</h4>

                <div className="space-y-3">
                  {monthEvents.map((event, index) => {
                    const eventDate = new Date(event.date);
                    const isUpcoming = new Date() < eventDate;
                    const isPast = new Date() > eventDate;
                    const isToday = new Date().toDateString() === eventDate.toDateString();

                    return (
                      <div
                        key={index}
                        className={`flex items-start p-3 rounded-lg border-l-4 ${
                          isPast ? 'border-gray-300 bg-gray-50/50' :
                          isToday ? 'border-yellow-400 bg-yellow-50/50' :
                          'border-[#412D6C] bg-purple-50/50'
                        }`}
                      >
                        <div className="flex-shrink-0 w-12 h-12 flex flex-col items-center justify-center bg-white rounded-lg shadow-sm mr-4">
                          <span className="text-lg font-bold">{eventDate.getDate()}</span>
                          <span className="text-xs text-gray-500">{eventDate.toLocaleString('default', { month: 'short' })}</span>
                        </div>

                        <div className="flex-1">
                          <div className="flex justify-between">
                            <h5 className={`font-medium ${isPast ? 'text-gray-500' : 'text-gray-800'}`}>
                              {event?.title || 'Untitled Event'}
                            </h5>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              isPast ? 'bg-gray-100 text-gray-500' :
                              isToday ? 'bg-yellow-100 text-yellow-800' :
                              'bg-purple-100 text-[#412D6C]'
                            }`}>
                              {isPast ? 'Past' : isToday ? 'Today' : `${getDaysUntil(eventDate)} days left`}
                            </span>
                          </div>

                          <p className="text-sm text-gray-600 mt-1">{event?.description || 'No description available'}</p>

                          {event?.course && (
                            <div className="mt-2 text-xs text-gray-500">
                              Course: <span className="font-medium">{event.course}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No upcoming deadlines or events.</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Helper function to calculate days until a date
const getDaysUntil = (date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const targetDate = new Date(date);
  targetDate.setHours(0, 0, 0, 0);

  const diffTime = targetDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
};

export default CalendarTimeline;
