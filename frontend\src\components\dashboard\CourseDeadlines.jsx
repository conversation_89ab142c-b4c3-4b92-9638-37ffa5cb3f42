import React, { useState } from 'react';

const CourseDeadlines = ({ courses = [] }) => {
  const [selectedCourse, setSelectedCourse] = useState('all');

  // Get all deadlines from all courses, with safety checks
  const allDeadlines = Array.isArray(courses)
    ? courses.flatMap(course =>
        Array.isArray(course?.deadlines)
          ? course.deadlines.map(deadline => ({
              ...deadline,
              courseId: course.id,
              courseTitle: course.title
            }))
          : []
      )
    : [];

  // Filter deadlines based on selected course
  const filteredDeadlines = selectedCourse === 'all'
    ? allDeadlines
    : allDeadlines.filter(deadline => deadline.courseId?.toString() === selectedCourse);

  // Sort deadlines by date
  const sortedDeadlines = [...filteredDeadlines].sort((a, b) => new Date(a.date) - new Date(b.date));

  return (
    <div className="bg-white/30 backdrop-blur-xl p-6 rounded-xl shadow-lg border border-white/20">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-[#412D6C] font-semibold">Course Deadlines</h3>

        <select
          value={selectedCourse}
          onChange={(e) => setSelectedCourse(e.target.value)}
          className="text-sm border border-gray-300 rounded-md px-3 py-1 bg-white"
        >
          <option value="all">All Courses</option>
          {Array.isArray(courses) && courses.map(course => (
            <option key={course.id} value={course.id?.toString()}>
              {course.title}
            </option>
          ))}
        </select>
      </div>

      {sortedDeadlines.length > 0 ? (
        <div className="space-y-4">
          {sortedDeadlines.map((deadline, index) => {
            const deadlineDate = new Date(deadline.date);
            const today = new Date();
            const isPast = deadlineDate < today;
            const isToday = deadlineDate.toDateString() === today.toDateString();
            const daysLeft = Math.ceil((deadlineDate - today) / (1000 * 60 * 60 * 24));

            return (
              <div
                key={index}
                className={`p-4 rounded-lg border-l-4 ${
                  isPast
                    ? 'border-gray-300 bg-gray-50/50'
                    : isToday
                      ? 'border-yellow-400 bg-yellow-50/50'
                      : daysLeft <= 3
                        ? 'border-red-400 bg-red-50/50'
                        : 'border-[#412D6C] bg-purple-50/50'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium text-gray-800">{deadline.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{deadline.description}</p>

                    {selectedCourse === 'all' && (
                      <div className="mt-1 text-xs text-[#412D6C]">
                        {deadline.courseTitle}
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col items-end">
                    <div className="text-sm font-medium">
                      {deadlineDate.toLocaleDateString()}
                    </div>

                    <div className={`mt-1 text-xs px-2 py-1 rounded-full ${
                      isPast
                        ? 'bg-gray-100 text-gray-500'
                        : isToday
                          ? 'bg-yellow-100 text-yellow-800'
                          : daysLeft <= 3
                            ? 'bg-red-100 text-red-800'
                            : 'bg-purple-100 text-[#412D6C]'
                    }`}>
                      {isPast
                        ? 'Past Due'
                        : isToday
                          ? 'Due Today'
                          : `${daysLeft} day${daysLeft !== 1 ? 's' : ''} left`}
                    </div>
                  </div>
                </div>

                {deadline.type && (
                  <div className="mt-2 flex items-center">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      deadline.type === 'assignment'
                        ? 'bg-blue-100 text-blue-800'
                        : deadline.type === 'quiz'
                          ? 'bg-green-100 text-green-800'
                          : deadline.type === 'project'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-gray-100 text-gray-800'
                    }`}>
                      {deadline.type.charAt(0).toUpperCase() + deadline.type.slice(1)}
                    </span>

                    {deadline.points && (
                      <span className="ml-2 text-xs text-gray-500">
                        {deadline.points} points
                      </span>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No deadlines found for the selected course.</p>
        </div>
      )}
    </div>
  );
};

export default CourseDeadlines;
