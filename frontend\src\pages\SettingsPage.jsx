import React, { useState, useEffect } from 'react';
import { useUser } from '../contexts/UserContext';
import { FaLock, FaBell, FaGlobe, FaShieldAlt, FaUserCircle } from 'react-icons/fa';
import Navbar from '../components/common/Navbar';
import Footer from '../components/common/Footer';
import axios from 'axios';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const SettingsPage = () => {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState('account');
  const [isLoading, setIsLoading] = useState(false);

  // Form states
  const [accountSettings, setAccountSettings] = useState({
    email: user?.email || '',
    username: user?.username || '',
    language: 'English',
    timezone: 'UTC+0'
  });

  const [passwordSettings, setPasswordSettings] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    courseUpdates: true,
    newMessages: true,
    marketingEmails: false
  });

  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: 'public',
    showEnrollments: true,
    showProgress: true
  });

  // Fetch user settings from the backend
  const fetchUserSettings = async () => {
    if (!user || !user.id) return;

    setIsLoading(true);
    try {
      const token = localStorage.getItem('authToken');
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/user/settings/${user.id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.data.status) {
        const settings = response.data.data;

        setAccountSettings({
          email: settings.email || user?.email || '',
          username: settings.username || user?.username || '',
          language: settings.language || 'English',
          timezone: settings.timezone || 'UTC+0'
        });

        if (settings.notificationSettings) {
          setNotificationSettings(settings.notificationSettings);
        }

        if (settings.privacySettings) {
          setPrivacySettings(settings.privacySettings);
        }
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      // If API fails, keep the default values
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUserSettings();
  }, [user]);

  const handleAccountChange = (e) => {
    const { name, value } = e.target;
    setAccountSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChange = (e) => {
    const { name, checked } = e.target;
    setNotificationSettings(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handlePrivacyChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPrivacySettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e, formType) => {
    e.preventDefault();

    if (!user || !user.id) {
      showErrorToast('User information not available. Please log in again.');
      return;
    }

    setIsLoading(true);
    try {
      const token = localStorage.getItem('authToken');
      let endpoint = '';
      let data = {};

      switch (formType) {
        case 'account':
          endpoint = `${process.env.REACT_APP_API_URL}/api/user/settings/account/${user.id}`;
          data = accountSettings;
          break;
        case 'password':
          endpoint = `${process.env.REACT_APP_API_URL}/api/user/settings/password/${user.id}`;
          data = passwordSettings;
          break;
        case 'notifications':
          endpoint = `${process.env.REACT_APP_API_URL}/api/user/settings/notifications/${user.id}`;
          data = notificationSettings;
          break;
        case 'privacy':
          endpoint = `${process.env.REACT_APP_API_URL}/api/user/settings/privacy/${user.id}`;
          data = privacySettings;
          break;
        default:
          throw new Error('Invalid form type');
      }

      const response = await axios.put(
        endpoint,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.data.status) {
        showSuccessToast(`${formType.charAt(0).toUpperCase() + formType.slice(1)} settings updated successfully!`);

        // Reset password fields if password was updated
        if (formType === 'password') {
          setPasswordSettings({
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
          });
        }

        // Refresh settings
        fetchUserSettings();
      } else {
        showErrorToast(response.data.message || `Failed to update ${formType} settings`);
      }
    } catch (error) {
      console.error(`Error updating ${formType} settings:`, error);
      showErrorToast(error.response?.data?.meta?.error || `An error occurred while updating your ${formType} settings`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Please log in to access settings</h2>
          <button
            onClick={() => window.location.href = '/'}
            className="bg-[#412D6C] text-white px-6 py-2 rounded-full hover:bg-[#362659] transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gray-100">
        <div className="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-8">Settings</h1>

          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="flex flex-col md:flex-row">
              {/* Sidebar */}
              <div className="w-full md:w-64 bg-gray-50 p-6 border-r border-gray-200">
                <nav className="space-y-2">
                  <button
                    onClick={() => setActiveTab('account')}
                    className={`flex items-center w-full px-4 py-3 rounded-md transition-colors ${
                      activeTab === 'account' ? 'bg-[#412D6C] text-white' : 'text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <FaUserCircle className="mr-3" />
                    Account
                  </button>
                  <button
                    onClick={() => setActiveTab('password')}
                    className={`flex items-center w-full px-4 py-3 rounded-md transition-colors ${
                      activeTab === 'password' ? 'bg-[#412D6C] text-white' : 'text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <FaLock className="mr-3" />
                    Password
                  </button>
                  <button
                    onClick={() => setActiveTab('notifications')}
                    className={`flex items-center w-full px-4 py-3 rounded-md transition-colors ${
                      activeTab === 'notifications' ? 'bg-[#412D6C] text-white' : 'text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <FaBell className="mr-3" />
                    Notifications
                  </button>
                  <button
                    onClick={() => setActiveTab('privacy')}
                    className={`flex items-center w-full px-4 py-3 rounded-md transition-colors ${
                      activeTab === 'privacy' ? 'bg-[#412D6C] text-white' : 'text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <FaShieldAlt className="mr-3" />
                    Privacy
                  </button>
                </nav>
              </div>

              {/* Content */}
              <div className="flex-1 p-6">
                {/* Account Settings */}
                {activeTab === 'account' && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-6">Account Settings</h2>
                    <form onSubmit={(e) => handleSubmit(e, 'account')}>
                      <div className="space-y-4 mb-6">
                        <div>
                          <label className="block text-gray-700 font-medium mb-2">Email</label>
                          <input
                            type="email"
                            name="email"
                            value={accountSettings.email}
                            onChange={handleAccountChange}
                            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-700 font-medium mb-2">Username</label>
                          <input
                            type="text"
                            name="username"
                            value={accountSettings.username}
                            onChange={handleAccountChange}
                            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-700 font-medium mb-2">Language</label>
                          <select
                            name="language"
                            value={accountSettings.language}
                            onChange={handleAccountChange}
                            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                          >
                            <option value="English">English</option>
                            <option value="Spanish">Spanish</option>
                            <option value="French">French</option>
                            <option value="German">German</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-gray-700 font-medium mb-2">Timezone</label>
                          <select
                            name="timezone"
                            value={accountSettings.timezone}
                            onChange={handleAccountChange}
                            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                          >
                            <option value="UTC+0">UTC+0 (London, Lisbon)</option>
                            <option value="UTC-5">UTC-5 (New York, Toronto)</option>
                            <option value="UTC-8">UTC-8 (Los Angeles, Vancouver)</option>
                            <option value="UTC+1">UTC+1 (Berlin, Paris)</option>
                            <option value="UTC+8">UTC+8 (Beijing, Singapore)</option>
                          </select>
                        </div>
                      </div>
                      <button
                        type="submit"
                        className="bg-[#412D6C] text-white px-6 py-2 rounded-md hover:bg-[#362659] transition-colors"
                      >
                        Save Changes
                      </button>
                    </form>
                  </div>
                )}

                {/* Password Settings */}
                {activeTab === 'password' && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-6">Change Password</h2>
                    <form onSubmit={(e) => handleSubmit(e, 'password')}>
                      <div className="space-y-4 mb-6">
                        <div>
                          <label className="block text-gray-700 font-medium mb-2">Current Password</label>
                          <input
                            type="password"
                            name="currentPassword"
                            value={passwordSettings.currentPassword}
                            onChange={handlePasswordChange}
                            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-700 font-medium mb-2">New Password</label>
                          <input
                            type="password"
                            name="newPassword"
                            value={passwordSettings.newPassword}
                            onChange={handlePasswordChange}
                            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-700 font-medium mb-2">Confirm New Password</label>
                          <input
                            type="password"
                            name="confirmPassword"
                            value={passwordSettings.confirmPassword}
                            onChange={handlePasswordChange}
                            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                          />
                        </div>
                      </div>
                      <button
                        type="submit"
                        className="bg-[#412D6C] text-white px-6 py-2 rounded-md hover:bg-[#362659] transition-colors"
                      >
                        Update Password
                      </button>
                    </form>
                  </div>
                )}

                {/* Notification Settings */}
                {activeTab === 'notifications' && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-6">Notification Settings</h2>
                    <form onSubmit={(e) => handleSubmit(e, 'notifications')}>
                      <div className="space-y-4 mb-6">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="emailNotifications"
                            name="emailNotifications"
                            checked={notificationSettings.emailNotifications}
                            onChange={handleNotificationChange}
                            className="h-4 w-4 text-[#412D6C] focus:ring-[#412D6C] border-gray-300 rounded"
                          />
                          <label htmlFor="emailNotifications" className="ml-2 block text-gray-700">
                            Email Notifications
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="courseUpdates"
                            name="courseUpdates"
                            checked={notificationSettings.courseUpdates}
                            onChange={handleNotificationChange}
                            className="h-4 w-4 text-[#412D6C] focus:ring-[#412D6C] border-gray-300 rounded"
                          />
                          <label htmlFor="courseUpdates" className="ml-2 block text-gray-700">
                            Course Updates
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="newMessages"
                            name="newMessages"
                            checked={notificationSettings.newMessages}
                            onChange={handleNotificationChange}
                            className="h-4 w-4 text-[#412D6C] focus:ring-[#412D6C] border-gray-300 rounded"
                          />
                          <label htmlFor="newMessages" className="ml-2 block text-gray-700">
                            New Messages
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="marketingEmails"
                            name="marketingEmails"
                            checked={notificationSettings.marketingEmails}
                            onChange={handleNotificationChange}
                            className="h-4 w-4 text-[#412D6C] focus:ring-[#412D6C] border-gray-300 rounded"
                          />
                          <label htmlFor="marketingEmails" className="ml-2 block text-gray-700">
                            Marketing Emails
                          </label>
                        </div>
                      </div>
                      <button
                        type="submit"
                        className="bg-[#412D6C] text-white px-6 py-2 rounded-md hover:bg-[#362659] transition-colors"
                      >
                        Save Preferences
                      </button>
                    </form>
                  </div>
                )}

                {/* Privacy Settings */}
                {activeTab === 'privacy' && (
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-6">Privacy Settings</h2>
                    <form onSubmit={(e) => handleSubmit(e, 'privacy')}>
                      <div className="space-y-4 mb-6">
                        <div>
                          <label className="block text-gray-700 font-medium mb-2">Profile Visibility</label>
                          <select
                            name="profileVisibility"
                            value={privacySettings.profileVisibility}
                            onChange={handlePrivacyChange}
                            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                          >
                            <option value="public">Public</option>
                            <option value="friends">Friends Only</option>
                            <option value="private">Private</option>
                          </select>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="showEnrollments"
                            name="showEnrollments"
                            checked={privacySettings.showEnrollments}
                            onChange={handlePrivacyChange}
                            className="h-4 w-4 text-[#412D6C] focus:ring-[#412D6C] border-gray-300 rounded"
                          />
                          <label htmlFor="showEnrollments" className="ml-2 block text-gray-700">
                            Show my course enrollments to others
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="showProgress"
                            name="showProgress"
                            checked={privacySettings.showProgress}
                            onChange={handlePrivacyChange}
                            className="h-4 w-4 text-[#412D6C] focus:ring-[#412D6C] border-gray-300 rounded"
                          />
                          <label htmlFor="showProgress" className="ml-2 block text-gray-700">
                            Show my course progress to others
                          </label>
                        </div>
                      </div>
                      <button
                        type="submit"
                        className="bg-[#412D6C] text-white px-6 py-2 rounded-md hover:bg-[#362659] transition-colors"
                      >
                        Save Privacy Settings
                      </button>
                    </form>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default SettingsPage;
