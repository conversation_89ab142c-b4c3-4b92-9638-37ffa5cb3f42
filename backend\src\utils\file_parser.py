"""
File parser utility for the Bank Reconciliation API.
"""

import os
import pandas as pd
import pdfplumber
import csv
import subprocess
import tempfile
from datetime import datetime
import json
import numpy as np
from .pdf_table_extractor import extract_tables_from_pdf
from .polaris_bank_parser import extract_polaris_bank_table, extract_polaris_bank_info

# Path to the JavaScript engine
JS_ENGINE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'engine', 'bundled_file_parser.js')

def run_js_engine(file_path):
    """
    Run the JavaScript engine to parse Excel and CSV files.

    Args:
        file_path: Path to the file to parse

    Returns:
        Parsed data or None if the engine fails
    """
    try:
        # Check if the JavaScript engine exists
        if not os.path.exists(JS_ENGINE_PATH):
            print(f"JavaScript engine not found at {JS_ENGINE_PATH}")
            return None

        # Check if Node.js is installed
        try:
            # Quick check if node is available
            node_check = subprocess.run(['node', '--version'],
                                       stdout=subprocess.PIPE,
                                       stderr=subprocess.PIPE,
                                       check=False)
            if node_check.returncode != 0:
                print("Node.js is not installed or not in PATH. Falling back to Python implementation.")
                return None
        except Exception:
            print("Could not check for Node.js. Falling back to Python implementation.")
            return None

        # Create a temporary file to store the output
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as temp_file:
            output_path = temp_file.name

        # Create a temporary script file
        with tempfile.NamedTemporaryFile(suffix='.js', delete=False) as script_file:
            script_path = script_file.name
            script_content = f"""
const parser = require('{JS_ENGINE_PATH.replace('\\', '\\\\')}');
const fs = require('fs');
const path = require('path');

try {{
    const result = parser.parseFile('{file_path.replace('\\', '\\\\')}');
    fs.writeFileSync('{output_path.replace('\\', '\\\\')}', JSON.stringify(result));
    console.log('Parsing completed successfully');
}} catch (error) {{
    console.error('Error parsing file:', error);
    process.exit(1);
}}
"""
            script_file.write(script_content.encode('utf-8'))

        # Run the JavaScript engine with Node.js
        try:
            subprocess.run(['node', script_path],
                          check=True,
                          capture_output=True,
                          timeout=60)  # Set a timeout of 60 seconds

            # Read the output
            with open(output_path, 'r') as f:
                result = json.load(f)

            # Clean up the temporary files
            os.unlink(output_path)
            os.unlink(script_path)

            return result
        except subprocess.TimeoutExpired:
            print("JavaScript engine timed out. Falling back to Python implementation.")
            # Clean up the temporary files
            if os.path.exists(output_path):
                os.unlink(output_path)
            if os.path.exists(script_path):
                os.unlink(script_path)
            return None
        except Exception as script_error:
            print(f"Error running JavaScript script: {str(script_error)}")
            # Clean up the temporary files
            if os.path.exists(output_path):
                os.unlink(output_path)
            if os.path.exists(script_path):
                os.unlink(script_path)
            return None
    except Exception as e:
        print(f"Error setting up JavaScript engine: {str(e)}")
        return None

def parse_file(file_path):
    """
    Parse file based on its extension.
    Supports PDF, Excel, and CSV files.
    """
    file_name = os.path.basename(file_path)
    file_ext = os.path.splitext(file_path)[1].lower()
    stats = os.stat(file_path)

    # Parse based on file extension
    if file_ext == '.pdf':
        return parse_pdf(file_path, file_name, stats)
    elif file_ext in ['.xlsx', '.xls']:
        return parse_excel(file_path, file_name, stats)
    elif file_ext == '.csv':
        return parse_csv(file_path, file_name, stats)
    else:
        raise ValueError(f"Unsupported file type: {file_ext}")

def parse_pdf(file_path, file_name, stats):
    """
    Parse PDF file.
    """
    # Extract tables from PDF
    extracted_data = extract_tables_from_pdf(file_path)

    # Check if it's a Polaris Bank statement
    bank_info = {}
    content_text = extracted_data.get('text', '')

    if 'Polaris Bank Limited' in content_text or 'POLARIS BANK' in content_text:
        # Extract Polaris Bank table
        polaris_table = extract_polaris_bank_table(content_text)
        if polaris_table:
            extracted_data['tables'] = [polaris_table]

        # Extract bank info
        bank_info = extract_polaris_bank_info(content_text)

    # Generate preview
    preview = generate_preview(extracted_data)

    return {
        'metadata': {
            'fileName': file_name,
            'fileSize': stats.st_size,
            'fileType': 'pdf',
            'lastModified': datetime.fromtimestamp(stats.st_mtime),
            'mimeType': 'application/pdf',
            'title': bank_info.get('bankName'),
            'subject': f"Account: {bank_info.get('accountNumber')}" if bank_info.get('accountNumber') else None,
            'keywords': [f"Date Range: {bank_info.get('dateRange')}"] if bank_info.get('dateRange') else None
        },
        'content': extracted_data,
        'preview': preview,
        'summary': {
            'pageCount': extracted_data.get('pageCount', 0),
            'tableCount': len(extracted_data.get('tables', [])),
            'rowCount': sum(len(table.get('rows', [])) for table in extracted_data.get('tables', []))
        }
    }

def parse_excel(file_path, file_name, stats):
    """
    Parse Excel file using the JavaScript engine or pandas as fallback.
    """
    try:
        # First try using the JavaScript engine
        js_result = run_js_engine(file_path)
        if js_result:
            print(f"Successfully parsed Excel file using JavaScript engine: {file_path}")
            return js_result

        # Fall back to pandas if JavaScript engine fails
        print(f"JavaScript engine failed, falling back to pandas for Excel parsing: {file_path}")
        return parse_excel_with_pandas(file_path, file_name, stats)
    except Exception as e:
        print(f"Error parsing Excel file: {str(e)}")
        # Return basic metadata on error
        return {
            'metadata': {
                'fileName': file_name,
                'fileSize': stats.st_size,
                'fileType': os.path.splitext(file_name)[1].lower(),
                'lastModified': datetime.fromtimestamp(stats.st_mtime).isoformat()
            },
            'content': [],
            'preview': f"Error parsing Excel file: {str(e)}",
            'error': str(e)
        }

def parse_excel_with_pandas(file_path, file_name, stats):
    """
    Parse Excel file using pandas as a fallback.
    """
    try:
        # Read Excel file
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names

        # Process each sheet
        sheets = {}
        all_preview_rows = []
        sheet_dimensions = {}
        total_rows = 0
        max_columns = 0
        data_type_summary = {}
        statistics_summary = {}
        bank_info = {}

        for sheet_name in sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            # Store sheet data
            sheets[sheet_name] = df.to_dict(orient='records')

            # Update dimensions
            rows, cols = df.shape
            total_rows += rows
            max_columns = max(max_columns, cols)
            sheet_dimensions[sheet_name] = {'rows': rows, 'columns': cols}

            # Get preview rows
            preview_rows = df.head(10).to_dict(orient='records')
            all_preview_rows.extend(preview_rows)

            # Analyze data types
            for col in df.columns:
                data_type = str(df[col].dtype)
                if data_type not in data_type_summary:
                    data_type_summary[data_type] = 0
                data_type_summary[data_type] += 1

            # Calculate statistics for numeric columns
            for col in df.select_dtypes(include=['number']).columns:
                statistics_summary[col] = {
                    'min': df[col].min(),
                    'max': df[col].max(),
                    'mean': df[col].mean(),
                    'median': df[col].median()
                }

            # Try to extract bank info from first sheet
            if sheet_name == sheet_names[0]:
                bank_info = extract_bank_info_from_excel(df)

        # Generate preview
        preview = generate_preview({'data': all_preview_rows})

        return {
            'metadata': {
                'fileName': file_name,
                'fileSize': stats.st_size,
                'fileType': 'xlsx' if file_name.endswith('.xlsx') else 'xls',
                'lastModified': datetime.fromtimestamp(stats.st_mtime),
                'mimeType': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' if file_name.endswith('.xlsx') else 'application/vnd.ms-excel',
                'title': bank_info.get('bankName'),
                'subject': f"Account: {bank_info.get('accountNumber')}" if bank_info.get('accountNumber') else None,
                'keywords': [f"Date Range: {bank_info.get('dateRange')}"] if bank_info.get('dateRange') else None
            },
            'content': {
                'sheets': sheet_names,
                'data': sheets,
                'rawPreview': all_preview_rows,
                'dimensions': sheet_dimensions,
                'bankInfo': bank_info
            },
            'preview': preview,
            'summary': {
                'rowCount': total_rows,
                'columnCount': max_columns,
                'sheetCount': len(sheet_names),
                'dataTypes': data_type_summary,
                'statistics': statistics_summary
            }
        }
    except Exception as e:
        print(f"Error parsing Excel file with pandas: {str(e)}")
        # Return basic metadata on error
        return {
            'metadata': {
                'fileName': file_name,
                'fileSize': stats.st_size,
                'fileType': os.path.splitext(file_name)[1].lower(),
                'lastModified': datetime.fromtimestamp(stats.st_mtime).isoformat()
            },
            'content': [],
            'preview': f"Error parsing Excel file: {str(e)}",
            'error': str(e)
        }

def parse_csv(file_path, file_name, stats):
    """
    Parse CSV file using the JavaScript engine or Python's csv module as fallback.
    """
    try:
        # First try using the JavaScript engine
        js_result = run_js_engine(file_path)
        if js_result:
            print(f"Successfully parsed CSV file using JavaScript engine: {file_path}")
            return js_result

        # Fall back to Python's csv module if JavaScript engine fails
        print(f"JavaScript engine failed, falling back to Python's csv module for CSV parsing: {file_path}")
        return parse_csv_with_python(file_path, file_name, stats)
    except Exception as e:
        print(f"Error parsing CSV file: {str(e)}")
        # Return basic metadata on error
        return {
            'metadata': {
                'fileName': file_name,
                'fileSize': stats.st_size,
                'fileType': 'csv',
                'lastModified': datetime.fromtimestamp(stats.st_mtime).isoformat()
            },
            'content': [],
            'preview': f"Error parsing CSV file: {str(e)}",
            'error': str(e)
        }

def parse_csv_with_python(file_path, file_name, stats):
    """
    Parse CSV file using Python's csv module as a fallback.
    """
    try:
        rows = []

        with open(file_path, 'r', encoding='utf-8') as csvfile:
            csv_reader = csv.DictReader(csvfile)
            for row in csv_reader:
                rows.append(row)

        # Generate preview
        preview = generate_preview({'data': rows[:10]})

        # Extract bank info
        bank_info = extract_bank_info_from_csv(rows)

        return {
            'metadata': {
                'fileName': file_name,
                'fileSize': stats.st_size,
                'fileType': 'csv',
                'lastModified': datetime.fromtimestamp(stats.st_mtime),
                'mimeType': 'text/csv',
                'title': bank_info.get('bankName'),
                'subject': f"Account: {bank_info.get('accountNumber')}" if bank_info.get('accountNumber') else None,
                'keywords': [f"Date Range: {bank_info.get('dateRange')}"] if bank_info.get('dateRange') else None
            },
            'content': {
                'rows': rows,
                'headers': list(rows[0].keys()) if rows else [],
                'bankInfo': bank_info
            },
            'preview': preview,
            'summary': {
                'rowCount': len(rows),
                'columnCount': len(rows[0].keys()) if rows else 0
            }
        }
    except Exception as e:
        print(f"Error parsing CSV file with Python: {str(e)}")
        # Return basic metadata on error
        return {
            'metadata': {
                'fileName': file_name,
                'fileSize': stats.st_size,
                'fileType': 'csv',
                'lastModified': datetime.fromtimestamp(stats.st_mtime).isoformat()
            },
            'content': [],
            'preview': f"Error parsing CSV file: {str(e)}",
            'error': str(e)
        }

def generate_preview(data):
    """
    Generate a preview of the data.
    """
    preview = {
        'text': data.get('text', '')[:500] if isinstance(data.get('text'), str) else '',
        'tables': []
    }

    # Add tables to preview
    tables = data.get('tables', [])
    if tables and isinstance(tables, list):
        for table in tables[:2]:  # Only include first 2 tables
            preview_table = {
                'headers': table.get('headers', []),
                'rows': table.get('rows', [])[:5]  # Only include first 5 rows
            }
            preview['tables'].append(preview_table)

    # Add data rows to preview
    data_rows = data.get('data', [])
    if data_rows and isinstance(data_rows, list):
        preview['data'] = data_rows[:5]  # Only include first 5 rows

    return preview

def extract_bank_info_from_excel(df):
    """
    Extract bank information from Excel dataframe.
    """
    bank_info = {
        'bankName': None,
        'accountNumber': None,
        'dateRange': None
    }

    # Try to find bank name and account number in the first few rows
    first_rows = df.head(10).values.flatten()
    for value in first_rows:
        if isinstance(value, str):
            value_lower = value.lower()
            if 'bank' in value_lower:
                bank_info['bankName'] = value
            if 'account' in value_lower and any(c.isdigit() for c in value):
                bank_info['accountNumber'] = value

    # Try to find date range
    date_cols = df.select_dtypes(include=['datetime64']).columns
    if len(date_cols) > 0:
        first_date_col = date_cols[0]
        min_date = df[first_date_col].min()
        max_date = df[first_date_col].max()
        if pd.notna(min_date) and pd.notna(max_date):
            bank_info['dateRange'] = f"{min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}"

    return bank_info

def extract_bank_info_from_csv(rows):
    """
    Extract bank information from CSV rows.
    """
    bank_info = {
        'bankName': None,
        'accountNumber': None,
        'dateRange': None
    }

    # Try to find bank name and account number in the first few rows
    for row in rows[:10]:
        for key, value in row.items():
            if isinstance(value, str):
                value_lower = value.lower()
                if 'bank' in value_lower:
                    bank_info['bankName'] = value
                if 'account' in value_lower and any(c.isdigit() for c in value):
                    bank_info['accountNumber'] = value

    return bank_info
