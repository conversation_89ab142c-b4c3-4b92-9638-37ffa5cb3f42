import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FiFileText, FiDollarSign, FiRefreshCw, FiPlusCircle, FiAlertCircle, FiLock } from 'react-icons/fi';
import { motion } from 'framer-motion';
import useReconciliationStore from '../../store/reconciliationStore';
import ConnectionStatus from '../../components/common/ConnectionStatus';
import CorsTest from '../../components/common/CorsTest';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const DashboardPage = () => {
  const navigate = useNavigate();
  const {
    statements,
    reconciliations,
    fetchStatements,
    fetchReconciliations,
    statementsLoading,
    reconciliationsLoading,
    statementsError,
    reconciliationsError
  } = useReconciliationStore();

  const [stats, setStats] = useState({
    totalStatements: 0,
    bankStatements: 0,
    cashbookStatements: 0,
    totalReconciliations: 0,
    completedReconciliations: 0,
    inProgressReconciliations: 0,
    reconciliationPercentage: 0
  });

  // Check for authentication token
  useEffect(() => {
    // Check for token in multiple places
    const localStorageToken = localStorage.getItem('authToken');
    const backupToken = localStorage.getItem('auth_token_backup');
    const sessionStorageToken = sessionStorage.getItem('authToken');

    // Get token from cookie if available
    const getCookieToken = () => {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.startsWith('authToken=')) {
          return cookie.substring('authToken='.length, cookie.length);
        }
      }
      return null;
    };

    const cookieToken = getCookieToken();

    // Use any available token
    const isAuthenticated = !!(localStorageToken || backupToken || sessionStorageToken || cookieToken);

    // If we have a token in backup but not in primary, restore it
    if (!localStorageToken && (backupToken || sessionStorageToken || cookieToken)) {
      const token = backupToken || sessionStorageToken || cookieToken;
      console.log('Restoring token from backup source');
      localStorage.setItem('authToken', token);
    }

    // If no token is found, redirect to login
    if (!isAuthenticated) {
      console.log('No auth token found in dashboard, redirecting to login');
      navigate('/login?session=expired');
    }
  }, [navigate]);

  useEffect(() => {
    fetchStatements();
    fetchReconciliations();
  }, [fetchStatements, fetchReconciliations]);

  useEffect(() => {
    if (statements && Array.isArray(statements) && reconciliations && Array.isArray(reconciliations)) {
      const bankStatements = statements.filter(s => s.statement_type === 'bank').length;
      const cashbookStatements = statements.filter(s => s.statement_type === 'cashbook').length;
      const completedReconciliations = reconciliations.filter(r => r.status === 'completed').length;
      const inProgressReconciliations = reconciliations.filter(r => r.status === 'in_progress').length;

      setStats({
        totalStatements: statements.length,
        bankStatements,
        cashbookStatements,
        totalReconciliations: reconciliations.length,
        completedReconciliations,
        inProgressReconciliations,
        reconciliationPercentage: reconciliations.length > 0
          ? (completedReconciliations / reconciliations.length) * 100
          : 0
      });
    }
  }, [statements, reconciliations]);

  // Chart data for reconciliation progress
  const reconciliationChartData = {
    labels: ['Completed', 'In Progress'],
    datasets: [
      {
        data: [stats.completedReconciliations, stats.inProgressReconciliations],
        backgroundColor: ['#4F46E5', '#F59E0B'],
        hoverBackgroundColor: ['#4338CA', '#D97706'],
        borderWidth: 0,
      },
    ],
  };

  // Chart data for statements by type
  const statementsChartData = {
    labels: ['Bank Statements', 'Cashbook Statements'],
    datasets: [
      {
        data: [stats.bankStatements, stats.cashbookStatements],
        backgroundColor: ['#10B981', '#6366F1'],
        hoverBackgroundColor: ['#059669', '#4F46E5'],
        borderWidth: 0,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        padding: 12,
        titleFont: {
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          size: 13
        },
        cornerRadius: 4,
        displayColors: false
      }
    },
    cutout: '70%'
  };

  // Loading state
  if (statementsLoading || reconciliationsLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (statementsError || reconciliationsError) {
    const errorMessage = statementsError || reconciliationsError;
    const isNetworkError = errorMessage.toLowerCase().includes('network') ||
                          errorMessage.toLowerCase().includes('failed to fetch') ||
                          errorMessage.toLowerCase().includes('connection');

    // Check if it's an authentication error
    const isAuthError = errorMessage.toLowerCase().includes('unauthorized') ||
                        errorMessage.toLowerCase().includes('authentication') ||
                        errorMessage.toLowerCase().includes('unauthenticated') ||
                        errorMessage.toLowerCase().includes('token');

    return (
      <div className="bg-red-50 p-6 rounded-lg shadow-sm">
        <div className="flex items-center">
          <FiAlertCircle className="text-red-500 mr-3" size={24} />
          <h2 className="text-lg font-medium text-red-800">Error Loading Dashboard</h2>
        </div>

        <p className="mt-2 text-red-700">
          {isNetworkError ?
            'Network Error: Unable to connect to the server. Please check your internet connection.' :
            isAuthError ?
            'Authentication Error: Your session may have expired. Please log in again.' :
            errorMessage
          }
        </p>

        <div className="mt-4 flex space-x-4">
          {isAuthError ? (
            <button
              onClick={() => navigate('/login?session=expired')}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors flex items-center"
            >
              <FiLock className="mr-2" />
              Go to Login
            </button>
          ) : (
            <button
              onClick={() => {
                fetchStatements();
                fetchReconciliations();
              }}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
            >
              <FiRefreshCw className="mr-2" />
              Retry
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <div className="flex space-x-3">
          <Link
            to="/statements/upload"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FiPlusCircle className="-ml-1 mr-2 h-5 w-5" />
            Upload Statement
          </Link>
          <Link
            to="/reconciliations/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <FiPlusCircle className="-ml-1 mr-2 h-5 w-5" />
            New Reconciliation
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          whileHover={{ y: -5 }}
          className="bg-white rounded-lg shadow-sm p-6 border border-gray-100"
        >
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-indigo-100 text-indigo-600">
              <FiFileText size={24} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Statements</p>
              <h3 className="text-2xl font-semibold text-gray-900">{stats.totalStatements}</h3>
            </div>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ y: -5 }}
          className="bg-white rounded-lg shadow-sm p-6 border border-gray-100"
        >
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <FiDollarSign size={24} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Reconciliations</p>
              <h3 className="text-2xl font-semibold text-gray-900">{stats.totalReconciliations}</h3>
            </div>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ y: -5 }}
          className="bg-white rounded-lg shadow-sm p-6 border border-gray-100"
        >
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <FiRefreshCw size={24} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <h3 className="text-2xl font-semibold text-gray-900">{stats.inProgressReconciliations}</h3>
            </div>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ y: -5 }}
          className="bg-white rounded-lg shadow-sm p-6 border border-gray-100"
        >
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <FiRefreshCw size={24} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed</p>
              <h3 className="text-2xl font-semibold text-gray-900">{stats.completedReconciliations}</h3>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Charts and Connection Status */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm p-6 border border-gray-100 lg:col-span-2"
        >
          <h2 className="text-lg font-medium text-gray-900 mb-4">Reconciliation Status</h2>
          <div className="h-64">
            <Doughnut data={reconciliationChartData} options={chartOptions} />
          </div>
        </motion.div>

        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <ConnectionStatus />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.15 }}
            className="mt-4"
          >
            <CorsTest />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6 border border-gray-100"
          >
            <h2 className="text-lg font-medium text-gray-900 mb-4">Statement Types</h2>
            <div className="h-48">
              <Doughnut data={statementsChartData} options={chartOptions} />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm p-6 border border-gray-100"
      >
        <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Reconciliations</h2>

        {reconciliations && Array.isArray(reconciliations) ? (
          reconciliations.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-gray-500">No reconciliations found</p>
              <Link
                to="/reconciliations/new"
                className="mt-2 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <FiPlusCircle className="-ml-1 mr-2 h-5 w-5" />
                Create New Reconciliation
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reconciliations.slice(0, 5).map((reconciliation) => (
                    <tr key={reconciliation.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{reconciliation.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          reconciliation.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {reconciliation.status === 'completed' ? 'Completed' : 'In Progress'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-indigo-600 h-2.5 rounded-full"
                            style={{ width: `${reconciliation.reconciliation_percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-xs text-gray-500">
                          {Math.round(reconciliation.reconciliation_percentage)}%
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(reconciliation.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Link
                          to={`/reconciliations/${reconciliation.id}`}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          View
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {reconciliations.length > 5 && (
                <div className="mt-4 text-center">
                  <Link
                    to="/reconciliations"
                    className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                  >
                    View all reconciliations
                  </Link>
                </div>
              )}
            </div>
          )
        ) : (
          <tr>
            <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
              No reconciliations data available
            </td>
          </tr>
        )}
      </motion.div>
    </div>
  );
};

export default DashboardPage;


