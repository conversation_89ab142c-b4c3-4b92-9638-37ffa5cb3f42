/* adler32.js (C) 2014-present SheetJS -- http://sheetjs.com */
// TypeScript Version: 2.2

/** Version string */
export const version: string;

/** Process a node buffer or byte array */
export function buf(data: number[] | Uint8Array, seed?: number): number;

/** Process a binary string */
export function bstr(data: string, seed?: number): number;

/** Process a JS string based on the UTF8 encoding */
export function str(data: string, seed?: number): number;
