import { toast } from 'react-toastify';

// Success toast with consistent styling
export const showSuccessToast = (message) => {
  toast.success(message, {
    position: "top-right",
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    style: {
      background: '#f0f9eb',
      color: '#67c23a',
      borderRadius: '4px',
      boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
    }
  });
};

// Error toast with consistent styling
export const showErrorToast = (message) => {
  toast.error(message, {
    position: "top-right",
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    style: {
      background: '#fef0f0',
      color: '#f56c6c',
      borderRadius: '4px',
      boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
    }
  });
};

// Info toast with consistent styling
export const showInfoToast = (message) => {
  toast.info(message, {
    position: "top-right",
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    style: {
      background: '#f4f4f5',
      color: '#909399',
      borderRadius: '4px',
      boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
    }
  });
};

// Warning toast with consistent styling
export const showWarningToast = (message) => {
  toast.warn(message, {
    position: "top-right",
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    style: {
      background: '#fdf6ec',
      color: '#e6a23c',
      borderRadius: '4px',
      boxShadow: '0 2px 12px 0 rgba(0, 0, 0, 0.1)'
    }
  });
};

// Default export for convenience
export default {
  success: showSuccessToast,
  error: showErrorToast,
  info: showInfoToast,
  warning: showWarningToast
};
