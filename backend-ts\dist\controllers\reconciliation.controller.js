"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.completeReconciliation = exports.getReconciliationTransactions = exports.createReconciliation = exports.getReconciliationById = exports.getAllReconciliations = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const reconciliation_model_1 = __importStar(require("../models/reconciliation.model"));
const transaction_model_1 = __importStar(require("../models/transaction.model"));
/**
 * Get all reconciliations
 * @route GET /api/reconciliations
 */
const getAllReconciliations = async (req, res) => {
    try {
        // Get user ID from authenticated user
        const userId = req.user.sub;
        // Find all reconciliations for this user
        const reconciliations = await reconciliation_model_1.default.find({ userId }).sort({ createdAt: -1 });
        res.status(200).json({
            status: true,
            count: reconciliations.length,
            data: reconciliations,
        });
    }
    catch (error) {
        console.error('Error getting reconciliations:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getAllReconciliations = getAllReconciliations;
/**
 * Get reconciliation by ID
 * @route GET /api/reconciliations/:id
 */
const getReconciliationById = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid reconciliation ID' });
            return;
        }
        // Find reconciliation by ID and user ID
        const reconciliation = await reconciliation_model_1.default.findOne({
            _id: id,
            userId,
        });
        if (!reconciliation) {
            res.status(404).json({ message: 'Reconciliation not found' });
            return;
        }
        res.status(200).json({
            status: true,
            data: reconciliation,
        });
    }
    catch (error) {
        console.error('Error getting reconciliation:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getReconciliationById = getReconciliationById;
/**
 * Create a new reconciliation
 * @route POST /api/reconciliations
 */
const createReconciliation = async (req, res) => {
    try {
        const { name, description, startDate, endDate } = req.body;
        const userId = req.user.sub;
        // Create new reconciliation
        const reconciliation = new reconciliation_model_1.default({
            name,
            description,
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            userId,
        });
        // Save reconciliation to database
        await reconciliation.save();
        // Get transaction counts for this date range
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(endDate);
        const totalTransactions = await transaction_model_1.default.countDocuments({
            userId,
            date: { $gte: startDateObj, $lte: endDateObj },
        });
        const matchedTransactions = await transaction_model_1.default.countDocuments({
            userId,
            date: { $gte: startDateObj, $lte: endDateObj },
            status: transaction_model_1.TransactionStatus.MATCHED,
        });
        const unmatchedTransactions = totalTransactions - matchedTransactions;
        // Update reconciliation with transaction counts
        reconciliation.totalTransactions = totalTransactions;
        reconciliation.matchedTransactions = matchedTransactions;
        reconciliation.unmatchedTransactions = unmatchedTransactions;
        await reconciliation.save();
        res.status(201).json({
            status: true,
            message: 'Reconciliation created successfully',
            data: reconciliation,
        });
    }
    catch (error) {
        console.error('Error creating reconciliation:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.createReconciliation = createReconciliation;
/**
 * Get transactions for a reconciliation
 * @route GET /api/reconciliations/:id/transactions
 */
const getReconciliationTransactions = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid reconciliation ID' });
            return;
        }
        // Find reconciliation by ID and user ID
        const reconciliation = await reconciliation_model_1.default.findOne({
            _id: id,
            userId,
        });
        if (!reconciliation) {
            res.status(404).json({ message: 'Reconciliation not found' });
            return;
        }
        // Get transactions for this date range
        const transactions = await transaction_model_1.default.find({
            userId,
            date: { $gte: reconciliation.startDate, $lte: reconciliation.endDate },
        })
            .sort({ date: -1 })
            .populate('statementId', 'name bankName')
            .populate('matchedWith', 'date description amount reference');
        res.status(200).json({
            status: true,
            count: transactions.length,
            data: transactions,
        });
    }
    catch (error) {
        console.error('Error getting reconciliation transactions:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getReconciliationTransactions = getReconciliationTransactions;
/**
 * Complete a reconciliation
 * @route POST /api/reconciliations/:id/complete
 */
const completeReconciliation = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid reconciliation ID' });
            return;
        }
        // Find reconciliation by ID and user ID
        const reconciliation = await reconciliation_model_1.default.findOne({
            _id: id,
            userId,
        });
        if (!reconciliation) {
            res.status(404).json({ message: 'Reconciliation not found' });
            return;
        }
        // Update reconciliation status
        reconciliation.status = reconciliation_model_1.ReconciliationStatus.COMPLETED;
        await reconciliation.save();
        // Update all matched transactions in this date range to reconciled
        await transaction_model_1.default.updateMany({
            userId,
            date: { $gte: reconciliation.startDate, $lte: reconciliation.endDate },
            status: transaction_model_1.TransactionStatus.MATCHED,
        }, {
            status: transaction_model_1.TransactionStatus.RECONCILED,
        });
        res.status(200).json({
            status: true,
            message: 'Reconciliation completed successfully',
            data: reconciliation,
        });
    }
    catch (error) {
        console.error('Error completing reconciliation:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.completeReconciliation = completeReconciliation;
