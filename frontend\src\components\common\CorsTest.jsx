import React, { useState } from 'react';
import { testCorsConfiguration, testAuthEndpoint } from '../../utils/corsTest';
import { FiRefreshCw, FiCheckCircle, FiXCircle } from 'react-icons/fi';

const CorsTest = () => {
  const [testResult, setTestResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [authTestResult, setAuthTestResult] = useState(null);
  const [authLoading, setAuthLoading] = useState(false);

  const runCorsTest = async () => {
    setLoading(true);
    try {
      const result = await testCorsConfiguration();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message,
        message: 'CORS test failed with an exception'
      });
    } finally {
      setLoading(false);
    }
  };

  const runAuthTest = async () => {
    setAuthLoading(true);
    try {
      const result = await testAuthEndpoint();
      setAuthTestResult(result);
    } catch (error) {
      setAuthTestResult({
        success: false,
        error: error.message,
        message: 'Authenticated CORS test failed with an exception'
      });
    } finally {
      setAuthLoading(false);
    }
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-lg font-medium mb-4">CORS Configuration Test</h3>

      {/* Public Endpoint Test */}
      <div className="mb-4">
        <div className="flex items-center mb-2">
          <div className="mr-2">
            {loading ? (
              <FiRefreshCw className="animate-spin text-blue-500" size={20} />
            ) : testResult?.success ? (
              <FiCheckCircle className="text-green-500" size={20} />
            ) : testResult ? (
              <FiXCircle className="text-red-500" size={20} />
            ) : null}
          </div>

          <div className="text-sm">
            {loading
              ? 'Testing CORS configuration...'
              : testResult?.message || 'Click to test CORS configuration'}
          </div>
        </div>

        {testResult?.data && (
          <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono overflow-auto max-h-32">
            <pre>{JSON.stringify(testResult.data, null, 2)}</pre>
          </div>
        )}

        {testResult?.error && (
          <div className="mt-2 p-2 bg-red-50 text-red-700 rounded text-xs">
            Error: {testResult.error}
          </div>
        )}

        <button
          onClick={runCorsTest}
          disabled={loading}
          className="mt-2 px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 disabled:opacity-50 flex items-center"
        >
          {loading ? (
            <>
              <FiRefreshCw className="animate-spin mr-1" size={14} />
              Testing...
            </>
          ) : (
            <>
              <FiRefreshCw className="mr-1" size={14} />
              Test Public Endpoint
            </>
          )}
        </button>
      </div>

      {/* Authenticated Endpoint Test */}
      <div className="mt-6">
        <div className="flex items-center mb-2">
          <div className="mr-2">
            {authLoading ? (
              <FiRefreshCw className="animate-spin text-blue-500" size={20} />
            ) : authTestResult?.success ? (
              <FiCheckCircle className="text-green-500" size={20} />
            ) : authTestResult ? (
              <FiXCircle className="text-red-500" size={20} />
            ) : null}
          </div>

          <div className="text-sm">
            {authLoading
              ? 'Testing authenticated endpoint...'
              : authTestResult?.message || 'Click to test authenticated endpoint'}
          </div>
        </div>

        {authTestResult?.data && (
          <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono overflow-auto max-h-32">
            <pre>{JSON.stringify(authTestResult.data, null, 2)}</pre>
          </div>
        )}

        {authTestResult?.error && (
          <div className="mt-2 p-2 bg-red-50 text-red-700 rounded text-xs">
            Error: {authTestResult.error}
          </div>
        )}

        <button
          onClick={runAuthTest}
          disabled={authLoading}
          className="mt-2 px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 disabled:opacity-50 flex items-center"
        >
          {authLoading ? (
            <>
              <FiRefreshCw className="animate-spin mr-1" size={14} />
              Testing...
            </>
          ) : (
            <>
              <FiRefreshCw className="mr-1" size={14} />
              Test Authenticated Endpoint
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default CorsTest;
