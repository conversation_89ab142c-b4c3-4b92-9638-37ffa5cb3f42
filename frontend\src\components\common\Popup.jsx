import React from 'react';
import PopupOverlay from './PopupOverlay';

/**
 * Popup component
 * 
 * A reusable component for creating popups with a consistent style.
 * This can be used for alerts, confirmations, and other simple popups.
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Title of the popup
 * @param {React.ReactNode} props.children - Content to display inside the popup
 * @param {Function} props.onClose - Function to call when the close button is clicked
 * @param {boolean} props.showCloseButton - Whether to show the close button (default: true)
 * @param {string} props.className - Additional classes for the popup container
 * @param {boolean} props.closeOnOverlayClick - Whether to close the popup when the overlay is clicked (default: true)
 */
const Popup = ({ 
  title, 
  children, 
  onClose, 
  showCloseButton = true,
  className = '',
  closeOnOverlayClick = true
}) => {
  return (
    <PopupOverlay 
      onClose={onClose} 
      className={`w-full max-w-md ${className}`}
      closeOnOverlayClick={closeOnOverlayClick}
    >
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-[#412D6C]">{title}</h2>
        {showCloseButton && (
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
      
      <div className="popup-content">
        {children}
      </div>
    </PopupOverlay>
  );
};

export default Popup;
