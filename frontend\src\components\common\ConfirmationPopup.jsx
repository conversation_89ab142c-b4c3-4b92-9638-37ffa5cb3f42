import React from 'react';
import Popup from './Popup';

/**
 * ConfirmationPopup component
 * 
 * A reusable component for creating confirmation popups with confirm and cancel buttons.
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Title of the popup
 * @param {string} props.message - Message to display in the popup
 * @param {string} props.confirmText - Text for the confirm button (default: "Confirm")
 * @param {string} props.cancelText - Text for the cancel button (default: "Cancel")
 * @param {Function} props.onConfirm - Function to call when the confirm button is clicked
 * @param {Function} props.onCancel - Function to call when the cancel button is clicked
 * @param {string} props.confirmButtonClass - Additional classes for the confirm button
 * @param {string} props.cancelButtonClass - Additional classes for the cancel button
 * @param {boolean} props.closeOnOverlayClick - Whether to close the popup when the overlay is clicked (default: true)
 */
const ConfirmationPopup = ({ 
  title, 
  message, 
  confirmText = "Confirm", 
  cancelText = "Cancel", 
  onConfirm, 
  onCancel,
  confirmButtonClass = "bg-[#412D6C] hover:bg-[#362659]",
  cancelButtonClass = "bg-gray-300 hover:bg-gray-400 text-gray-800",
  closeOnOverlayClick = true
}) => {
  return (
    <Popup 
      title={title} 
      onClose={onCancel} 
      closeOnOverlayClick={closeOnOverlayClick}
    >
      <div className="mb-6">
        <p className="text-gray-700">{message}</p>
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          onClick={onCancel}
          className={`px-4 py-2 rounded-md transition-colors ${cancelButtonClass}`}
        >
          {cancelText}
        </button>
        
        <button
          onClick={onConfirm}
          className={`px-4 py-2 rounded-md text-white transition-colors ${confirmButtonClass}`}
        >
          {confirmText}
        </button>
      </div>
    </Popup>
  );
};

export default ConfirmationPopup;
