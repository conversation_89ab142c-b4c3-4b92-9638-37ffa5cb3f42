import React, { useState } from 'react';
import { authService } from '../services/api';
import { Popup } from './common';

/**
 * LoginPopup component
 *
 * This component displays a login popup when the token is expired.
 * It allows the user to log in again without navigating away from the current page.
 */
const LoginPopup = ({ onClose, onLoginSuccess, message }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic validation
    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Call login API
      const response = await authService.login(email, password);

      if (response && response.status && response.data) {
        // Login successful
        onLoginSuccess({
          token: response.data.token,
          userId: response.data.userId,
          role: response.data.role,
          email: response.data.email
        });
      } else {
        setError('<PERSON><PERSON> failed. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Popup title="Login Required" onClose={onClose}>

        {message && (
          <div className="mb-4 p-3 bg-yellow-50 text-yellow-800 rounded-md">
            {message}
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              placeholder="Enter your email"
              required
            />
          </div>

          <div className="mb-6">
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              placeholder="Enter your password"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`w-full bg-[#412D6C] text-white py-2 rounded-md hover:bg-[#362659] transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
    </Popup>
  );
};

export default LoginPopup;
