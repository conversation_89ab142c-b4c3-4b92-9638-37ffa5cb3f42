@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&family=Permanent+Marker&display=swap');

@import "tailwindcss";


*{
    font-family: 'Outfit';
}

@layer utilities {
    .grid-cols-auto {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    .max-w-coursecard{
        max-width: 424px;
    }
}

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        padding-top: 0; /* Remove padding above navbar */
    }
}

::-webkit-scrollbar {
    display: none;
}

.rich-text { font-size: 15px; color: #7A7B7D; }

.rich-text p { margin-bottom: 16px; }

.rich-text h1 { font-size: 36px; font-weight: 800; color: #252525; margin: 32px 0; }

.rich-text h2 { font-size: 22px; font-weight: 700; color: #252525; margin: 24px 0; }

.rich-text h3 { font-size: 18px; font-weight: 600; color: #333333; margin: 20px 0; }

.rich-text h4 { font-size: 16px; font-weight: 500; color: #444444; margin: 16px 0; }

.rich-text h5 { font-size: 14px; font-weight: 400; color: #555555; margin: 12px 0; }

.rich-text h6 { font-size: 12px; font-weight: 400; color: #666666; margin: 8px 0; }

.rich-text strong { font-weight: 700; }

.rich-text ol { margin-left: 30px; list-style-type: decimal; }

.rich-text ul { margin-left: 30px; list-style-type: disc; }

.rich-text li { margin-bottom: 8px; }

/* Glassmorphism effect */
.glassmorphism {
    @apply backdrop-blur-md bg-white/70 border border-white/20 shadow-lg;
}

.glassmorphism-dark {
    @apply backdrop-blur-md bg-black/30 border border-white/10 shadow-lg;
}

/* Custom scrollbar for tables and content areas */
.custom-scrollbar::-webkit-scrollbar {
    display: block;
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Indeterminate progress bar animation */
@keyframes progress-indeterminate {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.animate-progress-indeterminate {
    animation: progress-indeterminate 1.5s linear infinite;
    background-image: linear-gradient(
        to right,
        transparent 0%,
        rgba(99, 102, 241, 0.8) 50%,
        transparent 100%
    );
}
