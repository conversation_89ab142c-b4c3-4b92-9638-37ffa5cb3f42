/**
 * Enhanced PDF Table Extractor
 *
 * This module provides advanced table extraction capabilities for PDF files,
 * specifically optimized for bank statements and financial documents.
 */

import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import pdfParse from 'pdf-parse';
import { PDFExtract, PDFExtractOptions } from 'pdf.js-extract';
import { extractPolarisBankTable, extractPolarisBankInfo } from './polarisBankParser';

// Convert fs functions to Promise-based
const readFileAsync = promisify(fs.readFile);
const statAsync = promisify(fs.stat);

// Define interfaces for extracted data
export interface TableCell {
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  fontName?: string;
}

export interface TableRow {
  [key: string]: string;
}

export interface Table {
  headers: string[];
  rows: TableRow[];
}

export interface BankStatementInfo {
  bankName?: string;
  accountNumber?: string;
  accountType?: string;
  dateRange?: string;
  sortCode?: string;
  branch?: string;
  reportBy?: string;
  staffInfo?: string;
  address?: string[];
  recipient?: string[];
}

export interface ExtractedPdfData {
  text: string;
  tables: Table[];
  bankInfo: BankStatementInfo;
  metadata: any;
}

/**
 * Extract tables from PDF using pdf.js-extract for detailed text extraction
 * with coordinates, which helps with complex table structures
 */
async function extractTablesWithPdfJs(filePath: string): Promise<ExtractedPdfData> {
  const pdfExtract = new PDFExtract();
  const options: PDFExtractOptions = {
    normalizeWhitespace: false,
    disableCombineTextItems: false
  };

  try {
    const data = await pdfExtract.extract(filePath, options);
    const text = data.pages.map(page =>
      page.content.map(item => item.str).join(' ')
    ).join('\n');

    // Extract bank statement info
    const bankInfo = extractBankStatementInfo(text);

    // Extract tables using coordinate-based analysis
    const tables = extractTablesFromCoordinates(data.pages);

    return {
      text,
      tables,
      bankInfo,
      metadata: data.meta
    };
  } catch (error) {
    console.error('Error extracting tables with pdf.js-extract:', error);
    throw error;
  }
}

/**
 * Extract tables from PDF using pdf-parse (simpler approach)
 */
async function extractTablesWithPdfParse(filePath: string): Promise<ExtractedPdfData> {
  try {
    const fileBuffer = await readFileAsync(filePath);
    const pdfData = await pdfParse(fileBuffer);
    const text = pdfData.text;

    // Extract bank statement info
    const bankInfo = extractBankStatementInfo(text);

    // Extract tables using text pattern analysis
    const tables = extractTablesFromText(text);

    return {
      text,
      tables,
      bankInfo,
      metadata: pdfData.info
    };
  } catch (error) {
    console.error('Error extracting tables with pdf-parse:', error);
    throw error;
  }
}

/**
 * Extract tables from text content using pattern recognition
 */
function extractTablesFromText(text: string): Table[] {
  const tables: Table[] = [];

  // First, try to detect bank statement tables specifically
  const bankStatementTable = extractBankStatementTable(text);
  if (bankStatementTable && bankStatementTable.rows.length > 0) {
    tables.push(bankStatementTable);
  }

  // Check if this is a Polaris Bank statement
  const isPolarisBank = text.includes('Polaris Bank Limited') || text.includes('POLARIS BANK');
  if (isPolarisBank) {
    console.log("PDF TABLE EXTRACTOR - Detected Polaris Bank statement");

    // Use our specialized Polaris Bank parser
    const polarisTable = extractPolarisBankTable(text);

    if (polarisTable && polarisTable.rows.length > 0 &&
        !tables.some(t => JSON.stringify(t) === JSON.stringify(polarisTable))) {
      console.log("PDF TABLE EXTRACTOR - Successfully extracted Polaris Bank table with",
        polarisTable.rows.length, "rows");
      tables.push(polarisTable);
    } else {
      console.log("PDF TABLE EXTRACTOR - Failed to extract Polaris Bank table or no rows found");
    }
  }

  return tables;
}

/**
 * Extract tables from PDF pages using coordinate-based analysis
 */
function extractTablesFromCoordinates(pages: any[]): Table[] {
  const tables: Table[] = [];

  // Process each page
  for (const page of pages) {
    // Group text items by y-coordinate to identify rows
    const rowGroups = groupTextItemsByRow(page.content);

    // If we have enough rows, try to extract a table
    if (rowGroups.length > 3) {
      const potentialTable = extractTableFromRowGroups(rowGroups);
      if (potentialTable && potentialTable.rows.length > 0) {
        tables.push(potentialTable);
      }
    }
  }

  return tables;
}

/**
 * Group text items by their y-coordinate to identify rows
 */
function groupTextItemsByRow(textItems: any[]): any[][] {
  // Sort items by y-coordinate
  const sortedItems = [...textItems].sort((a, b) => a.y - b.y);

  const rows: any[][] = [];
  let currentRow: any[] = [];
  let currentY = -1;

  // Group items with similar y-coordinates
  for (const item of sortedItems) {
    if (currentY === -1 || Math.abs(item.y - currentY) < 5) {
      // Same row (allowing for small variations)
      currentRow.push(item);
      currentY = item.y;
    } else {
      // New row
      if (currentRow.length > 0) {
        rows.push([...currentRow].sort((a, b) => a.x - b.x)); // Sort by x-coordinate
      }
      currentRow = [item];
      currentY = item.y;
    }
  }

  // Add the last row
  if (currentRow.length > 0) {
    rows.push([...currentRow].sort((a, b) => a.x - b.x));
  }

  return rows;
}

/**
 * Extract a table from grouped rows
 */
function extractTableFromRowGroups(rowGroups: any[][]): Table | null {
  if (rowGroups.length < 2) return null;

  // Try to identify header row
  let headerRowIndex = 0;
  for (let i = 0; i < Math.min(3, rowGroups.length); i++) {
    const row = rowGroups[i];
    // Check if this row has potential headers
    if (row.length >= 3 && row.some(item =>
      item.str.match(/date|details|description|amount|balance|debit|credit/i))) {
      headerRowIndex = i;
      break;
    }
  }

  // Extract headers
  const headerRow = rowGroups[headerRowIndex];
  const headers = headerRow.map((item: any) => item.str.trim());

  // Extract data rows
  const rows: TableRow[] = [];
  for (let i = headerRowIndex + 1; i < rowGroups.length; i++) {
    const rowItems = rowGroups[i];
    if (rowItems.length === 0) continue;

    // Create a row object
    const row: TableRow = {};
    for (let j = 0; j < Math.min(headers.length, rowItems.length); j++) {
      row[headers[j]] = rowItems[j]?.str || '';
    }

    // Only add rows that have enough data
    if (Object.keys(row).length >= Math.max(2, Math.floor(headers.length / 2))) {
      rows.push(row);
    }
  }

  return { headers, rows };
}

/**
 * Extract bank statement information from text
 */
function extractBankStatementInfo(text: string): BankStatementInfo {
  const lines = text.split('\n').filter(line => line.trim() !== '');
  const result: BankStatementInfo = {
    address: [],
    recipient: []
  };

  // Check if this is a Polaris Bank statement
  const isPolarisBank = text.includes('Polaris Bank Limited') || text.includes('POLARIS BANK');
  if (isPolarisBank) {
    // Use our specialized Polaris Bank info extractor
    return extractPolarisBankInfo(text);
  } else {
    // Generic bank statement detection
    for (let i = 0; i < Math.min(10, lines.length); i++) {
      if (lines[i].includes('BANK') || lines[i].includes('Bank')) {
        result.bankName = lines[i].trim();
        break;
      }
    }
  }

  return result;
}

// The Polaris Bank extraction function has been moved to polarisBankParser.ts

/**
 * Extract bank statement table (generic approach)
 */
function extractBankStatementTable(text: string): Table | null {
  // Implementation for generic bank statements
  // This is a simplified version that can be expanded based on specific needs

  const lines = text.split('\n').filter(line => line.trim() !== '');

  // Initialize result
  const result: Table = {
    headers: ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'],
    rows: []
  };

  // Look for table headers
  let tableStartIndex = -1;
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if ((line.includes('Date') && line.includes('Description') && line.includes('Amount')) ||
        (line.includes('Date') && line.includes('Details') && line.includes('Debit'))) {
      tableStartIndex = i;
      break;
    }
  }

  if (tableStartIndex === -1) return null;

  // Extract rows
  for (let i = tableStartIndex + 1; i < lines.length; i++) {
    const line = lines[i].trim();

    // Skip empty lines
    if (!line) continue;

    // Check if this looks like a transaction entry (has date and amount)
    if (line.match(/\d{2}[\/\-\.]\d{2}[\/\-\.]\d{2,4}/) &&
        line.match(/\d+\.\d{2}/)) {

      // Split by multiple spaces to get column values
      const values = line.split(/\s{2,}/).filter(v => v.trim() !== '');

      // Only process if we have enough values
      if (values.length >= 3) {
        const entry: TableRow = {};

        // Map values to headers
        for (let j = 0; j < Math.min(result.headers.length, values.length); j++) {
          entry[result.headers[j]] = values[j];
        }

        // Add balance if it's the last column and we have more values than headers
        if (values.length > result.headers.length) {
          entry['Balance'] = values[values.length - 1];
        }

        result.rows.push(entry);
      }
    }
  }

  return result.rows.length > 0 ? result : null;
}

/**
 * Main function to extract tables from PDF
 */
export async function extractTablesFromPDF(filePath: string): Promise<ExtractedPdfData> {
  console.log("PDF TABLE EXTRACTOR - Starting extraction for file:", filePath);

  try {
    // First try with pdf.js-extract for more detailed extraction
    console.log("PDF TABLE EXTRACTOR - Attempting extraction with pdf.js-extract");
    const result = await extractTablesWithPdfJs(filePath);
    console.log("PDF TABLE EXTRACTOR - Successfully extracted with pdf.js-extract");
    console.log("PDF TABLE EXTRACTOR - Tables found:", result.tables.length);
    return result;
  } catch (error) {
    console.warn('Failed to extract with pdf.js-extract, falling back to pdf-parse:', error);

    // Fall back to simpler extraction
    console.log("PDF TABLE EXTRACTOR - Attempting extraction with pdf-parse");
    const result = await extractTablesWithPdfParse(filePath);
    console.log("PDF TABLE EXTRACTOR - Successfully extracted with pdf-parse");
    console.log("PDF TABLE EXTRACTOR - Tables found:", result.tables.length);
    return result;
  }
}

export default {
  extractTablesFromPDF
};
