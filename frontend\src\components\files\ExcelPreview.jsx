import React, { useState } from 'react';
import { FiChevronLeft, FiChevronRight, FiInfo, FiBarChart2, FiAlertCircle, FiClock } from 'react-icons/fi';
import ProgressLoader from '../common/ProgressLoader';

const ExcelPreview = ({ data, processingStatus = 'completed', fetchTime = 0 }) => {
  const [activeSheet, setActiveSheet] = useState(0);
  const [showDataTypes, setShowDataTypes] = useState(false);

  // Handle the new data structure
  const content = data.content || {};
  const summary = data.summary || {};

  // Check if we have content array from the bank statement format
  if (Array.isArray(data.content) && data.content.length > 0) {
    // This is the bank statement format with content array
    const contentArray = data.content;
    const totalRows = contentArray.length;

    // Check if the first row has the expected format for the normalized data
    const firstRow = contentArray[0];
    const hasNormalizedFormat = firstRow &&
                               (firstRow['Financial Date'] !== undefined ||
                                firstRow['Transaction Date'] !== undefined ||
                                firstRow['Reference No.'] !== undefined);

    // If we already have the normalized format, we can skip the extraction steps
    if (hasNormalizedFormat) {
      // We already have the properly formatted data
      const headers = Object.keys(firstRow).filter(key => key !== undefined && key !== null && key !== '');

      // Extract bank info if available
      let bankName = 'RANDALPHA MICROFINANCE BANK LIMITED'; // Default bank name

      return (
        <div className="bg-white p-4 rounded border border-gray-200">
          <div className="mb-4">
            {bankName && (
              <h3 className="text-lg font-semibold text-gray-800 mb-1">{bankName}</h3>
            )}

            {/* Show total rows info */}
            <p className="text-sm text-gray-600 mt-2">
              Total rows: {contentArray.length}
            </p>
          </div>

          {/* Scrollable container with fixed height and visible scrollbars */}
          <div className="overflow-auto custom-scrollbar" style={{
            maxHeight: '70vh',
            maxWidth: '100%',
            overflowY: 'scroll',
            overflowX: 'scroll',
            border: '1px solid #ddd',
            borderRadius: '4px'
          }}>
            <table className="min-w-full border-collapse">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  {/* Line number header */}
                  <th scope="col" className="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100 w-12">
                    #
                  </th>
                  {headers.map((header, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {contentArray.map((row, rowIndex) => (
                  <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    {/* Line number column */}
                    <td className="px-2 py-4 whitespace-nowrap text-xs text-gray-500 bg-gray-100 text-right font-mono w-12">
                      {rowIndex + 1}
                    </td>
                    {headers.map((header, colIndex) => {
                      const cellValue = row[header];
                      const isNumeric = !isNaN(parseFloat(cellValue)) && cellValue !== null && cellValue !== undefined && cellValue.toString().trim() !== '';

                      return (
                        <td
                          key={colIndex}
                          className={`px-6 py-4 whitespace-nowrap text-sm ${isNumeric ? 'text-right font-medium' : 'text-gray-500'}`}
                        >
                          {cellValue !== null && cellValue !== undefined ?
                            (typeof cellValue === 'string' && cellValue.trim() === '\n    ' ? '' : cellValue)
                            : ''}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );
    }

    // Extract bank info from the first few rows if available
    let bankName = '';
    let accountNumber = '';
    let dateRange = '';

    // Try to find bank info in the first rows
    for (let i = 0; i < Math.min(5, contentArray.length); i++) {
      const row = contentArray[i];
      if (row['RANDALPHA MICROFINANCE BANK LIMITED']) {
        if (i === 0) {
          bankName = 'RANDALPHA MICROFINANCE BANK LIMITED';
        } else if (row['RANDALPHA MICROFINANCE BANK LIMITED'] === 'Account Number' && row['Unnamed: 1']) {
          accountNumber = row['Unnamed: 1'];
        } else if (row['RANDALPHA MICROFINANCE BANK LIMITED'] === 'Financial Date Range: ' && row['Unnamed: 1']) {
          dateRange = row['Unnamed: 1'];
        }
      }
    }

    // Find the header row (the row with "Financial Date", "Transaction Date", etc.)
    let headerRow = null;
    let headerRowIndex = -1;

    for (let i = 0; i < contentArray.length; i++) {
      const row = contentArray[i];
      if (row['Financial Date'] && row['Transaction Date'] && row['Reference No.']) {
        headerRow = row;
        headerRowIndex = i;
        break;
      }
    }

    // If we found a header row, use it to display the data
    if (headerRow) {
      const headers = Object.keys(headerRow).filter(key => key !== 'undefined' && key !== '');
      const dataRows = contentArray.slice(headerRowIndex + 1);

      return (
        <div className="bg-white p-4 rounded border border-gray-200">
          <div className="mb-4">
            {bankName && (
              <h3 className="text-lg font-semibold text-gray-800 mb-1">{bankName}</h3>
            )}
            {accountNumber && (
              <p className="text-sm text-gray-600 mb-1">Account: {accountNumber}</p>
            )}
            {dateRange && (
              <p className="text-sm text-gray-600">Period: {dateRange}</p>
            )}

            {/* Show total rows info */}
            <p className="text-sm text-gray-600 mt-2">
              Total rows: {dataRows.length}
            </p>
          </div>

          {/* Scrollable container with fixed height and visible scrollbars */}
          <div className="overflow-auto custom-scrollbar" style={{
            maxHeight: '70vh',
            maxWidth: '100%',
            overflowY: 'scroll',
            overflowX: 'scroll',
            border: '1px solid #ddd',
            borderRadius: '4px'
          }}>
            <table className="min-w-full border-collapse">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  {/* Line number header */}
                  <th scope="col" className="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100 w-12">
                    #
                  </th>
                  {headers.map((header, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {dataRows.map((row, rowIndex) => (
                  <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    {/* Line number column */}
                    <td className="px-2 py-4 whitespace-nowrap text-xs text-gray-500 bg-gray-100 text-right font-mono w-12">
                      {rowIndex + 1}
                    </td>
                    {headers.map((header, colIndex) => {
                      const cellValue = row[header];
                      const isNumeric = !isNaN(parseFloat(cellValue)) && cellValue !== null && cellValue !== undefined && cellValue.toString().trim() !== '';

                      return (
                        <td
                          key={colIndex}
                          className={`px-6 py-4 whitespace-nowrap text-sm ${isNumeric ? 'text-right font-medium' : 'text-gray-500'}`}
                        >
                          {cellValue !== null && cellValue !== undefined ? cellValue : ''}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );
    }

    // If we didn't find a header row, display the raw content
    return (
      <div className="bg-white p-4 rounded border border-gray-200">
        <div className="mb-4">
          {bankName && (
            <h3 className="text-lg font-semibold text-gray-800 mb-1">{bankName}</h3>
          )}
          {accountNumber && (
            <p className="text-sm text-gray-600 mb-1">Account: {accountNumber}</p>
          )}
          {dateRange && (
            <p className="text-sm text-gray-600">Period: {dateRange}</p>
          )}

          {/* Show total rows info */}
          <p className="text-sm text-gray-600 mt-2">
            Total rows: {totalRows}
          </p>
        </div>

        {/* Scrollable container with fixed height and visible scrollbars */}
        <div className="overflow-auto custom-scrollbar" style={{
          maxHeight: '70vh',
          maxWidth: '100%',
          overflowY: 'scroll',
          overflowX: 'scroll',
          border: '1px solid #ddd',
          borderRadius: '4px'
        }}>
          <table className="min-w-full border-collapse">
            <tbody>
              {contentArray.map((row, rowIndex) => {
                const rowKeys = Object.keys(row).filter(key => key !== 'undefined' && key !== '');

                return (
                  <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    {/* Line number column */}
                    <td className="px-2 py-2 text-xs text-gray-500 border bg-gray-100 text-right font-mono w-12">
                      {rowIndex + 1}
                    </td>
                    {rowKeys.map((key, cellIndex) => {
                      const cellValue = row[key];
                      const isNumeric = !isNaN(parseFloat(cellValue)) && cellValue !== null && cellValue !== undefined && cellValue.toString().trim() !== '';

                      return (
                        <td
                          key={cellIndex}
                          className={`px-3 py-2 text-sm border ${
                            // Make header rows bold
                            rowIndex < 10 ? 'font-semibold bg-gray-100' : ''
                          } ${
                            // Align numbers to the right
                            isNumeric ? 'text-right' : ''
                          }`}
                        >
                          {cellValue !== null && cellValue !== undefined ? cellValue : ''}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  // Check if we have raw preview data from the enhanced parser
  if (content.rawPreview && Array.isArray(content.rawPreview) && content.rawPreview.length > 0) {
    // Use the raw preview data for a more accurate representation
    // Load all data at once with scrollbars
    const totalRawRows = content.rawPreview.length;

    return (
      <div className="bg-white p-4 rounded border border-gray-200">
        <div className="mb-4">
          {content.bankInfo && content.bankInfo.bankName && (
            <h3 className="text-lg font-semibold text-gray-800 mb-1">{content.bankInfo.bankName}</h3>
          )}
          {content.bankInfo && content.bankInfo.accountNumber && (
            <p className="text-sm text-gray-600 mb-1">Account: {content.bankInfo.accountNumber}</p>
          )}
          {content.bankInfo && content.bankInfo.dateRange && (
            <p className="text-sm text-gray-600">Period: {content.bankInfo.dateRange}</p>
          )}

          {/* Show total rows info */}
          <p className="text-sm text-gray-600 mt-2">
            Total rows: {totalRawRows}
          </p>
        </div>

        {/* Scrollable container with fixed height and visible scrollbars */}
        <div className="overflow-auto custom-scrollbar" style={{
          maxHeight: '70vh',
          maxWidth: '100%',
          overflowY: 'scroll',
          overflowX: 'scroll',
          border: '1px solid #ddd',
          borderRadius: '4px'
        }}>
          <table className="min-w-full border-collapse">
            <tbody>
              {content.rawPreview.map((row, rowIndex) => (
                <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  {/* Line number column */}
                  <td className="px-2 py-2 text-xs text-gray-500 border bg-gray-100 text-right font-mono w-12">
                    {rowIndex + 1}
                  </td>
                  {row.map((cell, cellIndex) => (
                    <td
                      key={cellIndex}
                      className={`px-3 py-2 text-sm border ${
                        // Make header rows bold
                        rowIndex < 3 ? 'font-semibold bg-gray-100' : ''
                      } ${
                        // Align numbers to the right
                        !isNaN(parseFloat(cell)) && cell.trim() !== '' ? 'text-right' : ''
                      }`}
                    >
                      {cell || ''}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  // Check if we have an error message in the data
  if (data.status === false && data.message) {
    return (
      <div className="bg-red-50 p-4 rounded border border-red-200">
        <div className="flex items-start">
          <FiAlertCircle className="h-5 w-5 text-red-400 mt-0.5 mr-2" />
          <div>
            <h3 className="text-sm font-medium text-red-800">Error loading preview</h3>
            <p className="mt-1 text-sm text-red-700">{data.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-3 inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // We don't need to show processing status anymore since Python/Rust processing is very fast
  // This block is intentionally removed

  // If processing failed, show error
  if (processingStatus === 'failed') {
    return (
      <div className="bg-white p-4 rounded border border-gray-200">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            {data.name || 'Excel File'}
          </h3>
          <p className="text-sm text-gray-600">
            {data.fileName || 'Failed to process file'}
          </p>
        </div>

        <div className="my-8 px-4">
          <ProgressLoader
            status="failed"
            progress={0}
            text="Failed to process Excel data"
            showPercentage={false}
          />
        </div>

        <div className="text-sm text-red-500 mt-4 text-center">
          <p>{data.error || 'An error occurred while processing the file.'}</p>
        </div>
      </div>
    );
  }

  // If we don't have raw preview or parsed content, show the text preview
  if (!content.sheets || !content.data) {
    return (
      <div className="bg-white p-4 rounded border border-gray-200">
        <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto">
          {data.preview || 'No preview available'}
        </pre>

        {/* Show fetch time if available */}
        {fetchTime > 0 && (
          <div className="mt-4 text-xs text-gray-500 text-right">
            Fetched in {fetchTime}ms
          </div>
        )}
      </div>
    );
  }

  const { sheets } = content;
  const currentSheetName = sheets[activeSheet] || 'Sheet1';
  const sheetData = content.data[currentSheetName] || [];
  const headers = sheetData.length > 0 ? Object.keys(sheetData[0]) : [];

  const handleSheetChange = (index) => {
    setActiveSheet(index);
  };

  // Get data types for the current sheet
  const getSheetDataTypes = () => {
    if (!summary.dataTypes) return {};

    // Handle nested data types structure
    if (summary.dataTypes[currentSheetName]) {
      return summary.dataTypes[currentSheetName];
    }

    return {};
  };

  // Get statistics for the current sheet
  const getSheetStatistics = () => {
    if (!summary.statistics) return {};

    // Handle nested statistics structure
    if (summary.statistics[currentSheetName]) {
      return summary.statistics[currentSheetName];
    }

    return {};
  };

  const dataTypes = getSheetDataTypes();
  const statistics = getSheetStatistics();

  // Get the data type for a column
  const getDataType = (header) => {
    if (!dataTypes[header]) return null;

    const type = dataTypes[header];
    if (type === 'number') return 'Number';
    if (type === 'string') return 'Text';
    if (type === 'date') return 'Date';
    if (type === 'boolean') return 'Boolean';
    if (type.includes('/')) return 'Mixed';
    return type;
  };

  // Get the color for a data type
  const getTypeColor = (type) => {
    if (!type) return 'bg-gray-100';
    if (type === 'Number') return 'bg-blue-100 text-blue-800';
    if (type === 'Text') return 'bg-green-100 text-green-800';
    if (type === 'Date') return 'bg-purple-100 text-purple-800';
    if (type === 'Boolean') return 'bg-yellow-100 text-yellow-800';
    if (type === 'Mixed') return 'bg-red-100 text-red-800';
    return 'bg-gray-100';
  };

  // Get statistics for a column
  const getColumnStats = (header) => {
    if (!statistics[header]) return null;

    return statistics[header];
  };

  return (
    <div className="border rounded-md overflow-hidden">
      {/* Sheet tabs */}
      {sheets.length > 0 && (
        <div className="bg-gray-100 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div className="flex overflow-x-auto">
              {sheets.map((sheet, index) => (
                <button
                  key={index}
                  onClick={() => handleSheetChange(index)}
                  className={`px-4 py-2 text-sm font-medium ${
                    index === activeSheet
                      ? 'bg-white border-t border-l border-r border-gray-200 text-indigo-600'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {sheet}
                </button>
              ))}
            </div>

            {/* Show fetch time if available */}
            {fetchTime > 0 && processingStatus === 'completed' && (
              <div className="px-3 text-xs text-gray-500">
                Fetched in {fetchTime}ms
              </div>
            )}
          </div>
        </div>
      )}

      {/* Data type toggle */}
      {Object.keys(dataTypes).length > 0 && (
        <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex justify-between items-center">
          <div className="flex items-center">
            <FiInfo className="text-gray-500 mr-2" />
            <span className="text-sm text-gray-600">
              {Object.keys(dataTypes).length} columns detected in {currentSheetName}
            </span>
          </div>
          <button
            onClick={() => setShowDataTypes(!showDataTypes)}
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            {showDataTypes ? 'Hide Data Types' : 'Show Data Types'}
          </button>
        </div>
      )}

      {/* Data type info */}
      {showDataTypes && Object.keys(dataTypes).length > 0 && (
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
            <FiBarChart2 className="mr-1" /> Column Data Types
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            {headers.map((header) => {
              const type = getDataType(header);
              const stats = getColumnStats(header);

              return (
                <div key={header} className="bg-white p-2 rounded border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div className="font-medium text-sm truncate" title={header}>
                      {header}
                    </div>
                    {type && (
                      <span className={`text-xs px-1.5 py-0.5 rounded ${getTypeColor(type)}`}>
                        {type}
                      </span>
                    )}
                  </div>

                  {stats && stats.min !== undefined && (
                    <div className="mt-1 text-xs text-gray-500">
                      <div className="flex justify-between">
                        <span>Min:</span>
                        <span className="font-medium">{stats.min}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Max:</span>
                        <span className="font-medium">{stats.max}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Avg:</span>
                        <span className="font-medium">{stats.average.toFixed(2)}</span>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Table with visible scrollbars */}
      <div className="overflow-auto custom-scrollbar" style={{
        maxHeight: '70vh',
        maxWidth: '100%',
        overflowY: 'scroll',
        overflowX: 'scroll',
        border: '1px solid #ddd',
        borderRadius: '4px'
      }}>
        {headers.length > 0 ? (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {/* Line number header */}
                <th scope="col" className="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100 w-12">
                  #
                </th>
                {headers.map((header, index) => (
                  <th
                    key={index}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header}
                    {showDataTypes && getDataType(header) && (
                      <span className={`ml-1 text-xs px-1.5 py-0.5 rounded ${getTypeColor(getDataType(header))}`}>
                        {getDataType(header)}
                      </span>
                    )}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sheetData.map((row, rowIndex) => (
                <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  {/* Line number column */}
                  <td className="px-2 py-4 whitespace-nowrap text-xs text-gray-500 bg-gray-100 text-right font-mono w-12">
                    {rowIndex + 1}
                  </td>
                  {headers.map((header, colIndex) => (
                    <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {row[header] || ''}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="p-4 text-center text-gray-500">
            No data available for this sheet
          </div>
        )}
      </div>

      {/* Total rows info */}
      {headers.length > 0 && (
        <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
          <p className="text-sm text-gray-700">
            Total rows: <span className="font-medium">{sheetData.length}</span>
          </p>
        </div>
      )}
    </div>
  );
};

export default ExcelPreview;
