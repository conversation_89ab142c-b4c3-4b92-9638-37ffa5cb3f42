"""
Authentication controller for the Bank Reconciliation API.
"""

from flask import request, jsonify
import jwt
from datetime import datetime, timedelta
from models.user_model import User
from config import config

def register():
    """
    Register a new user.
    """
    try:
        data = request.get_json()
        
        # Check if required fields are provided
        required_fields = ['email', 'password', 'firstName', 'lastName']
        for field in required_fields:
            if field not in data:
                return jsonify({'message': f'{field} is required'}), 400
        
        # Check if user already exists
        existing_user = User.find_by_email(data['email'])
        if existing_user:
            return jsonify({'message': 'User already exists'}), 400
        
        # Create new user
        user = User(
            email=data['email'],
            password=data['password'],
            first_name=data['firstName'],
            last_name=data['lastName'],
            role=data.get('role', 'user')
        )
        
        # Save user to database
        user.save()
        
        # Generate JWT token
        token = generate_token(user)
        
        return jsonify({
            'status': True,
            'message': 'User registered successfully',
            'data': {
                'user': user.to_dict(),
                'token': token
            }
        }), 201
    
    except Exception as e:
        print(f"Error registering user: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

def login():
    """
    Login user.
    """
    try:
        data = request.get_json()
        
        # Check if required fields are provided
        if 'email' not in data or 'password' not in data:
            return jsonify({'message': 'Email and password are required'}), 400
        
        # Find user by email
        user = User.find_by_email(data['email'])
        if not user:
            return jsonify({'message': 'Invalid credentials'}), 401
        
        # Check password
        if not user.compare_password(data['password']):
            return jsonify({'message': 'Invalid credentials'}), 401
        
        # Generate JWT token
        token = generate_token(user)
        
        return jsonify({
            'status': True,
            'message': 'Login successful',
            'data': {
                'user': user.to_dict(),
                'token': token
            }
        }), 200
    
    except Exception as e:
        print(f"Error logging in: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

def generate_token(user):
    """
    Generate JWT token.
    """
    payload = {
        'id': str(user._id),
        'email': user.email,
        'role': user.role,
        'exp': datetime.utcnow() + timedelta(seconds=config['jwt_expiration'])
    }
    
    return jwt.encode(payload, config['jwt_secret'], algorithm='HS256')
