import express from 'express';
import {
  getAllStatements,
  getStatementById,
  deleteStatement,
  uploadStatement,
  parseStatementFile,
  getStatementPreview,
  downloadStatementFile
} from '../controllers/statement.controller';

const router = express.Router();

// @route   GET /api/statements
// @desc    Get all statements
// @access  Private
router.get('/', getAllStatements);

// @route   GET /api/statements/:id
// @desc    Get statement by ID
// @access  Private
router.get('/:id', getStatementById);

// @route   DELETE /api/statements/:id
// @desc    Delete statement
// @access  Private
router.delete('/:id', deleteStatement);

// @route   POST /api/statements/upload
// @desc    Upload statement
// @access  Private
router.post('/upload', uploadStatement);

// @route   POST /api/statements/parse
// @desc    Parse statement file
// @access  Private
router.post('/parse', parseStatementFile);

// @route   GET /api/statements/:id/preview
// @desc    Get statement preview
// @access  Private
router.get('/:id/preview', getStatementPreview);

// @route   GET /api/statements/:id/download
// @desc    Download statement file
// @access  Private
router.get('/:id/download', downloadStatementFile);

export default router;



