"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
// StatementData schema
const statementDataSchema = new mongoose_1.Schema({
    statementId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Statement',
        required: [true, 'Statement ID is required'],
    },
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User ID is required'],
    },
    status: {
        type: String,
        enum: ['processing', 'completed', 'failed'],
        default: 'processing',
    },
    content: {
        type: mongoose_1.Schema.Types.Mixed,
        required: [true, 'Content is required'],
    },
    metadata: {
        fileType: {
            type: String,
            required: [true, 'File type is required'],
        },
        fileName: {
            type: String,
            required: [true, 'File name is required'],
        },
        fileSize: {
            type: Number,
            required: [true, 'File size is required'],
        },
        uploadDate: {
            type: Date,
            default: Date.now,
        },
        processingTime: Number,
        pageCount: Number,
        rowCount: Number,
        columnCount: Number,
    },
    summary: {
        startDate: Date,
        endDate: Date,
        startBalance: Number,
        endBalance: Number,
        totalCredits: Number,
        totalDebits: Number,
        transactionCount: Number,
    },
    insights: mongoose_1.Schema.Types.Mixed,
}, {
    timestamps: true,
});
// Create and export StatementData model
exports.default = mongoose_1.default.model('StatementData', statementDataSchema);
