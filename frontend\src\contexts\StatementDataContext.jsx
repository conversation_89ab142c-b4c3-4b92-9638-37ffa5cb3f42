import React, { createContext, useContext, useState, useCallback } from 'react';
import api from '../services/api';

// Create context
const StatementDataContext = createContext();

/**
 * Provider component for statement data
 * This context provides a way to cache statement preview data
 * so it doesn't need to be fetched multiple times
 */
export const StatementDataProvider = ({ children }) => {
  // Cache for statement preview data
  const [previewCache, setPreviewCache] = useState({});
  // Loading states for each statement
  const [loadingStates, setLoadingStates] = useState({});
  // Error states for each statement
  const [errorStates, setErrorStates] = useState({});

  /**
   * Get statement preview data
   * Will use cached data if available, otherwise fetch from API
   * @param {string} statementId - The ID of the statement
   * @param {boolean} forceRefresh - Whether to force a refresh from the API
   * @returns {Promise<Object>} - The statement preview data
   */
  const getStatementPreview = useCallback(async (statementId, forceRefresh = false) => {
    // If we're already loading this statement, return a promise that will resolve when it's done
    if (loadingStates[statementId] && !forceRefresh) {
      return new Promise((resolve, reject) => {
        // Check every 100ms if the data is loaded
        const checkInterval = setInterval(() => {
          if (!loadingStates[statementId]) {
            clearInterval(checkInterval);
            if (errorStates[statementId]) {
              reject(errorStates[statementId]);
            } else if (previewCache[statementId]) {
              resolve(previewCache[statementId]);
            }
          }
        }, 100);
        
        // Timeout after 10 seconds
        setTimeout(() => {
          clearInterval(checkInterval);
          reject(new Error('Timeout waiting for statement data'));
        }, 10000);
      });
    }
    
    // If we have cached data and don't need to refresh, return it
    if (previewCache[statementId] && !forceRefresh) {
      console.log(`Using cached preview data for statement ${statementId}`);
      return previewCache[statementId];
    }
    
    try {
      // Set loading state
      setLoadingStates(prev => ({ ...prev, [statementId]: true }));
      // Clear any previous errors
      setErrorStates(prev => ({ ...prev, [statementId]: null }));
      
      console.log(`Fetching preview data for statement ${statementId}`);
      const response = await api.statement.getStatementPreview(statementId);
      
      // Cache the response data
      if (response && response.status === true && response.data) {
        setPreviewCache(prev => ({ ...prev, [statementId]: response.data }));
      }
      
      // Clear loading state
      setLoadingStates(prev => ({ ...prev, [statementId]: false }));
      
      return response.data;
    } catch (error) {
      console.error(`Error fetching statement preview for ${statementId}:`, error);
      // Set error state
      setErrorStates(prev => ({ ...prev, [statementId]: error }));
      // Clear loading state
      setLoadingStates(prev => ({ ...prev, [statementId]: false }));
      throw error;
    }
  }, [previewCache, loadingStates, errorStates]);
  
  /**
   * Clear the cache for a specific statement or all statements
   * @param {string} statementId - The ID of the statement to clear, or null to clear all
   */
  const clearCache = useCallback((statementId = null) => {
    if (statementId) {
      setPreviewCache(prev => {
        const newCache = { ...prev };
        delete newCache[statementId];
        return newCache;
      });
      setLoadingStates(prev => {
        const newStates = { ...prev };
        delete newStates[statementId];
        return newStates;
      });
      setErrorStates(prev => {
        const newStates = { ...prev };
        delete newStates[statementId];
        return newStates;
      });
    } else {
      setPreviewCache({});
      setLoadingStates({});
      setErrorStates({});
    }
  }, []);
  
  // Context value
  const value = {
    previewCache,
    loadingStates,
    errorStates,
    getStatementPreview,
    clearCache,
  };
  
  return (
    <StatementDataContext.Provider value={value}>
      {children}
    </StatementDataContext.Provider>
  );
};

/**
 * Hook to use the statement data context
 * @returns {Object} - The statement data context
 */
export const useStatementData = () => {
  const context = useContext(StatementDataContext);
  if (!context) {
    throw new Error('useStatementData must be used within a StatementDataProvider');
  }
  return context;
};

export default StatementDataContext;
