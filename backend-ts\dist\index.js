"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const config_1 = __importDefault(require("./config"));
const database_1 = __importDefault(require("./config/database"));
const auth_middleware_1 = require("./middleware/auth.middleware");
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
const transaction_routes_1 = __importDefault(require("./routes/transaction.routes"));
const statement_routes_1 = __importDefault(require("./routes/statement.routes"));
const reconciliation_routes_1 = __importDefault(require("./routes/reconciliation.routes"));
const health_routes_1 = __importDefault(require("./routes/health.routes"));
const file_routes_1 = __importDefault(require("./routes/file.routes"));
// Initialize express app
const app = (0, express_1.default)();
// Connect to MongoDB
(0, database_1.default)();
// Middleware
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
// Configure CORS for development
app.use((0, cors_1.default)({
    origin: 'http://localhost:5173', // Specify the exact frontend origin
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With']
}));
// Logging middleware
app.use((0, morgan_1.default)('dev'));
// Apply conditional auth middleware to all routes
app.use(auth_middleware_1.conditionalAuth);
// API Routes
app.use('/api/auth', auth_routes_1.default);
app.use('/api/users', user_routes_1.default);
app.use('/api/transactions', transaction_routes_1.default);
app.use('/api/statements', statement_routes_1.default);
app.use('/api/reconciliations', reconciliation_routes_1.default);
app.use('/api/health', health_routes_1.default);
app.use('/api/files', file_routes_1.default);
// Start the server
const PORT = config_1.default.port;
app.listen(PORT, () => {
    console.log(`Server running in ${config_1.default.nodeEnv} mode on port ${PORT}`);
});
// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
    console.log(`Error: ${err.message}`);
    // Close server & exit process
    process.exit(1);
});
