import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  FiRefreshCw,
  FiFileText,
  FiAlertCircle,
  FiCheck,
  FiCheckCircle,
  FiDollarSign,
  FiArrowLeft,
  FiLink,
  FiX
} from 'react-icons/fi';
import { motion } from 'framer-motion';
import useReconciliationStore from '../../store/reconciliationStore';
import { format } from 'date-fns';

const ReconciliationDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const {
    currentReconciliation,
    fetchReconciliationById,
    fetchReconciliationTransactions,
    completeReconciliation,
    matchTransactions,
    unmatchTransaction,
    reconciliationsLoading,
    transactionsLoading,
    reconciliationsError,
    transactionsError,
    transactions
  } = useReconciliationStore();

  const [bankTransactions, setBankTransactions] = useState([]);
  const [cashbookTransactions, setCashbookTransactions] = useState([]);
  const [selectedBankTransaction, setSelectedBankTransaction] = useState(null);
  const [selectedCashbookTransaction, setSelectedCashbookTransaction] = useState(null);
  const [showMatchModal, setShowMatchModal] = useState(false);
  const [showUnmatchModal, setShowUnmatchModal] = useState(false);
  const [transactionToUnmatch, setTransactionToUnmatch] = useState(null);
  const [showCompleteModal, setShowCompleteModal] = useState(false);
  const [matchError, setMatchError] = useState(null);
  const [bankSearchTerm, setBankSearchTerm] = useState('');
  const [cashbookSearchTerm, setCashbookSearchTerm] = useState('');

  useEffect(() => {
    const loadReconciliation = async () => {
      try {
        await fetchReconciliationById(id);
        await fetchReconciliationTransactions(id);
      } catch (error) {
        console.error('Error loading reconciliation:', error);
      }
    };

    loadReconciliation();
  }, [id, fetchReconciliationById, fetchReconciliationTransactions]);

  useEffect(() => {
    if (transactions) {
      const bankTxs = transactions.filter(t => t.source === 'bank');
      const cashbookTxs = transactions.filter(t => t.source === 'cashbook');

      setBankTransactions(bankTxs);
      setCashbookTransactions(cashbookTxs);
    }
  }, [transactions]);

  const handleBankTransactionClick = (transaction) => {
    if (transaction.status === 'reconciled') return;

    setSelectedBankTransaction(transaction);
    if (selectedCashbookTransaction) {
      setShowMatchModal(true);
    }
  };

  const handleCashbookTransactionClick = (transaction) => {
    if (transaction.status === 'reconciled') return;

    setSelectedCashbookTransaction(transaction);
    if (selectedBankTransaction) {
      setShowMatchModal(true);
    }
  };

  const handleUnmatchClick = (transaction) => {
    setTransactionToUnmatch(transaction);
    setShowUnmatchModal(true);
  };

  const handleCompleteClick = () => {
    setShowCompleteModal(true);
  };

  const confirmMatch = async () => {
    if (selectedBankTransaction && selectedCashbookTransaction) {
      try {
        setMatchError(null);
        await matchTransactions({
          bank_transaction_id: selectedBankTransaction.id,
          cashbook_transaction_id: selectedCashbookTransaction.id
        });

        // Refresh transactions
        await fetchReconciliationTransactions(id);

        // Reset selections
        setSelectedBankTransaction(null);
        setSelectedCashbookTransaction(null);
        setShowMatchModal(false);
      } catch (error) {
        setMatchError(error.message || 'Failed to match transactions');
      }
    }
  };

  const confirmUnmatch = async () => {
    if (transactionToUnmatch) {
      try {
        await unmatchTransaction(transactionToUnmatch.id);

        // Refresh transactions
        await fetchReconciliationTransactions(id);

        setTransactionToUnmatch(null);
        setShowUnmatchModal(false);
      } catch (error) {
        console.error('Error unmatching transaction:', error);
      }
    }
  };

  const confirmComplete = async () => {
    try {
      await completeReconciliation(id);

      // Refresh reconciliation
      await fetchReconciliationById(id);

      setShowCompleteModal(false);
    } catch (error) {
      console.error('Error completing reconciliation:', error);
    }
  };

  const cancelMatch = () => {
    setShowMatchModal(false);
    setMatchError(null);
  };

  const cancelUnmatch = () => {
    setShowUnmatchModal(false);
    setTransactionToUnmatch(null);
  };

  const cancelComplete = () => {
    setShowCompleteModal(false);
  };

  const clearSelections = () => {
    setSelectedBankTransaction(null);
    setSelectedCashbookTransaction(null);
  };

  // Filter transactions based on search terms
  const filteredBankTransactions = bankTransactions.filter(t =>
    t.description.toLowerCase().includes(bankSearchTerm.toLowerCase()) ||
    (t.reference && t.reference.toLowerCase().includes(bankSearchTerm.toLowerCase())) ||
    t.amount.toString().includes(bankSearchTerm)
  );

  const filteredCashbookTransactions = cashbookTransactions.filter(t =>
    t.description.toLowerCase().includes(cashbookSearchTerm.toLowerCase()) ||
    (t.reference && t.reference.toLowerCase().includes(cashbookSearchTerm.toLowerCase())) ||
    t.amount.toString().includes(cashbookSearchTerm)
  );

  // Loading state
  if (reconciliationsLoading && !currentReconciliation) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading reconciliation...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (reconciliationsError) {
    return (
      <div className="bg-red-50 p-6 rounded-lg shadow-sm">
        <div className="flex items-center">
          <FiAlertCircle className="text-red-500 mr-3" size={24} />
          <h2 className="text-lg font-medium text-red-800">Error Loading Reconciliation</h2>
        </div>
        <p className="mt-2 text-red-700">{reconciliationsError}</p>
        <button
          onClick={() => navigate('/reconciliations')}
          className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
        >
          Back to Reconciliations
        </button>
      </div>
    );
  }

  if (!currentReconciliation) {
    return (
      <div className="bg-yellow-50 p-6 rounded-lg shadow-sm">
        <div className="flex items-center">
          <FiAlertCircle className="text-yellow-500 mr-3" size={24} />
          <h2 className="text-lg font-medium text-yellow-800">Reconciliation Not Found</h2>
        </div>
        <p className="mt-2 text-yellow-700">The requested reconciliation could not be found.</p>
        <button
          onClick={() => navigate('/reconciliations')}
          className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
        >
          Back to Reconciliations
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <button
          onClick={() => navigate('/reconciliations')}
          className="flex items-center text-gray-600 hover:text-gray-800"
        >
          <FiArrowLeft className="mr-2" />
          Back to Reconciliations
        </button>
      </div>

      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Reconciliation for {format(new Date(currentReconciliation.period_start), 'MMMM yyyy')}
          </h1>
          <div className="flex items-center space-x-4">
            {currentReconciliation.status !== 'completed' && (
              <button
                onClick={handleCompleteClick}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                <FiCheckCircle className="inline-block mr-2" />
                Complete Reconciliation
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Bank Transactions */}
          <div className="border rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-4">Bank Transactions</h2>
            <input
              type="text"
              placeholder="Search bank transactions..."
              value={bankSearchTerm}
              onChange={(e) => setBankSearchTerm(e.target.value)}
              className="w-full p-2 mb-4 border rounded"
            />
            <div className="h-[600px] overflow-y-auto">
              {filteredBankTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  onClick={() => handleBankTransactionClick(transaction)}
                  className={`p-4 mb-2 rounded cursor-pointer ${
                    transaction.status === 'reconciled'
                      ? 'bg-green-50'
                      : transaction.id === selectedBankTransaction?.id
                      ? 'bg-blue-50'
                      : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-gray-600">
                        {format(new Date(transaction.date), 'dd MMM yyyy')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${
                        transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.amount >= 0 ? '+' : ''}{transaction.amount}
                      </p>
                      {transaction.status === 'reconciled' && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUnmatchClick(transaction);
                          }}
                          className="text-sm text-red-600 hover:text-red-800"
                        >
                          <FiX className="inline-block mr-1" />
                          Unmatch
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Cashbook Transactions */}
          <div className="border rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-4">Cashbook Transactions</h2>
            <input
              type="text"
              placeholder="Search cashbook transactions..."
              value={cashbookSearchTerm}
              onChange={(e) => setCashbookSearchTerm(e.target.value)}
              className="w-full p-2 mb-4 border rounded"
            />
            <div className="h-[600px] overflow-y-auto">
              {filteredCashbookTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  onClick={() => handleCashbookTransactionClick(transaction)}
                  className={`p-4 mb-2 rounded cursor-pointer ${
                    transaction.status === 'reconciled'
                      ? 'bg-green-50'
                      : transaction.id === selectedCashbookTransaction?.id
                      ? 'bg-blue-50'
                      : 'bg-gray-50 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-gray-600">
                        {format(new Date(transaction.date), 'dd MMM yyyy')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${
                        transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.amount >= 0 ? '+' : ''}{transaction.amount}
                      </p>
                      {transaction.status === 'reconciled' && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUnmatchClick(transaction);
                          }}
                          className="text-sm text-red-600 hover:text-red-800"
                        >
                          <FiX className="inline-block mr-1" />
                          Unmatch
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Match Modal */}
      {showMatchModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full">
            <h3 className="text-lg font-semibold mb-4">Confirm Match</h3>
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded">
                <p className="font-medium">Bank Transaction</p>
                <p>{selectedBankTransaction.description}</p>
                <p className="text-green-600 font-semibold">{selectedBankTransaction.amount}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded">
                <p className="font-medium">Cashbook Transaction</p>
                <p>{selectedCashbookTransaction.description}</p>
                <p className="text-green-600 font-semibold">{selectedCashbookTransaction.amount}</p>
              </div>
              {matchError && (
                <div className="bg-red-50 text-red-700 p-4 rounded">
                  {matchError}
                </div>
              )}
            </div>
            <div className="mt-6 flex justify-end space-x-4">
              <button
                onClick={cancelMatch}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={confirmMatch}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Confirm Match
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Unmatch Modal */}
      {showUnmatchModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full">
            <h3 className="text-lg font-semibold mb-4">Confirm Unmatch</h3>
            <p>Are you sure you want to unmatch this transaction?</p>
            <div className="mt-6 flex justify-end space-x-4">
              <button
                onClick={cancelUnmatch}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={confirmUnmatch}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Confirm Unmatch
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Complete Modal */}
      {showCompleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full">
            <h3 className="text-lg font-semibold mb-4">Complete Reconciliation</h3>
            <p>Are you sure you want to mark this reconciliation as complete?</p>
            <div className="mt-6 flex justify-end space-x-4">
              <button
                onClick={cancelComplete}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={confirmComplete}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Complete Reconciliation
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReconciliationDetailPage;
