"""
Statement Data Service for the Bank Reconciliation API.
"""

import time
from datetime import datetime
from bson import ObjectId
from models.statement_model import Statement
from models.statement_data_model import StatementData
from utils.file_parser import parse_file

def process_and_store_statement_data(statement_id, user_id):
    """
    Process and store statement data in MongoDB.
    """
    try:
        # Find statement
        statement = Statement.find_by_id(statement_id)
        if not statement:
            raise ValueError(f"Statement not found: {statement_id}")

        # Check if statement data already exists
        statement_data = StatementData.find_by_statement_id(statement_id)
        if not statement_data:
            # Create new statement data
            statement_data = StatementData(
                statement_id=statement_id,
                user_id=user_id,
                status='processing',
                metadata={
                    'fileType': statement.file_name.split('.')[-1].lower(),
                    'fileName': statement.file_name,
                    'fileSize': 0,
                    'uploadDate': datetime.now()
                }
            )
            statement_data.save()
        else:
            # Update status to processing
            statement_data.status = 'processing'
            statement_data.save()

        # Start processing time
        start_time = time.time()

        # Parse the file
        file_data = parse_file(statement.file_path)

        # Extract and format the data
        formatted_data = format_statement_data(file_data)

        # Update statement data with the processed content
        statement_data.content = formatted_data
        statement_data.status = 'completed'
        statement_data.metadata = {
            **statement_data.metadata,
            'fileSize': file_data['metadata']['fileSize'],
            'processingTime': time.time() - start_time,
            'pageCount': file_data['content'].get('pageCount', 0),
            'rowCount': file_data['summary'].get('rowCount', 0),
            'columnCount': file_data['summary'].get('columnCount', 0)
        }

        # Update summary
        if 'summary' in file_data:
            statement_data.summary = {
                'startDate': file_data['summary'].get('startDate'),
                'endDate': file_data['summary'].get('endDate'),
                'startBalance': file_data['summary'].get('startBalance'),
                'endBalance': file_data['summary'].get('endBalance'),
                'totalCredits': file_data['summary'].get('totalCredits'),
                'totalDebits': file_data['summary'].get('totalDebits'),
                'transactionCount': file_data['summary'].get('rowCount')
            }

        # Save statement data
        statement_data.save()

        return statement_data

    except Exception as e:
        # Update statement data status to failed
        if statement_data:
            statement_data.status = 'failed'
            statement_data.save()

        print(f"Error processing statement data: {str(e)}")
        raise

def format_statement_data(file_data):
    """
    Format statement data for storage.
    """
    # Extract bank info
    bank_name = None
    account_number = None
    content = []
    summary = {}

    # Extract bank info from metadata
    if 'metadata' in file_data and 'title' in file_data['metadata']:
        bank_name = file_data['metadata']['title']

    if 'metadata' in file_data and 'subject' in file_data['metadata']:
        subject = file_data['metadata']['subject']
        if subject and 'Account:' in subject:
            account_number = subject.replace('Account:', '').strip()

    # Handle Excel files
    if file_data['metadata']['fileType'] in ['xlsx', 'xls']:
        # For Excel, extract data from the first sheet or the one with transactions
        sheet_names = file_data['content'].get('sheets', [])
        if sheet_names:
            first_sheet_name = sheet_names[0]
            sheet_data = file_data['content']['data'].get(first_sheet_name, [])

            # Format the data according to the standardized structure
            content = format_excel_data(sheet_data)

            # Calculate summary
            summary = calculate_summary(content)

    # Handle CSV files
    elif file_data['metadata']['fileType'] == 'csv':
        # For CSV, extract data from rows
        rows = file_data['content'].get('rows', [])

        # Format the data according to the standardized structure
        content = format_csv_data(rows)

        # Calculate summary
        summary = calculate_summary(content)

    # Handle PDF files
    elif file_data['metadata']['fileType'] == 'pdf':
        # For PDF, extract data from tables
        tables = file_data['content'].get('tables', [])
        if tables:
            # Format the data according to the standardized structure
            content = format_pdf_data(tables)

            # Calculate summary
            summary = calculate_summary(content)

    return {
        'bankName': bank_name,
        'accountNumber': account_number,
        'content': content,
        'summary': summary,
        'rawData': file_data['content']
    }

def format_excel_data(sheet_data):
    """
    Format Excel data to standardized format.
    """
    # Map Excel data to standardized format
    return [map_to_standard_format(row) for row in sheet_data]

def format_csv_data(rows):
    """
    Format CSV data to standardized format.
    """
    # Map CSV data to standardized format
    return [map_to_standard_format(row) for row in rows]

def format_pdf_data(tables):
    """
    Format PDF data to standardized format.
    """
    all_rows = []

    # Collect all rows from all tables
    for table in tables:
        all_rows.extend(table.get('rows', []))

    # Map PDF data to standardized format
    return [map_to_standard_format(row) for row in all_rows]

def map_to_standard_format(row):
    """
    Map row data to standardized format.
    """
    # Try to map common column headers to standardized format
    financial_date = row.get('EntryDate', row.get('Entry Date', row.get('Transaction Date', '')))
    transaction_date = row.get('ValueDate', row.get('Value Date', row.get('Date', '')))
    reference_no = row.get('Reference No.', row.get('Reference', row.get('Ref', '')))
    instrument_no = row.get('Instrument No.', row.get('Cheque No.', row.get('Cheque', '')))
    narration = row.get('Details', row.get('Description', row.get('Narration', '')))
    username = row.get('Username', row.get('User', ''))

    # Handle numeric values
    debit = 0
    credit = 0
    balance = 0

    # Try to extract debit amount
    debit_val = row.get('Debit', row.get('DR', row.get('Withdrawal', 0)))
    if isinstance(debit_val, (int, float)):
        debit = debit_val
    elif isinstance(debit_val, str) and debit_val.strip():
        try:
            debit = float(debit_val.replace(',', ''))
        except ValueError:
            pass

    # Try to extract credit amount
    credit_val = row.get('Credit', row.get('CR', row.get('Deposit', 0)))
    if isinstance(credit_val, (int, float)):
        credit = credit_val
    elif isinstance(credit_val, str) and credit_val.strip():
        try:
            credit = float(credit_val.replace(',', ''))
        except ValueError:
            pass

    # Try to extract balance
    balance_val = row.get('Balance', row.get('Avail. Bal', row.get('Available Balance', 0)))
    if isinstance(balance_val, (int, float)):
        balance = balance_val
    elif isinstance(balance_val, str) and balance_val.strip():
        try:
            balance = float(balance_val.replace(',', ''))
        except ValueError:
            pass

    return {
        'Financial Date': financial_date,
        'Transaction Date': transaction_date,
        'Reference No.': reference_no,
        'Instrument No.': instrument_no,
        'Narration': narration,
        'Username': username,
        'DR': debit,
        'CR': credit,
        'Avail. Bal': balance,
        'Entry Code': row.get('Entry Code', ''),
        # Store original row data for reference
        '_original': row
    }

def calculate_summary(content):
    """
    Calculate summary from content.
    """
    summary = {
        'startDate': None,
        'endDate': None,
        'startBalance': None,
        'endBalance': None,
        'totalCredits': 0,
        'totalDebits': 0,
        'transactionCount': len(content)
    }

    if not content:
        return summary

    # Sort by transaction date if available
    sorted_content = sorted(
        content,
        key=lambda x: x.get('Transaction Date', x.get('Financial Date', '')),
        reverse=False
    )

    # Get start and end dates
    if sorted_content:
        first_transaction = sorted_content[0]
        last_transaction = sorted_content[-1]

        summary['startDate'] = first_transaction.get('Transaction Date', first_transaction.get('Financial Date', None))
        summary['endDate'] = last_transaction.get('Transaction Date', last_transaction.get('Financial Date', None))

        # Get start and end balances
        summary['startBalance'] = first_transaction.get('Avail. Bal', None)
        summary['endBalance'] = last_transaction.get('Avail. Bal', None)

    # Calculate total credits and debits
    for transaction in content:
        summary['totalCredits'] += transaction.get('CR', 0)
        summary['totalDebits'] += transaction.get('DR', 0)

    return summary

def get_statement_data(statement_id, projection=None):
    """
    Get statement data by statement ID.
    """
    statement_data = StatementData.find_by_statement_id(statement_id)
    if not statement_data:
        return None

    # Apply projection if provided
    if projection:
        result = {}
        for field in projection:
            if hasattr(statement_data, field):
                result[field] = getattr(statement_data, field)
        return result

    return statement_data
