/**
 * File Parser Engine
 * 
 * This is a compiled JavaScript file that handles Excel and CSV parsing
 * without requiring additional dependencies in the Python backend.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

/**
 * Parse a file based on its extension
 * @param {string} filePath - Path to the file
 * @returns {Object} Parsed file data
 */
function parseFile(filePath) {
  try {
    const stats = fs.statSync(filePath);
    const fileName = path.basename(filePath);
    const fileExt = path.extname(filePath).toLowerCase();
    
    // Determine parser based on file extension
    if (fileExt === '.csv') {
      return parseCSV(filePath, stats, fileName);
    } else if (fileExt === '.xlsx' || fileExt === '.xls') {
      return parseExcel(filePath, stats, fileName);
    } else {
      throw new Error(`Unsupported file type: ${fileExt}`);
    }
  } catch (error) {
    console.error('Error parsing file:', error);
    throw error;
  }
}

/**
 * Parse CSV file
 * @param {string} filePath - Path to the CSV file
 * @param {Object} stats - File stats
 * @param {string} fileName - File name
 * @returns {Object} Parsed CSV data
 */
function parseCSV(filePath, stats, fileName) {
  try {
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // Split into lines
    const lines = fileContent.split(/\r?\n/).filter(line => line.trim());
    
    // Get headers from first line
    const headers = lines[0].split(',').map(header => header.trim());
    
    // Parse rows
    const rows = [];
    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue;
      
      // Handle quoted values with commas inside
      const values = parseCSVLine(lines[i]);
      
      // Create row object
      const row = {};
      for (let j = 0; j < Math.min(headers.length, values.length); j++) {
        row[headers[j]] = values[j];
      }
      
      rows.push(row);
    }
    
    // Calculate statistics
    const summary = calculateSummary(rows);
    
    return {
      metadata: {
        fileName,
        fileSize: stats.size,
        fileType: 'csv',
        lastModified: stats.mtime
      },
      content: {
        headers,
        rows,
        totalRows: rows.length
      },
      preview: lines.slice(0, 10).join('\n'),
      summary: {
        rowCount: rows.length,
        columnCount: headers.length,
        ...summary
      }
    };
  } catch (error) {
    console.error('Error parsing CSV:', error);
    throw error;
  }
}

/**
 * Parse a CSV line handling quoted values
 * @param {string} line - CSV line to parse
 * @returns {string[]} Array of values
 */
function parseCSVLine(line) {
  const values = [];
  let currentValue = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      values.push(currentValue.trim());
      currentValue = '';
    } else {
      currentValue += char;
    }
  }
  
  // Add the last value
  values.push(currentValue.trim());
  
  return values;
}

/**
 * Parse Excel file using a simple CSV conversion approach
 * @param {string} filePath - Path to the Excel file
 * @param {Object} stats - File stats
 * @param {string} fileName - File name
 * @returns {Object} Parsed Excel data
 */
function parseExcel(filePath, stats, fileName) {
  try {
    // Create a temporary CSV file
    const tempDir = os.tmpdir();
    const tempFile = path.join(tempDir, `${Date.now()}_temp.csv`);
    
    // Convert Excel to CSV using a Python one-liner
    // This avoids requiring Excel libraries in JavaScript
    const pythonScript = `
import pandas as pd
import sys
try:
    df = pd.read_excel("${filePath.replace(/\\/g, '\\\\')}")
    df.to_csv("${tempFile.replace(/\\/g, '\\\\')}", index=False)
    print("Conversion successful")
except Exception as e:
    print(f"Error: {str(e)}")
    sys.exit(1)
`;
    
    // Write the Python script to a temporary file
    const scriptFile = path.join(tempDir, `${Date.now()}_convert.py`);
    fs.writeFileSync(scriptFile, pythonScript);
    
    // Execute the Python script
    try {
      execSync(`python ${scriptFile}`, { encoding: 'utf8' });
    } catch (error) {
      console.error('Error executing Python script:', error);
      throw new Error('Failed to convert Excel file to CSV');
    }
    
    // Parse the resulting CSV
    const csvResult = parseCSV(tempFile, stats, fileName);
    
    // Clean up temporary files
    try {
      fs.unlinkSync(tempFile);
      fs.unlinkSync(scriptFile);
    } catch (cleanupError) {
      console.warn('Error cleaning up temporary files:', cleanupError);
    }
    
    // Update metadata to reflect Excel format
    csvResult.metadata.fileType = path.extname(fileName).substring(1);
    
    return {
      ...csvResult,
      content: {
        ...csvResult.content,
        sheets: ['Sheet1'],
        data: {
          Sheet1: csvResult.content.rows
        }
      }
    };
  } catch (error) {
    console.error('Error parsing Excel:', error);
    throw error;
  }
}

/**
 * Calculate summary statistics from rows
 * @param {Object[]} rows - Array of data rows
 * @returns {Object} Summary statistics
 */
function calculateSummary(rows) {
  if (!rows || rows.length === 0) {
    return {
      startDate: null,
      endDate: null,
      startBalance: 0,
      endBalance: 0,
      totalCredits: 0,
      totalDebits: 0
    };
  }
  
  // Map common column names
  const dateColumns = ['Financial Date', 'Transaction Date', 'Date', 'EntryDate', 'ValueDate'];
  const debitColumns = ['DR', 'Debit', 'Withdrawal'];
  const creditColumns = ['CR', 'Credit', 'Deposit'];
  const balanceColumns = ['Avail. Bal', 'Balance', 'Available Balance'];
  
  // Find the actual column names in the data
  const dateColumn = findFirstMatchingColumn(rows[0], dateColumns);
  const debitColumn = findFirstMatchingColumn(rows[0], debitColumns);
  const creditColumn = findFirstMatchingColumn(rows[0], creditColumns);
  const balanceColumn = findFirstMatchingColumn(rows[0], balanceColumns);
  
  // Sort by date if possible
  let sortedRows = [...rows];
  if (dateColumn) {
    sortedRows.sort((a, b) => {
      const dateA = new Date(a[dateColumn] || '');
      const dateB = new Date(b[dateColumn] || '');
      return dateA - dateB;
    });
  }
  
  // Calculate summary
  let totalCredits = 0;
  let totalDebits = 0;
  
  sortedRows.forEach(row => {
    if (debitColumn) {
      const debit = parseFloat(row[debitColumn]) || 0;
      totalDebits += debit;
    }
    
    if (creditColumn) {
      const credit = parseFloat(row[creditColumn]) || 0;
      totalCredits += credit;
    }
  });
  
  // Get start and end dates
  const startDate = dateColumn && sortedRows.length > 0 ? sortedRows[0][dateColumn] : null;
  const endDate = dateColumn && sortedRows.length > 0 ? sortedRows[sortedRows.length - 1][dateColumn] : null;
  
  // Get start and end balances
  const startBalance = balanceColumn && sortedRows.length > 0 ? parseFloat(sortedRows[0][balanceColumn]) || 0 : 0;
  const endBalance = balanceColumn && sortedRows.length > 0 ? parseFloat(sortedRows[sortedRows.length - 1][balanceColumn]) || 0 : 0;
  
  return {
    startDate,
    endDate,
    startBalance,
    endBalance,
    totalCredits,
    totalDebits
  };
}

/**
 * Find the first matching column name from a list of possibilities
 * @param {Object} row - Data row
 * @param {string[]} possibleColumns - Possible column names
 * @returns {string|null} Matching column name or null
 */
function findFirstMatchingColumn(row, possibleColumns) {
  for (const column of possibleColumns) {
    if (column in row) {
      return column;
    }
  }
  return null;
}

module.exports = {
  parseFile,
  parseCSV,
  parseExcel
};
