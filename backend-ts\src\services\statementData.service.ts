import mongoose from 'mongoose';
import { parseFile } from '../utils/fileParser';
import StatementData, { IStatementData } from '../models/statementData.model';
import Statement from '../models/statement.model';
import path from 'path';

/**
 * Process and store statement data in MongoDB
 * @param statementId The ID of the statement
 * @param userId The ID of the user
 * @returns The stored statement data
 */
export const processAndStoreStatementData = async (
  statementId: string,
  userId: string
): Promise<IStatementData> => {
  try {
    // Check if data already exists
    const existingData = await StatementData.findOne({ statementId });
    if (existingData && existingData.status === 'completed') {
      console.log(`Statement data already processed for statement ${statementId}`);
      return existingData;
    }

    // Get the statement
    const statement = await Statement.findById(statementId);
    if (!statement) {
      throw new Error(`Statement not found with ID ${statementId}`);
    }

    // Create or update statement data with processing status
    let statementData = existingData;
    if (!statementData) {
      statementData = new StatementData({
        statementId: new mongoose.Types.ObjectId(statementId),
        userId: new mongoose.Types.ObjectId(userId),
        status: 'processing',
        content: {},
        metadata: {
          fileType: path.extname(statement.fileName).toLowerCase().substring(1),
          fileName: statement.fileName,
          fileSize: 0, // Will be updated after parsing
          uploadDate: statement.createdAt,
        },
      });
      await statementData.save();
    } else {
      statementData.status = 'processing';
      await statementData.save();
    }

    // Start processing time
    const startTime = Date.now();

    // Parse the file
    const fileData = await parseFile(statement.filePath);

    // Extract and format the data
    const formattedData = formatStatementData(fileData);

    // Update statement data with the processed content
    statementData.content = formattedData;
    statementData.status = 'completed';
    statementData.metadata = {
      ...statementData.metadata,
      fileSize: fileData.metadata.fileSize || 0,
      processingTime: Date.now() - startTime,
      pageCount: fileData.content.pages || 0,
      rowCount: fileData.summary?.rowCount || 0,
      columnCount: fileData.summary?.columnCount || 0,
    };
    statementData.summary = {
      startDate: formattedData.summary?.startDate || undefined,
      endDate: formattedData.summary?.endDate || undefined,
      startBalance: formattedData.summary?.startBalance || 0,
      endBalance: formattedData.summary?.endBalance || 0,
      totalCredits: formattedData.summary?.totalCredits || 0,
      totalDebits: formattedData.summary?.totalDebits || 0,
      transactionCount: formattedData.content?.length || 0,
    };
    statementData.insights = fileData.insights || {};

    // Save the updated statement data
    await statementData.save();

    // Update the statement with extracted metadata if it was pending processing
    if (statement.bankName === 'Pending Processing') {
      statement.bankName = formattedData.bankName || 'Unknown Bank';
      statement.accountNumber = formattedData.accountNumber || 'Unknown';
      statement.startDate = formattedData.summary?.startDate || statement.startDate;
      statement.endDate = formattedData.summary?.endDate || statement.endDate;
      statement.startBalance = formattedData.summary?.startBalance || 0;
      statement.endBalance = formattedData.summary?.endBalance || 0;
      await statement.save();
    }

    return statementData;
  } catch (error) {
    console.error('Error processing statement data:', error);

    // Update statement data with failed status
    const statementData = await StatementData.findOne({ statementId });
    if (statementData) {
      statementData.status = 'failed';
      await statementData.save();
    }

    throw error;
  }
};

/**
 * Get statement data from MongoDB
 * @param statementId The ID of the statement
 * @param projection Optional projection to specify which fields to include
 * @returns The statement data
 */
export const getStatementData = async (
  statementId: string,
  projection: Record<string, number> = {}
): Promise<IStatementData | null> => {
  try {
    console.log(`Fetching statement data for statement ${statementId} from database`);
    // Use lean() for faster query execution when we don't need a Mongoose document
    return await StatementData.findOne({ statementId }, projection).lean();
  } catch (error) {
    console.error(`Error fetching statement data for statement ${statementId}:`, error);
    return null;
  }
};

/**
 * Format statement data for storage
 * @param fileData The parsed file data
 * @returns Formatted statement data
 */
const formatStatementData = (fileData: any) => {
  // Extract bank name and account number
  const bankName = fileData.content.bankInfo?.bankName || 'Unknown Bank';
  const accountNumber = fileData.content.bankInfo?.accountNumber || 'Unknown';

  // Format content based on file type
  let content: any[] = [];
  let summary: {
    startDate: Date | null;
    endDate: Date | null;
    startBalance: number;
    endBalance: number;
    totalCredits: number;
    totalDebits: number;
  } = {
    startDate: null,
    endDate: null,
    startBalance: 0,
    endBalance: 0,
    totalCredits: 0,
    totalDebits: 0,
  };

  // Handle Excel files
  if (fileData.metadata.fileType === 'xlsx' || fileData.metadata.fileType === 'xls') {
    // For Excel, extract data from the first sheet or the one with transactions
    const sheetNames = fileData.content.sheets || [];
    if (sheetNames.length > 0) {
      const firstSheetName = sheetNames[0];
      const sheetData = fileData.content.data[firstSheetName] || [];

      // Format the data according to the standardized structure
      content = formatExcelData(sheetData);

      // Calculate summary
      summary = calculateSummary(content);
    }
  }
  // Handle CSV files
  else if (fileData.metadata.fileType === 'csv') {
    // For CSV, extract data from rows
    const rows = fileData.content.rows || [];

    // Format the data according to the standardized structure
    content = formatCsvData(rows);

    // Calculate summary
    summary = calculateSummary(content);
  }
  // Handle PDF files
  else if (fileData.metadata.fileType === 'pdf') {
    // For PDF, extract data from tables
    const tables = fileData.content.tables || [];
    if (tables.length > 0) {
      // Format the data according to the standardized structure
      content = formatPdfData(tables);

      // Calculate summary
      summary = calculateSummary(content);
    }
  }

  return {
    bankName,
    accountNumber,
    content,
    summary,
    rawData: fileData.content,
  };
};

/**
 * Format Excel data
 * @param sheetData The Excel sheet data
 * @returns Formatted data
 */
const formatExcelData = (sheetData: any[]) => {
  // Map Excel data to standardized format
  return sheetData.map((row: any) => {
    // Try to map common column headers to standardized format
    const financialDate = row['Financial Date'] || row['Transaction Date'] || row['Date'] || '';
    const transactionDate = row['Transaction Date'] || row['Value Date'] || row['Date'] || '';
    const referenceNo = row['Reference No.'] || row['Reference'] || row['Ref'] || '';
    const instrumentNo = row['Instrument No.'] || row['Cheque No.'] || '';
    const narration = row['Narration'] || row['Description'] || row['Details'] || '';
    const username = row['Username'] || '';
    const debit = parseFloat(row['DR'] || row['Debit'] || row['Withdrawal'] || '0') || 0;
    const credit = parseFloat(row['CR'] || row['Credit'] || row['Deposit'] || '0') || 0;
    const balance = parseFloat(row['Avail. Bal'] || row['Balance'] || '0') || 0;
    const entryCode = row['Entry Code'] || '';

    return {
      'Financial Date': financialDate,
      'Transaction Date': transactionDate,
      'Reference No.': referenceNo,
      'Instrument No.': instrumentNo,
      'Narration': narration,
      'Username': username,
      'DR': debit,
      'CR': credit,
      'Avail. Bal': balance,
      'Entry Code': entryCode,
      // Store original row data for reference
      _original: row
    };
  });
};

/**
 * Format CSV data
 * @param rows The CSV rows
 * @returns Formatted data
 */
const formatCsvData = (rows: any[]) => {
  // Similar to Excel formatting
  return formatExcelData(rows);
};

/**
 * Format PDF data
 * @param tables The PDF tables
 * @returns Formatted data
 */
const formatPdfData = (tables: any[]) => {
  // Extract rows from all tables
  const allRows: any[] = [];
  tables.forEach(table => {
    if (table.rows && Array.isArray(table.rows)) {
      allRows.push(...table.rows);
    }
  });

  // Map PDF data to standardized format
  return allRows.map((row: any) => {
    // Try to map common column headers to standardized format
    const financialDate = row['EntryDate'] || '';
    const transactionDate = row['ValueDate'] || '';
    const narration = row['Details'] || '';
    const debit = parseFloat(row['Debit'] || '0') || 0;
    const credit = parseFloat(row['Credit'] || '0') || 0;
    const balance = parseFloat(row['Balance'] || '0') || 0;

    return {
      'Financial Date': financialDate,
      'Transaction Date': transactionDate,
      'Reference No.': '',
      'Instrument No.': '',
      'Narration': narration,
      'Username': '',
      'DR': debit,
      'CR': credit,
      'Avail. Bal': balance,
      'Entry Code': '',
      // Store original row data for reference
      _original: row
    };
  });
};

/**
 * Calculate summary from formatted data
 * @param content The formatted content
 * @returns Summary data
 */
const calculateSummary = (content: any[]) => {
  if (!content || content.length === 0) {
    return {
      startDate: null,
      endDate: null,
      startBalance: 0,
      endBalance: 0,
      totalCredits: 0,
      totalDebits: 0,
    };
  }

  // Sort by date
  const sortedContent = [...content].sort((a, b) => {
    const dateA = new Date(a['Financial Date'] || a['Transaction Date'] || '');
    const dateB = new Date(b['Financial Date'] || b['Transaction Date'] || '');
    return dateA.getTime() - dateB.getTime();
  });

  // Calculate summary
  const startDate = new Date(sortedContent[0]['Financial Date'] || sortedContent[0]['Transaction Date'] || '');
  const endDate = new Date(sortedContent[sortedContent.length - 1]['Financial Date'] || sortedContent[sortedContent.length - 1]['Transaction Date'] || '');
  const startBalance = parseFloat(sortedContent[0]['Avail. Bal'] || '0') - parseFloat(sortedContent[0]['CR'] || '0') + parseFloat(sortedContent[0]['DR'] || '0');
  const endBalance = parseFloat(sortedContent[sortedContent.length - 1]['Avail. Bal'] || '0');

  // Calculate totals
  let totalCredits = 0;
  let totalDebits = 0;

  sortedContent.forEach(row => {
    totalCredits += parseFloat(row['CR'] || '0') || 0;
    totalDebits += parseFloat(row['DR'] || '0') || 0;
  });

  return {
    startDate: !isNaN(startDate.getTime()) ? startDate : null,
    endDate: !isNaN(endDate.getTime()) ? endDate : null,
    startBalance,
    endBalance,
    totalCredits,
    totalDebits,
  };
};
