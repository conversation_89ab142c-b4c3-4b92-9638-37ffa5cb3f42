"""
StatementData model for the Bank Reconciliation API.
"""

from datetime import datetime
from bson import ObjectId
from pymongo import MongoClient, ASCENDING
from config import config

# Connect to MongoDB
client = MongoClient(config['mongo_uri'])
db = client[config['mongo_database']]
statement_data_collection = db['statementData']

# Create indexes
statement_data_collection.create_index([('statementId', ASCENDING)])
statement_data_collection.create_index([('userId', ASCENDING)])
statement_data_collection.create_index([('status', ASCENDING)])

class StatementData:
    """
    StatementData model class.
    """
    def __init__(self, statement_id, user_id, status='processing', content=None, metadata=None, summary=None, insights=None):
        self.statement_id = ObjectId(statement_id)
        self.user_id = ObjectId(user_id)
        self.status = status
        self.content = content or {}
        self.metadata = metadata or {
            'fileType': '',
            'fileName': '',
            'fileSize': 0,
            'uploadDate': datetime.now()
        }
        self.summary = summary or {}
        self.insights = insights or {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def to_dict(self):
        """
        Convert statement data object to dictionary.
        """
        return {
            'statementId': str(self.statement_id),
            'userId': str(self.user_id),
            'status': self.status,
            'content': self.content,
            'metadata': self.metadata,
            'summary': self.summary,
            'insights': self.insights,
            'createdAt': self.created_at,
            'updatedAt': self.updated_at
        }
    
    @classmethod
    def find_by_statement_id(cls, statement_id):
        """
        Find statement data by statement ID.
        """
        data = statement_data_collection.find_one({'statementId': ObjectId(statement_id)})
        if not data:
            return None
        
        return cls._create_from_data(data)
    
    @classmethod
    def _create_from_data(cls, data):
        """
        Create statement data object from database data.
        """
        statement_data = cls(
            statement_id=data['statementId'],
            user_id=data['userId'],
            status=data['status'],
            content=data.get('content', {}),
            metadata=data.get('metadata', {}),
            summary=data.get('summary', {}),
            insights=data.get('insights', {})
        )
        statement_data._id = data['_id']
        statement_data.created_at = data.get('createdAt', datetime.now())
        statement_data.updated_at = data.get('updatedAt', datetime.now())
        
        return statement_data
    
    def save(self):
        """
        Save statement data to database.
        """
        data = {
            'statementId': self.statement_id,
            'userId': self.user_id,
            'status': self.status,
            'content': self.content,
            'metadata': self.metadata,
            'summary': self.summary,
            'insights': self.insights,
            'updatedAt': datetime.now()
        }
        
        if hasattr(self, '_id'):
            # Update existing statement data
            statement_data_collection.update_one(
                {'_id': self._id},
                {'$set': data}
            )
        else:
            # Create new statement data
            data['createdAt'] = self.created_at
            result = statement_data_collection.insert_one(data)
            self._id = result.inserted_id
        
        return self
