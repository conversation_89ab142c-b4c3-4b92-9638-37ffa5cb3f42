/* @license @mkas3/pdf-table-parser v1.2.18 */
"use strict";function t(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function e(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function r(t){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=t.apply(r,n);function c(t){e(a,o,i,c,u,"next",t)}function u(t){e(a,o,i,c,u,"throw",t)}c(void 0)}))}}function n(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,s(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function o(t,e,r){return(e=s(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function c(){c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function h(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{h({},"")}catch(t){h=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var i=e&&e.prototype instanceof m?e:m,a=Object.create(i.prototype),c=new M(n||[]);return o(a,"_invoke",{value:k(t,r,c)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var v="suspendedStart",d="suspendedYield",p="executing",g="completed",y={};function m(){}function b(){}function w(){}var N={};h(N,a,(function(){return this}));var x=Object.getPrototypeOf,j=x&&x(x(_([])));j&&j!==r&&n.call(j,a)&&(N=j);var O=w.prototype=m.prototype=Object.create(N);function E(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function P(t,e){function r(o,i,a,c){var u=f(t[o],t,i);if("throw"!==u.type){var s=u.arg,h=s.value;return h&&"object"==typeof h&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(h).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function k(e,r,n){var o=v;return function(i,a){if(o===p)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=L(c,n);if(u){if(u===y)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===v)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?g:d,s.arg===y)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=g,n.method="throw",n.arg=s.arg)}}}function L(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,L(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function _(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return b.prototype=w,o(O,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:b,configurable:!0}),b.displayName=h(w,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,h(t,s,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},E(P.prototype),h(P.prototype,u,(function(){return this})),e.AsyncIterator=P,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new P(l(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(O),h(O,s,"Generator"),h(O,a,(function(){return this})),h(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=_,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),A(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;A(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:_(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function u(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(e,r){if(e){if("string"==typeof e)return t(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}Object.defineProperty(exports,"__esModule",{value:!0});var h=function(t){for(var e,r=0,n=String(t);r<n.length&&("&"===(e=n[r])||"<"===e||'"'===e||"\n"===e||"\r"===e||"\t"===e);)r+=1;if(r>=n.length)return n;for(var o=n.substring(0,r);r<n.length;)switch(e=n[r+=1]){case"&":o+="&amp;";break;case"<":o+="&lt;";break;case'"':o+="&quot;";break;case"\n":o+="&#xA;";break;case"\r":o+="&#xD;";break;case"\t":o+="&#x9;";break;default:o+=e}return o};global.btoa=function(t){var e,r,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",o="";for(e=0,r=t.length;e<r;e+=3){var i=255&t.charCodeAt(e),a=255&t.charCodeAt(e+1),c=255&t.charCodeAt(e+2),u=(3&i)<<4|a>>4,s=e+1<r?(15&a)<<2|c>>6:64,h=e+2<r?63&c:64;o+=n.charAt(i>>2)+n.charAt(u)+n.charAt(s)+n.charAt(h)}return o};var l=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),o(this,"nodeName",""),o(this,"childNodes",[]),o(this,"attributes",{}),o(this,"textContent",""),o(this,"sheet",{}),this.nodeName=e,this.childNodes=[],this.attributes={},this.textContent="","style"===e&&(this.sheet={cssRules:[],insertRule:function(t){var e;null===(e=this.cssRules)||void 0===e||e.push(t)}})}return n(t,[{key:"setAttributeNS",value:function(t,e){this.attributes[t]=h(e||"")}},{key:"appendChild",value:function(t){this.childNodes.includes(t)||(this.childNodes.push(t),t.parentNode=this)}},{key:"toString",value:function(){var t=this,e=[];if(Object.values(this.attributes).forEach((function(r){e.push("".concat(r,'="').concat(h(t.attributes[r]),'"'))})),"svg:tspan"===this.nodeName||"svg:style"===this.nodeName){var r=h(this.textContent);return"<".concat(this.nodeName," ").concat(e.join(" "),">").concat(r,"</").concat(this.nodeName,">")}if("svg:svg"===this.nodeName){return"<".concat(this.nodeName," ").concat('xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svg="http://www.w3.org/2000/svg"'," ").concat(e.join(" "),">").concat(this.childNodes.join(""),"</").concat(this.nodeName,">")}return"<".concat(this.nodeName," ").concat(e.join(" "),">").concat(this.childNodes.join(""),"</").concat(this.nodeName,">")}},{key:"cloneNode",value:function(){var e=new t(this.nodeName);return e.childNodes=this.childNodes,e.attributes=this.attributes,e.textContent=this.textContent,e}},{key:"remove",value:function(){var t;return null===(t=this.parentNode)||void 0===t?void 0:t.removeChild(this)}},{key:"removeChild",value:function(t){var e=this.childNodes.indexOf(t);-1!==e&&(this.childNodes=this.childNodes.splice(e,1))}}])}();global.document={childNodes:[],get currentScript(){return{src:""}},get documentElement(){return this},createElementNS:function(t,e){return new l(e)},createElement:function(t){return this.createElementNS("",t)},getElementsByTagName:function(t){return"head"===t?(this.head||(this.head=new l("head")),this.head):[]}};var f={pageTables:[],numPages:0,currentPages:0},v=function(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]},d=function(t,e,r,n,o,i){var c,s,h=[],l=[],d={},p={},g=[1,0,0,1,0,0],y=[],m={},b=[];Object.keys(e).forEach((function(t){b[e[t]]=t}));for(var w,N,x,j,O,E=[],P=i.maxEdgesPerPage||Number.MAX_VALUE;r.fnArray.length;){var k=r.fnArray.shift(),L=r.argsArray.shift();if(e.constructPath===k&&"number"!=typeof L[0]&&"number"!=typeof L[1])for(;L[0].length;){var S=L[0].shift();if(void 0!==S&&S===e.rectangle){var A,M,_,T,C=null!==(A=L[1].shift())&&void 0!==A?A:NaN,F=null!==(M=L[1].shift())&&void 0!==M?M:NaN,G=null!==(_=L[1].shift())&&void 0!==_?_:NaN,D=null!==(T=L[1].shift())&&void 0!==T?T:NaN;if(Math.min(G,D)<2&&(E.push({y:F,x:C,width:G,height:D,transform:g}),E.length>P))return f}else if(S===e.moveTo)w=L[1].shift(),N=L[1].shift();else if(S===e.lineTo){var I,J,R,U,Y,q,B=null!==(I=L[1].shift())&&void 0!==I?I:NaN,V=null!==(J=L[1].shift())&&void 0!==J?J:NaN;if(w===B)E.push({y:Math.min(V,null!==(R=N)&&void 0!==R?R:NaN),x:B-(null!==(U=x)&&void 0!==U?U:NaN)/2,width:null!==(Y=x)&&void 0!==Y?Y:NaN,height:Math.abs(V-(null!==(q=N)&&void 0!==q?q:0)),transform:g});else if(N===V){var W,X,z,H;E.push({x:Math.min(B,null!==(W=w)&&void 0!==W?W:0),y:V-(null!==(X=x)&&void 0!==X?X:NaN)/2,height:null!==(z=x)&&void 0!==z?z:NaN,width:Math.abs(B-(null!==(H=w)&&void 0!==H?H:0)),transform:g})}if(w=B,N=V,E.length>P)return f}}else e.save===k?y.push(g):e.restore===k?g=y.pop():e.transform===k?(O=L,g=[(j=g)[0]*O[0]+j[2]*O[1],j[1]*O[0]+j[3]*O[1],j[0]*O[2]+j[2]*O[3],j[1]*O[2]+j[3]*O[3],j[0]*O[4]+j[2]*O[5]+j[4],j[1]*O[4]+j[3]*O[5]+j[5]]):e.setLineWidth===k?x=L[0]:"eoFill"!==b[k]&&void 0===m[k]&&(m[k]=b[k])}E=E.map((function(t){var e=v([t.x,t.y],t.transform),r=v([t.x+t.width,t.y+t.height],t.transform);return{x:Math.min(e[0],r[0]),y:Math.min(e[1],r[1]),width:Math.abs(e[0]-r[0]),height:Math.abs(e[1]-r[1]),transform:void 0}}));var K=JSON.parse(JSON.stringify(E)).toSorted((function(t,e){return t.x-e.x||t.y-e.y})),Q=JSON.parse(JSON.stringify(E)).toSorted((function(t,e){return t.y-e.y||t.x-e.x}));w=NaN,N=NaN;for(var Z,$,tt=0,et=[],rt=function t(e,r,n){for(var o=!1,i=r,a=n,c=0;c<e.length;c+=1){var s=e[c],h=s.bottom,l=void 0===h?NaN:h,f=s.top,v=void 0===f?NaN:f;if(l>=i&&v<=a)return o=!0,i=Math.min(v,i),a=Math.max(l,a),c>1&&e.slice(0,c-1),Z=u(e.slice(c+1)),t(e=Z,i,a)}return o||e.push({top:i,bottom:a}),e};$&&($=K.shift());)$.width<=2&&((Number.isNaN(w)||$.x-w>2)&&(tt>2&&(et=rt(et,N,N+tt)),!Number.isNaN(w)&&et.length&&h.push({x:w,lines:u(et)}),w=$.x,N=$.y,tt=0,et=[]),Math.abs(N+tt-$.y)<10?tt=$.height+$.y-N:(tt>2&&(et=rt(et,N,N+tt)),N=$.y,tt=$.height));if(console.log("lines1",et),console.log("lines1-oth",tt,2,N,w),tt>2&&(et=rt(et,N,N+tt)),console.log("lines2",et),Number.isNaN(w)||0===et.length)return console.log("exit"),f;h.push({x:w,lines:et}),w=NaN,N=NaN;for(var nt=0,ot=function t(e,r,n){for(var o=!1,i=r,a=n,c=0;c<e.length;c+=1){var s=e[c],h=s.right,l=void 0===h?NaN:h,f=s.left,v=void 0===f?NaN:f;if(l>=i&&v<=a)return o=!0,i=Math.min(v,i),a=Math.max(l,a),c>1&&e.slice(0,c-1),Z=u(e.slice(c+1)),t(e=Z,i,a)}return o||e.push({left:i,right:a}),e};$&&($=Q.shift());)$.height<=2&&((Number.isNaN(N)||$.y-N>2)&&(nt>2&&(et=ot(et,w,w+nt)),!Number.isNaN(N)&&et.length&&l.push({y:N,lines:et}),w=$.x,N=$.y,nt=0,et=[]),Math.abs(w+nt-$.x)<10?nt=$.width+$.x-w:(nt>2&&(et=ot(et,w,w+nt)),w=$.x,nt=$.width));if(nt>2&&(et=ot(et,w,w+nt)),null===N||0===et.length)return f;l.push({y:N,lines:et});for(var it=function(t,e){for(var r=0;r<e.length;r+=1)if(Math.abs(e[r]-t)<5)return r;return-1},at=h.map((function(t){return t.x})),ct=l.map((function(t){return t.y})).toSorted((function(t,e){return e-t})),ut=null!==(c=h.map((function(t){return t.lines[0].bottom})).sort().reverse()[0])&&void 0!==c?c:NaN,st=null!==(s=h.map((function(t){return t.lines[t.lines.length-1].top})).sort()[0])&&void 0!==s?s:NaN,ht=-1===it(st,ct)?1:0,lt=-1===it(ut,ct)?1:0,ft={},vt=0;vt<l.length-2+ht+lt;vt+=1){var dt,pt=it(null!==(dt=(et=l[lt+l.length-vt-2].lines.slice(0))[0].left)&&void 0!==dt?dt:0,at);if(0!==pt)for(var gt=0;gt<pt;gt+=1)ft[[vt,gt].join("-")]={row:vt,col:gt,width:1,height:2};for(var yt=void 0;yt;){var mt,bt;if(!(yt=et.shift()))break;var wt=it(null!==(mt=yt.left)&&void 0!==mt?mt:NaN,at),Nt=it(null!==(bt=yt.right)&&void 0!==bt?bt:NaN,at);if(wt!==pt)for(var xt=pt;xt<wt;xt+=1)ft[[vt,xt].join("-")]={row:vt,col:xt,width:1,height:2};pt=Nt}if(pt!==h.length-1+ht)for(var jt=pt;jt<h.length-1+ht;jt+=1)ft[[vt,jt].join("-")]={row:vt,col:jt,width:1,height:2}}var Ot=!1,Et=function(){var t=Object.values(ft).some((function(e){for(var r="".concat(e.row+e.height-1,"-").concat(e.col+e.width-1);void 0!==ft[r];)e.height+=ft[r].height-1,delete ft[r],Ot=!0,r="".concat(e.row+e.height-1,"-").concat(e.col+e.width-1);return t}));Ot=t};do{Et()}while(!Ot);for(var Pt={},kt=0;kt<h.length-2;kt+=1){var Lt,St=it(null!==(Lt=(et=h[kt+1].lines.slice(0))[0].bottom)&&void 0!==Lt?Lt:NaN,ct)+lt;if(0!==St)for(var At=0;At<St;At+=1)Pt[[At,kt].join("-")]={row:At,col:kt,width:2,height:1};for(var Mt=void 0;Mt;){var _t,Tt;if(!(Mt=et.shift()))break;var Ct=it(null!==(_t=Mt.top)&&void 0!==_t?_t:NaN,ct);-1===Ct?Ct=ct.length+lt:Ct+=lt;var Ft=it(null!==(Tt=Mt.bottom)&&void 0!==Tt?Tt:NaN,ct)+lt;if(Ft!==St)for(var Gt=Ft;Gt<St;Gt+=1)Pt[[Gt,kt].join("-")]={row:Gt,col:kt,width:2,height:1};St=Ct}if(St!==l.length-1+lt+ht)for(var Dt=St;Dt<l.length-1+lt+ht;Dt+=1)Pt[[Dt,kt].join("-")]={row:Dt,col:kt,width:2,height:1}}ht&&l.unshift({y:st,lines:[]}),lt&&l.push({y:ut,lines:[]});var It=function(){var t=Object.values(Pt).some((function(e){for(var r="".concat(e.row+e.height-1,"-").concat(e.col+e.width-1);void 0!==Pt[r];)e.width+=Pt[r].width-1,delete Pt[r],Ot=!0,r="".concat(e.row+e.height-1,"-").concat(e.col+e.width-1);return t}));Ot=t};do{It()}while(!Ot);d=ft,Object.keys(Pt).forEach((function(t){void 0!==d[t]?d[t].width=Pt[t].width:d[t]=Pt[t]})),Object.values(d).forEach((function(t){for(var e=0;e<t.width;e+=1)for(var r=0;r<t.height;r+=1)0===e&&0===r||delete d[[r+t.row,e+t.col].join("-")]})),p={},Object.values(d).forEach((function(t){for(var e=0;e<t.width;e+=1)for(var r=0;r<t.height;r+=1)0===r&&0===e||(p[[t.row+r,t.col+e].join("-")]=[t.row,t.col].join("-"))}));for(var Jt,Rt=[],Ut=[],Yt=0;Yt<l.length-1;Yt+=1){Rt[Yt]=[],Ut[Yt]=[];for(var qt=0;qt<h.length-1;qt+=1)Rt[Yt][qt]="",Ut[Yt][qt]=null}for(;Jt;){var Bt;if(!(Jt=n.items.shift()))break;if("transform"in Jt){for(var Vt=Jt.transform[4],Wt=Jt.transform[5],Xt=-1,zt=0;zt<h.length-1;zt+=1)if(Vt>=h[zt].x&&Vt<h[zt+1].x){Xt=zt;break}if(-1!==Xt){for(var Ht=-1,Kt=0;Kt<l.length-1;Kt+=1)if(Wt>=l[Kt].y&&Wt<l[Kt+1].y){Ht=l.length-Kt-2;break}if(-1!==Ht){var Qt=void 0;void 0!==p["".concat(Ht,"-").concat(Xt)]&&(Ht=+(Qt=p["".concat(Ht,"-").concat(Xt)]).split("-")[0],Xt=+Qt.split("-")[1]),null!==Ut[Ht][Xt]&&Math.abs(+(null!==(Bt=Ut[Ht][Xt])&&void 0!==Bt?Bt:0)-Wt)>5&&(Rt[Ht][Xt]+="\n"),Ut[Ht][Xt]=Wt,Rt[Ht][Xt]+=Jt.str}}}}var Zt=a(a({},o),{},{currentPages:o.currentPages+1});return Rt.length&&Zt.pageTables.push({page:t,tables:Rt,merges:d,merge_alias:p,width:h.length-1,height:l.length-1}),i.progressFunc&&"function"==typeof i.progressFunc&&i.progressFunc(Zt),Zt},p=function(){var t=r(c().mark((function t(e,n,o){var i,u,s,h,l,f,v;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=e.numPages,u={pageTables:[],numPages:i,currentPages:0},t.next=4,Promise.all(Array.from({length:i}).map(function(){var t=r(c().mark((function t(r,n){var o,i,a;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.getPage(n+1);case 2:return o=t.sent,t.next=5,o.getOperatorList();case 5:return i=t.sent,t.next=8,o.getTextContent();case 8:return a=t.sent,t.abrupt("return",{opList:i,content:a});case 10:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}()));case 4:for(s=t.sent,h=1;h<=i;h+=1)l=s[h-1],f=l.opList,v=l.content,u=d(h,n,f,v,a({},u),o);return t.abrupt("return",u);case 7:case"end":return t.stop()}}),t)})));return function(e,r,n){return t.apply(this,arguments)}}(),g=function(){var t=r(c().mark((function t(e){var r,n,o,i,a,u,s=arguments;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=s.length>1&&void 0!==s[1]?s[1]:{},t.next=3,import("pdfjs-dist");case 3:return n=t.sent,o=new Uint8Array(e),i=n.getDocument({data:o,disableFontFace:!1,useSystemFonts:!0}),t.next=8,i.promise;case 8:return a=t.sent,t.next=11,p(a,n.OPS,r);case 11:return u=t.sent,t.next=14,i.destroy();case 14:return t.abrupt("return",u);case 15:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();exports.default=g,exports.extractPdfTable=g;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
