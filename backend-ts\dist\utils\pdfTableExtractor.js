"use strict";
/**
 * Enhanced PDF Table Extractor
 *
 * This module provides advanced table extraction capabilities for PDF files,
 * specifically optimized for bank statements and financial documents.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractTablesFromPDF = extractTablesFromPDF;
const fs_1 = __importDefault(require("fs"));
const util_1 = require("util");
const pdf_parse_1 = __importDefault(require("pdf-parse"));
const pdf_js_extract_1 = require("pdf.js-extract");
const polarisBankParser_1 = require("./polarisBankParser");
// Convert fs functions to Promise-based
const readFileAsync = (0, util_1.promisify)(fs_1.default.readFile);
const statAsync = (0, util_1.promisify)(fs_1.default.stat);
/**
 * Extract tables from PDF using pdf.js-extract for detailed text extraction
 * with coordinates, which helps with complex table structures
 */
async function extractTablesWithPdfJs(filePath) {
    const pdfExtract = new pdf_js_extract_1.PDFExtract();
    const options = {
        normalizeWhitespace: false,
        disableCombineTextItems: false
    };
    try {
        const data = await pdfExtract.extract(filePath, options);
        const text = data.pages.map(page => page.content.map(item => item.str).join(' ')).join('\n');
        // Extract bank statement info
        const bankInfo = extractBankStatementInfo(text);
        // Extract tables using coordinate-based analysis
        const tables = extractTablesFromCoordinates(data.pages);
        return {
            text,
            tables,
            bankInfo,
            metadata: data.meta
        };
    }
    catch (error) {
        console.error('Error extracting tables with pdf.js-extract:', error);
        throw error;
    }
}
/**
 * Extract tables from PDF using pdf-parse (simpler approach)
 */
async function extractTablesWithPdfParse(filePath) {
    try {
        const fileBuffer = await readFileAsync(filePath);
        const pdfData = await (0, pdf_parse_1.default)(fileBuffer);
        const text = pdfData.text;
        // Extract bank statement info
        const bankInfo = extractBankStatementInfo(text);
        // Extract tables using text pattern analysis
        const tables = extractTablesFromText(text);
        return {
            text,
            tables,
            bankInfo,
            metadata: pdfData.info
        };
    }
    catch (error) {
        console.error('Error extracting tables with pdf-parse:', error);
        throw error;
    }
}
/**
 * Extract tables from text content using pattern recognition
 */
function extractTablesFromText(text) {
    const tables = [];
    // First, try to detect bank statement tables specifically
    const bankStatementTable = extractBankStatementTable(text);
    if (bankStatementTable && bankStatementTable.rows.length > 0) {
        tables.push(bankStatementTable);
    }
    // Check if this is a Polaris Bank statement
    const isPolarisBank = text.includes('Polaris Bank Limited') || text.includes('POLARIS BANK');
    if (isPolarisBank) {
        console.log("PDF TABLE EXTRACTOR - Detected Polaris Bank statement");
        // Use our specialized Polaris Bank parser
        const polarisTable = (0, polarisBankParser_1.extractPolarisBankTable)(text);
        if (polarisTable && polarisTable.rows.length > 0 &&
            !tables.some(t => JSON.stringify(t) === JSON.stringify(polarisTable))) {
            console.log("PDF TABLE EXTRACTOR - Successfully extracted Polaris Bank table with", polarisTable.rows.length, "rows");
            tables.push(polarisTable);
        }
        else {
            console.log("PDF TABLE EXTRACTOR - Failed to extract Polaris Bank table or no rows found");
        }
    }
    return tables;
}
/**
 * Extract tables from PDF pages using coordinate-based analysis
 */
function extractTablesFromCoordinates(pages) {
    const tables = [];
    // Process each page
    for (const page of pages) {
        // Group text items by y-coordinate to identify rows
        const rowGroups = groupTextItemsByRow(page.content);
        // If we have enough rows, try to extract a table
        if (rowGroups.length > 3) {
            const potentialTable = extractTableFromRowGroups(rowGroups);
            if (potentialTable && potentialTable.rows.length > 0) {
                tables.push(potentialTable);
            }
        }
    }
    return tables;
}
/**
 * Group text items by their y-coordinate to identify rows
 */
function groupTextItemsByRow(textItems) {
    // Sort items by y-coordinate
    const sortedItems = [...textItems].sort((a, b) => a.y - b.y);
    const rows = [];
    let currentRow = [];
    let currentY = -1;
    // Group items with similar y-coordinates
    for (const item of sortedItems) {
        if (currentY === -1 || Math.abs(item.y - currentY) < 5) {
            // Same row (allowing for small variations)
            currentRow.push(item);
            currentY = item.y;
        }
        else {
            // New row
            if (currentRow.length > 0) {
                rows.push([...currentRow].sort((a, b) => a.x - b.x)); // Sort by x-coordinate
            }
            currentRow = [item];
            currentY = item.y;
        }
    }
    // Add the last row
    if (currentRow.length > 0) {
        rows.push([...currentRow].sort((a, b) => a.x - b.x));
    }
    return rows;
}
/**
 * Extract a table from grouped rows
 */
function extractTableFromRowGroups(rowGroups) {
    var _a;
    if (rowGroups.length < 2)
        return null;
    // Try to identify header row
    let headerRowIndex = 0;
    for (let i = 0; i < Math.min(3, rowGroups.length); i++) {
        const row = rowGroups[i];
        // Check if this row has potential headers
        if (row.length >= 3 && row.some(item => item.str.match(/date|details|description|amount|balance|debit|credit/i))) {
            headerRowIndex = i;
            break;
        }
    }
    // Extract headers
    const headerRow = rowGroups[headerRowIndex];
    const headers = headerRow.map((item) => item.str.trim());
    // Extract data rows
    const rows = [];
    for (let i = headerRowIndex + 1; i < rowGroups.length; i++) {
        const rowItems = rowGroups[i];
        if (rowItems.length === 0)
            continue;
        // Create a row object
        const row = {};
        for (let j = 0; j < Math.min(headers.length, rowItems.length); j++) {
            row[headers[j]] = ((_a = rowItems[j]) === null || _a === void 0 ? void 0 : _a.str) || '';
        }
        // Only add rows that have enough data
        if (Object.keys(row).length >= Math.max(2, Math.floor(headers.length / 2))) {
            rows.push(row);
        }
    }
    return { headers, rows };
}
/**
 * Extract bank statement information from text
 */
function extractBankStatementInfo(text) {
    const lines = text.split('\n').filter(line => line.trim() !== '');
    const result = {
        address: [],
        recipient: []
    };
    // Check if this is a Polaris Bank statement
    const isPolarisBank = text.includes('Polaris Bank Limited') || text.includes('POLARIS BANK');
    if (isPolarisBank) {
        // Use our specialized Polaris Bank info extractor
        return (0, polarisBankParser_1.extractPolarisBankInfo)(text);
    }
    else {
        // Generic bank statement detection
        for (let i = 0; i < Math.min(10, lines.length); i++) {
            if (lines[i].includes('BANK') || lines[i].includes('Bank')) {
                result.bankName = lines[i].trim();
                break;
            }
        }
    }
    return result;
}
// The Polaris Bank extraction function has been moved to polarisBankParser.ts
/**
 * Extract bank statement table (generic approach)
 */
function extractBankStatementTable(text) {
    // Implementation for generic bank statements
    // This is a simplified version that can be expanded based on specific needs
    const lines = text.split('\n').filter(line => line.trim() !== '');
    // Initialize result
    const result = {
        headers: ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'],
        rows: []
    };
    // Look for table headers
    let tableStartIndex = -1;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if ((line.includes('Date') && line.includes('Description') && line.includes('Amount')) ||
            (line.includes('Date') && line.includes('Details') && line.includes('Debit'))) {
            tableStartIndex = i;
            break;
        }
    }
    if (tableStartIndex === -1)
        return null;
    // Extract rows
    for (let i = tableStartIndex + 1; i < lines.length; i++) {
        const line = lines[i].trim();
        // Skip empty lines
        if (!line)
            continue;
        // Check if this looks like a transaction entry (has date and amount)
        if (line.match(/\d{2}[\/\-\.]\d{2}[\/\-\.]\d{2,4}/) &&
            line.match(/\d+\.\d{2}/)) {
            // Split by multiple spaces to get column values
            const values = line.split(/\s{2,}/).filter(v => v.trim() !== '');
            // Only process if we have enough values
            if (values.length >= 3) {
                const entry = {};
                // Map values to headers
                for (let j = 0; j < Math.min(result.headers.length, values.length); j++) {
                    entry[result.headers[j]] = values[j];
                }
                // Add balance if it's the last column and we have more values than headers
                if (values.length > result.headers.length) {
                    entry['Balance'] = values[values.length - 1];
                }
                result.rows.push(entry);
            }
        }
    }
    return result.rows.length > 0 ? result : null;
}
/**
 * Main function to extract tables from PDF
 */
async function extractTablesFromPDF(filePath) {
    console.log("PDF TABLE EXTRACTOR - Starting extraction for file:", filePath);
    try {
        // First try with pdf.js-extract for more detailed extraction
        console.log("PDF TABLE EXTRACTOR - Attempting extraction with pdf.js-extract");
        const result = await extractTablesWithPdfJs(filePath);
        console.log("PDF TABLE EXTRACTOR - Successfully extracted with pdf.js-extract");
        console.log("PDF TABLE EXTRACTOR - Tables found:", result.tables.length);
        return result;
    }
    catch (error) {
        console.warn('Failed to extract with pdf.js-extract, falling back to pdf-parse:', error);
        // Fall back to simpler extraction
        console.log("PDF TABLE EXTRACTOR - Attempting extraction with pdf-parse");
        const result = await extractTablesWithPdfParse(filePath);
        console.log("PDF TABLE EXTRACTOR - Successfully extracted with pdf-parse");
        console.log("PDF TABLE EXTRACTOR - Tables found:", result.tables.length);
        return result;
    }
}
exports.default = {
    extractTablesFromPDF
};
