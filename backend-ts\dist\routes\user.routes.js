"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const user_controller_1 = require("../controllers/user.controller");
const router = express_1.default.Router();
// @route   GET /api/users/me
// @desc    Get current user
// @access  Private
router.get('/me', user_controller_1.getCurrentUser);
// @route   PUT /api/users/me
// @desc    Update user
// @access  Private
router.put('/me', user_controller_1.updateUser);
// @route   DELETE /api/users/me
// @desc    Delete user
// @access  Private
router.delete('/me', user_controller_1.deleteUser);
exports.default = router;
