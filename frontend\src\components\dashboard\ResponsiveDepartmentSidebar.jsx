import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const ResponsiveDepartmentSidebar = ({ menuItems, department, departmentColor = '#412D6C' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState({});
  const location = useLocation();

  // Initialize expanded state based on current route
  useEffect(() => {
    const newExpandedState = {};
    menuItems.forEach((item, index) => {
      if (item.subItems && item.subItems.some(subItem =>
        location.pathname === subItem.path || location.pathname.startsWith(subItem.path + '/')
      )) {
        newExpandedState[index] = true;
      }
    });
    setExpandedItems(newExpandedState);
  }, [location, menuItems]);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event) => {
      const sidebar = document.getElementById('department-sidebar');
      const hamburger = document.getElementById('hamburger-button');

      if (isOpen && sidebar && hamburger &&
          !sidebar.contains(event.target) &&
          !hamburger.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // Close sidebar on route change for mobile
  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  return (
    <>
      {/* Mobile Hamburger - Positioned below navbar */}
      <div
        id="hamburger-button"
        onClick={(e) => {
          e.preventDefault(); // Prevent default behavior
          e.stopPropagation(); // Stop event propagation
          setIsOpen(!isOpen);
          return false; // Ensure no default action
        }}
        onMouseDown={(e) => e.preventDefault()} // Prevent any mousedown default behavior
        className={`
          lg:hidden fixed top-20 left-4 z-40
          p-2 rounded-lg transition-all duration-300 cursor-pointer
          ${isOpen
            ? 'bg-white text-gray-800'
            : 'bg-white/30 backdrop-blur-xl border border-white/20'
          }
        `}
      >
        <div className="w-6 h-6 relative">
          <span className={`
            absolute left-0 block w-full h-0.5 transform transition-all duration-300
            ${isOpen
              ? 'top-3 rotate-45 bg-gray-800'
              : 'top-1 bg-gray-600'
            }
          `}></span>
          <span className={`
            absolute left-0 block w-full h-0.5 bg-gray-600 top-3
            transition-all duration-300
            ${isOpen ? 'opacity-0' : 'opacity-100'}
          `}></span>
          <span className={`
            absolute left-0 block w-full h-0.5 transform transition-all duration-300
            ${isOpen
              ? 'top-3 -rotate-45 bg-gray-800'
              : 'top-5 bg-gray-600'
            }
          `}></span>
        </div>
      </div>

      {/* Overlay - Positioned below navbar */}
      <div
        className={`
          fixed inset-0 top-16 bg-black/50 lg:hidden transition-opacity duration-300
          ${isOpen ? 'opacity-100 z-30' : 'opacity-0 pointer-events-none'}
        `}
        onClick={(e) => {
          e.preventDefault(); // Prevent default behavior
          e.stopPropagation(); // Stop event propagation
          setIsOpen(false);
          return false; // Ensure no default action
        }}
        onMouseDown={(e) => e.preventDefault()} // Prevent any mousedown default behavior
      />

      {/* Sidebar - Positioned below navbar */}
      <div
        id="department-sidebar"
        className={`
          fixed top-16 left-0 h-[calc(100vh-4rem)] w-[280px] lg:w-60
          z-40 lg:z-30 overflow-hidden
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          bg-white/95 backdrop-blur-xl border-r border-white/20
        `}
      >
        {/* Scrollable container */}
        <div className="h-full overflow-y-auto px-4 py-6">
          <div className="mb-8">
            <h2 className="text-xl font-bold" style={{ color: departmentColor }}>
              {department}
            </h2>
            <p className="text-sm text-gray-600">Dashboard</p>
          </div>

          <nav className="space-y-2">
            {menuItems.map((item, index) => (
              <div key={index} className="space-y-1">
                {/* Main menu item */}
                {item.subItems ? (
                  <div
                    className={`
                      flex items-center space-x-3 px-4 py-3 rounded-lg
                      transition-all duration-200 cursor-pointer
                      ${location.pathname === item.path
                        ? 'bg-gray-100 text-gray-900'
                        : 'hover:bg-white/50 text-gray-700'
                      }
                    `}
                    onClick={(e) => {
                      e.preventDefault(); // Prevent default behavior
                      e.stopPropagation(); // Stop event propagation
                      setExpandedItems(prev => ({
                        ...prev,
                        [index]: !prev[index]
                      }));
                    }}
                  >
                    <span className="text-xl">{item.icon}</span>
                    <span className="flex-1">{item.label}</span>
                    {item.badge && (
                      <span
                        className="px-2 py-1 text-xs rounded-full text-white"
                        style={{ backgroundColor: departmentColor }}
                      >
                        {item.badge}
                      </span>
                    )}
                    <span className="text-gray-500">
                      {expandedItems[index] ? '▼' : '▶'}
                    </span>
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    className={`
                      flex items-center space-x-3 px-4 py-3 rounded-lg
                      transition-all duration-200 cursor-pointer
                      ${location.pathname === item.path
                        ? 'bg-gray-100 text-gray-900'
                        : 'hover:bg-white/50 text-gray-700'
                      }
                    `}
                    onClick={(e) => {
                      // On mobile, close the sidebar after navigation
                      if (window.innerWidth < 1024) { // lg breakpoint
                        setIsOpen(false);
                      }
                    }}
                  >
                    <span className="text-xl">{item.icon}</span>
                    <span className="flex-1">{item.label}</span>
                    {item.badge && (
                      <span
                        className="px-2 py-1 text-xs rounded-full text-white"
                        style={{ backgroundColor: departmentColor }}
                      >
                        {item.badge}
                      </span>
                    )}
                  </Link>
                )}

                {/* Sub-items */}
                {item.subItems && expandedItems[index] && (
                  <div className="ml-8 pl-2 border-l-2 border-gray-200 space-y-1">
                    {item.subItems.map((subItem, subIndex) => (
                      <Link
                        key={subIndex}
                        to={subItem.path}
                        className={`
                          flex items-center space-x-2 px-3 py-2 rounded-lg
                          transition-all duration-200 text-sm
                          ${location.pathname === subItem.path
                            ? 'bg-gray-100 text-gray-900 font-medium'
                            : 'hover:bg-white/50 text-gray-600'
                          }
                        `}
                        onClick={(e) => {
                          // On mobile, close the sidebar after navigation
                          if (window.innerWidth < 1024) { // lg breakpoint
                            setIsOpen(false);
                          }
                        }}
                      >
                        <span className="w-1.5 h-1.5 rounded-full bg-gray-400"></span>
                        <span>{subItem.label}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>

          <div className="mt-6">
            <div className="bg-white/50 rounded-lg p-4">
              <p className="text-sm text-gray-600">Need help?</p>
              <a
                href="/support"
                className="text-sm font-medium hover:underline"
                style={{ color: departmentColor }}
              >
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ResponsiveDepartmentSidebar;