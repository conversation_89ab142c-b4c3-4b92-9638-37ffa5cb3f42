"""
Configuration module for the Bank Reconciliation API.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

config = {
    'port': int(os.environ.get('PORT', 8080)),
    'node_env': os.environ.get('NODE_ENV', 'development'),
    'mongo_uri': os.environ.get('MONGODB_URI', 'mongodb://localhost:27017/bank_reconciliation'),
    'mongo_database': os.environ.get('MONGODB_DATABASE', 'bank_reconciliation'),
    'jwt_secret': os.environ.get('JWT_SECRET', 'your_jwt_secret_key'),
    'jwt_expiration': int(os.environ.get('JWT_EXPIRATION', 86400)),  # 24 hours in seconds
}
