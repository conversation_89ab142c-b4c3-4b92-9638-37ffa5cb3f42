{"name": "pdf-table-extractor", "version": "1.0.0", "description": "A standalone script to extract tables from PDF files", "main": "extract-pdf-tables.js", "scripts": {"start": "node extract-pdf-tables.js"}, "keywords": ["pdf", "table", "parser", "extractor"], "license": "MIT", "dependencies": {"@mkas3/pdf-table-parser": "^1.2.18", "form-data": "^4.0.2", "pdf-table-extractor": "file:"}, "engines": {"node": ">=14.0.0"}, "bin": {"extract-pdf-tables": "./extract-pdf-tables.js"}}