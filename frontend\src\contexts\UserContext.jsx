import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { createAuthenticatedAxiosInstance } from '../services/api';
import { USER_ROUTES } from '../apiRoutes';
import { shouldMakeApiCalls } from '../utils/authUtils';

// Create the context
const UserContext = createContext();

// Create a provider component
export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to fetch user data from token
  const fetchUserData = async () => {
    // Don't make API calls if we're on the login page
    if (!shouldMakeApiCalls()) {
      console.log('On login page, skipping user data fetch');
      setLoading(false);
      return;
    }

    const token = localStorage.getItem('authToken');

    if (!token) {
      setLoading(false);
      return;
    }

    try {
      // Get user ID from token (assuming it's stored in localStorage)
      const userId = localStorage.getItem('userId');

      if (!userId) {
        setLoading(false);
        return;
      }

      // Fetch user data from API using authenticated axios instance
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(USER_ROUTES.GET_USER_ME);

      if (response.data.status) {
        const userData = {
          id: userId,
          ...response.data.data
        };

        console.log('User data fetched successfully:', userData);
        console.log('User regMode:', userData.regMode);

        // Update context state
        setUser(userData);

        // The store will be updated in the App component using the useEffect hook
      } else {
        // If API returns error status
        setError(response.data.message || 'Failed to fetch user data');
        // Clear invalid token
        localStorage.removeItem('authToken');
        localStorage.removeItem('userId');
      }
    } catch (err) {
      console.error('Error fetching user data:', err);
      setError(err.message || 'An error occurred while fetching user data');

      // If token is invalid or expired
      if (err.response?.status === 401) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userId');
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to update user data in context
  const updateUser = (userData) => {
    // Update user state in context
    const updatedUser = {
      ...user,
      ...userData
    };

    console.log('UpdateUser: Updating user data:', updatedUser);

    // Update context
    setUser(updatedUser);

    // The store will be updated in the App component using the useEffect hook
  };

  // Function to handle user login
  const login = (userData, token) => {
    // Save token and user ID to localStorage
    localStorage.setItem('authToken', token);
    localStorage.setItem('userId', userData.id);

    console.log('Login: Setting user data:', userData);

    // Update user state in context
    setUser(userData);

    // The store will be updated in the App component using the useEffect hook
  };

  // Function to handle user logout
  const logout = () => {
    // Clear localStorage of all auth-related items
    localStorage.removeItem('authToken');
    localStorage.removeItem('userId');
    localStorage.removeItem('userRole');
    localStorage.removeItem('userEmail');

    // Clear any other potential auth data
    sessionStorage.removeItem('authToken');

    console.log('Logout: Clearing user data');

    // Reset user state in context
    setUser(null);
    setError(null);

    // The store will be updated in the App component using the useEffect hook
  };

  // Initialize user from localStorage if available
  useEffect(() => {
    // Don't make API calls if we're on the login page
    if (!shouldMakeApiCalls()) {
      console.log('On login page, skipping user initialization');
      setLoading(false);
      return;
    }

    // First try to initialize from localStorage
    const userId = localStorage.getItem('userId');
    const token = localStorage.getItem('authToken');

    if (userId && token) {
      // Set initial user state from localStorage
      const initialUser = {
        id: userId,
        token: token
      };

      console.log('Initial user data from localStorage:', initialUser);

      // Update context
      setUser(initialUser);

      // Then fetch complete user data
      fetchUserData();
    } else {
      console.log('No user data in localStorage');
      setLoading(false);
    }
  }, []);

  // Create the context value
  const contextValue = {
    user,
    loading,
    error,
    updateUser,
    login,
    logout,
    fetchUserData
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default UserContext;
