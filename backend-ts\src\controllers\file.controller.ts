import { Request, Response } from 'express';
import { parseFile } from '../utils/fileParser';
import path from 'path';
import fs from 'fs';
import multer from 'multer';
import os from 'os';

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'uploads');
try {
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('Created uploads directory:', uploadsDir);
  }
} catch (err) {
  console.error('Error creating uploads directory:', err);
}

// Configure multer for file uploads - use OS temp directory as fallback
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Try to use our uploads directory first
    if (fs.existsSync(uploadsDir) && fs.accessSync(uploadsDir, fs.constants.W_OK)) {
      cb(null, uploadsDir);
    } else {
      // Fallback to OS temp directory
      const tempDir = os.tmpdir();
      console.log('Using OS temp directory for uploads:', tempDir);
      cb(null, tempDir);
    }
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    const allowedExtensions = ['.pdf', '.csv', '.xlsx', '.xls'];
    const ext = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF, CSV, and Excel files are allowed.'));
    }
  }
}).single('file');

/**
 * Upload file
 * @route POST /api/files/upload
 */
export const uploadFile = async (req: Request, res: Response): Promise<void> => {
  // Use multer middleware to handle file upload
  upload(req, res, async (err) => {
    if (err) {
      res.status(400).json({ message: err.message });
      return;
    }

    try {
      if (!req.file) {
        res.status(400).json({ message: 'No file uploaded' });
        return;
      }

      const userId = req.user?.sub;

      // Return file information
      res.status(200).json({
        status: true,
        message: 'File uploaded successfully',
        data: {
          fileName: req.file.filename,
          originalName: req.file.originalname,
          filePath: req.file.path,
          fileSize: req.file.size,
          fileType: path.extname(req.file.originalname).toLowerCase(),
          userId
        }
      });
    } catch (error: any) {
      console.error('Error uploading file:', error);
      res.status(500).json({ message: error.message || 'Server error' });
    }
  });
};

/**
 * Preview file content
 * @route GET /api/files/preview/:filePath
 */
export const previewFile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { filePath } = req.params;

    // Decode the file path (it will be URL encoded)
    const decodedPath = decodeURIComponent(filePath);

    // Security check: Make sure the file is in the uploads directory
    const fullPath = path.resolve(decodedPath);
    if (!fullPath.startsWith(uploadsDir) && !fullPath.startsWith(os.tmpdir())) {
      res.status(403).json({ message: 'Access denied: Invalid file path' });
      return;
    }

    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      res.status(404).json({ message: 'File not found' });
      return;
    }

    // Get query parameters for customization
    const options = {
      maxRows: req.query.maxRows ? parseInt(req.query.maxRows as string) : undefined,
      includeStats: req.query.stats === 'true',
      sheet: req.query.sheet as string,
      page: req.query.page ? parseInt(req.query.page as string) : 1
    };

    // Parse file based on type
    const fileData = await parseFile(fullPath);

    // Apply customizations based on options
    if (fileData.content && options.maxRows) {
      // Limit rows for CSV and Excel
      if (fileData.content.rows && Array.isArray(fileData.content.rows)) {
        fileData.content.rows = fileData.content.rows.slice(0, options.maxRows);
      }

      // For Excel with multiple sheets
      if (fileData.content.data) {
        Object.keys(fileData.content.data).forEach(sheetName => {
          if (Array.isArray(fileData.content.data[sheetName])) {
            fileData.content.data[sheetName] = fileData.content.data[sheetName].slice(0, options.maxRows);
          }
        });
      }
    }

    // Filter by specific sheet for Excel
    if (fileData.content && fileData.content.data && options.sheet) {
      if (fileData.content.sheets && fileData.content.sheets.includes(options.sheet)) {
        // Keep only the requested sheet
        const sheetData = fileData.content.data[options.sheet];
        fileData.content.data = { [options.sheet]: sheetData };
        fileData.content.currentSheet = options.sheet;
      }
    }

    // Remove statistics if not requested (to reduce payload size)
    if (!options.includeStats && fileData.summary) {
      delete fileData.summary.statistics;
      delete fileData.summary.dataTypes;
    }

    // Special handling for Polaris Bank statements
    if (fileData.content && fileData.content.text &&
        (fileData.content.text.includes('Polaris Bank Limited') || fileData.content.text.includes('POLARIS BANK'))) {

      console.log("FILE CONTROLLER - Detected Polaris Bank statement");

      // Clean up the tables data structure for better JSON output
      if (fileData.content.tables && Array.isArray(fileData.content.tables)) {
        console.log("FILE CONTROLLER - Processing tables, count:", fileData.content.tables.length);

        // Format the tables properly
        fileData.content.tables = fileData.content.tables.map((table: any) => {
          // Make sure we have a clean structure
          return {
            headers: table.headers,
            rows: table.rows.map((row: any) => {
              // Create a clean row object with all fields
              const cleanRow: Record<string, string> = {};
              for (const header of table.headers) {
                cleanRow[header] = row[header] || '';
              }
              return cleanRow;
            })
          };
        });
      }

      // Add a formatted transactions array for easier consumption
      if (fileData.content.tables && fileData.content.tables.length > 0) {
        const mainTable = fileData.content.tables[0];
        console.log("FILE CONTROLLER - Creating transactions array from table with", mainTable.rows.length, "rows");

        fileData.content.transactions = mainTable.rows.map((row: any) => {
          // Create a clean transaction object
          return {
            entryDate: row.EntryDate || '',
            details: row.Details || '',
            valueDate: row.ValueDate || '',
            debit: row.Debit || '',
            credit: row.Credit || '',
            balance: row.Balance || ''
          };
        });

        console.log("FILE CONTROLLER - Created transactions array with", fileData.content.transactions.length, "transactions");
        console.log("FILE CONTROLLER - First transaction sample:",
          JSON.stringify(fileData.content.transactions[0], null, 2));
      } else {
        console.log("FILE CONTROLLER - No tables found for Polaris Bank statement");
      }
    }

    res.status(200).json({
      status: true,
      data: fileData
    });
  } catch (error: any) {
    console.error('Error previewing file:', error);
    res.status(500).json({ message: error.message || 'Server error' });
  }
};

/**
 * Download file
 * @route GET /api/files/download/:filePath
 */
export const downloadFile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { filePath } = req.params;

    // Decode the file path (it will be URL encoded)
    const decodedPath = decodeURIComponent(filePath);

    // Security check: Make sure the file is in the uploads directory
    const fullPath = path.resolve(decodedPath);
    if (!fullPath.startsWith(uploadsDir) && !fullPath.startsWith(os.tmpdir())) {
      res.status(403).json({ message: 'Access denied: Invalid file path' });
      return;
    }

    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      res.status(404).json({ message: 'File not found' });
      return;
    }

    // Get file name
    const fileName = path.basename(fullPath);

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // Stream the file
    const fileStream = fs.createReadStream(fullPath);
    fileStream.pipe(res);
  } catch (error: any) {
    console.error('Error downloading file:', error);
    res.status(500).json({ message: error.message || 'Server error' });
  }
};

/**
 * Analyze file content and extract insights
 * @route POST /api/files/analyze
 */
export const analyzeFile = async (req: Request, res: Response): Promise<void> => {
  // Use multer middleware to handle file upload
  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const filePath = req.file.path;

      // Parse the file to get content and metadata
      const fileData = await parseFile(filePath);

      // Extract insights based on file type
      const insights = await extractInsights(fileData);

      res.status(200).json({
        status: true,
        message: 'File analyzed successfully',
        data: {
          fileInfo: {
            fileName: req.file.originalname,
            fileSize: req.file.size,
            fileType: path.extname(req.file.originalname).toLowerCase(),
          },
          insights,
          summary: fileData.summary
        }
      });
    } catch (error: any) {
      console.error('Error analyzing file:', error);
      res.status(500).json({ message: error.message || 'Server error' });
    }
  });
};

/**
 * Extract insights from file data
 */
async function extractInsights(fileData: any): Promise<any> {
  const insights: any = {
    suggestions: [],
    anomalies: [],
    patterns: [],
    summary: {}
  };

  try {
    const fileType = fileData.metadata.fileType;

    // Process based on file type
    if (fileType === 'pdf') {
      // PDF insights
      const text = fileData.content.text || '';

      // Check for tables
      if (fileData.content.tables && fileData.content.tables.length > 0) {
        insights.summary.tableCount = fileData.content.tables.length;
        insights.suggestions.push('PDF contains tabular data that could be extracted for analysis');
      }

      // Look for dates
      const dateMatches = text.match(/\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}/g) || [];
      if (dateMatches.length > 0) {
        insights.patterns.push({
          type: 'dates',
          count: dateMatches.length,
          samples: dateMatches.slice(0, 5)
        });
      }

      // Look for monetary values
      const moneyMatches = text.match(/[\$\€\£\¥]\s?\d+(?:[.,]\d+)*/g) || [];
      if (moneyMatches.length > 0) {
        insights.patterns.push({
          type: 'monetary',
          count: moneyMatches.length,
          samples: moneyMatches.slice(0, 5)
        });

        insights.suggestions.push('Document contains financial data that could be extracted for analysis');
      }

    } else if (fileType === 'csv') {
      // CSV insights
      const headers = fileData.content.headers || [];
      const rows = fileData.content.rows || [];

      insights.summary.columnCount = headers.length;
      insights.summary.rowCount = rows.length;

      // Check for empty columns
      const emptyColumns = headers.filter((header: string) =>
        rows.every((row: Record<string, any>) => !row[header] || row[header].toString().trim() === '')
      );

      if (emptyColumns.length > 0) {
        insights.anomalies.push({
          type: 'emptyColumns',
          columns: emptyColumns,
          message: `Found ${emptyColumns.length} empty columns that could be removed`
        });
      }

      // Check for duplicate rows
      const uniqueRows = new Set(rows.map((row: Record<string, any>) => JSON.stringify(row)));
      const duplicateCount = rows.length - uniqueRows.size;

      if (duplicateCount > 0) {
        insights.anomalies.push({
          type: 'duplicateRows',
          count: duplicateCount,
          message: `Found ${duplicateCount} duplicate rows`
        });
      }

      // Analyze data types
      if (fileData.summary && fileData.summary.dataTypes) {
        const dataTypes = fileData.summary.dataTypes;

        // Check for mixed data types
        const mixedColumns = Object.entries(dataTypes)
          .filter(([_, type]) => typeof type === 'string' && type.includes('/'))
          .map(([column]) => column);

        if (mixedColumns.length > 0) {
          insights.anomalies.push({
            type: 'mixedDataTypes',
            columns: mixedColumns,
            message: `Found ${mixedColumns.length} columns with mixed data types`
          });
        }

        // Check for date columns
        const dateColumns = Object.entries(dataTypes)
          .filter(([_, type]) => typeof type === 'string' && type.includes('date'))
          .map(([column]) => column);

        if (dateColumns.length > 0) {
          insights.suggestions.push(`Found ${dateColumns.length} date columns that could be used for time-series analysis`);
        }

        // Check for numeric columns
        const numericColumns = Object.entries(dataTypes)
          .filter(([_, type]) => typeof type === 'string' && type === 'number')
          .map(([column]) => column);

        if (numericColumns.length > 0) {
          insights.suggestions.push(`Found ${numericColumns.length} numeric columns that could be used for statistical analysis`);
        }
      }

    } else if (fileType === 'xlsx' || fileType === 'xls') {
      // Excel insights
      const sheetNames = fileData.content.sheets || [];
      insights.summary.sheetCount = sheetNames.length;

      if (sheetNames.length > 1) {
        insights.suggestions.push(`Workbook contains ${sheetNames.length} sheets that could be analyzed separately or combined`);
      }

      // Analyze each sheet
      const sheetInsights = [];

      for (const sheetName of sheetNames) {
        const sheetData = fileData.content.data[sheetName] || [];

        if (sheetData.length === 0) {
          sheetInsights.push({
            sheet: sheetName,
            isEmpty: true
          });
          continue;
        }

        const headers = Object.keys(sheetData[0]);

        // Check for formulas (approximation)
        const potentialFormulaColumns = headers.filter((header: string) =>
          sheetData.some((row: Record<string, any>) =>
            typeof row[header] === 'string' &&
            row[header].toString().startsWith('=')
          )
        );

        if (potentialFormulaColumns.length > 0) {
          sheetInsights.push({
            sheet: sheetName,
            hasFormulas: true,
            formulaColumns: potentialFormulaColumns
          });

          insights.suggestions.push(`Sheet "${sheetName}" may contain Excel formulas that should be preserved during processing`);
        }
      }

      insights.sheetInsights = sheetInsights;
    }

    return insights;
  } catch (error) {
    console.error('Error extracting insights:', error);
    return {
      error: 'Failed to extract insights',
      suggestions: ['Basic file preview is available, but detailed analysis failed']
    };
  }
}
