"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.login = exports.register = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const user_model_1 = __importDefault(require("../models/user.model"));
const config_1 = __importDefault(require("../config"));
/**
 * Generate JWT token
 */
const generateToken = (user) => {
    return jsonwebtoken_1.default.sign({ sub: user._id, email: user.email, role: user.role }, config_1.default.jwtSecret, { expiresIn: config_1.default.jwtExpiration });
};
/**
 * Register a new user
 * @route POST /api/auth/register
 */
const register = async (req, res) => {
    try {
        const { email, password, firstName, lastName } = req.body;
        // Validate required fields
        if (!email || !password || !firstName || !lastName) {
            res.status(400).json({
                message: 'All fields are required',
                errors: {
                    email: !email ? 'Email is required' : null,
                    password: !password ? 'Password is required' : null,
                    firstName: !firstName ? 'First name is required' : null,
                    lastName: !lastName ? 'Last name is required' : null
                }
            });
            return;
        }
        console.log('Registration attempt with data:', { email, firstName, lastName });
        // Check if user already exists
        const existingUser = await user_model_1.default.findOne({ email });
        if (existingUser) {
            res.status(400).json({ message: 'User already exists' });
            return;
        }
        // Create new user
        const user = new user_model_1.default({
            email,
            password,
            firstName,
            lastName,
        });
        // Save user to database
        try {
            await user.save();
        }
        catch (validationError) {
            // Handle validation errors
            if (validationError.name === 'ValidationError') {
                const errors = {};
                // Extract validation error messages
                for (const field in validationError.errors) {
                    errors[field] = validationError.errors[field].message;
                }
                res.status(400).json({
                    message: 'Validation failed',
                    errors
                });
                return;
            }
            // Re-throw other errors to be caught by the outer catch block
            throw validationError;
        }
        // Generate JWT token
        const token = generateToken(user);
        // Return user data and token
        res.status(201).json({
            status: true,
            message: 'User registered successfully',
            data: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
            },
            token,
        });
    }
    catch (error) {
        console.error('Error registering user:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.register = register;
/**
 * Login user
 * @route POST /api/auth/login
 */
const login = async (req, res) => {
    try {
        const { email, password } = req.body;
        // Validate input
        if (!email || !password) {
            res.status(400).json({ message: 'Email and password are required' });
            return;
        }
        console.log(`Login attempt for email: ${email}`);
        // Check if user exists
        const user = await user_model_1.default.findOne({ email });
        if (!user) {
            console.log(`User not found for email: ${email}`);
            res.status(401).json({ message: 'Invalid credentials' });
            return;
        }
        console.log(`User found for email: ${email}, checking password`);
        // Check if password is correct
        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            console.log(`Invalid password for email: ${email}`);
            res.status(401).json({ message: 'Invalid credentials' });
            return;
        }
        console.log(`Password match successful for email: ${email}`);
        // Generate JWT token
        const token = generateToken(user);
        // Return user data and token
        res.status(200).json({
            status: true,
            message: 'Login successful',
            data: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
            },
            token,
        });
    }
    catch (error) {
        console.error('Error logging in:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.login = login;
