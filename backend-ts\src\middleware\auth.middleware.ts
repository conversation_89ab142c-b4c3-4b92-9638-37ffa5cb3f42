import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import config from '../config';

// Extend Express Request interface to include user property
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

/**
 * Authentication middleware
 * Verifies JWT token and adds user to request object
 */
export const auth = (req: Request, res: Response, next: NextFunction): void => {
  // Get token from header
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  // Check if no token
  if (!token) {
    res.status(401).json({ message: 'No token, authorization denied' });
    return;
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, config.jwtSecret);

    // Add user from payload
    req.user = decoded;
    next();
  } catch (err) {
    res.status(401).json({ message: 'Token is not valid' });
  }
};

/**
 * List of public routes that don't require authentication
 */
export const publicRoutes = [
  '/api/health',
  '/api/auth/login',
  '/api/auth/register'
];

/**
 * Conditional authentication middleware
 * Only applies auth middleware to protected routes
 */
export const conditionalAuth = (req: Request, res: Response, next: NextFunction): void => {
  // Check if the route is public
  const isPublicRoute = publicRoutes.some(route => req.path.startsWith(route));

  if (isPublicRoute) {
    // Skip authentication for public routes
    return next();
  }

  // Apply authentication for protected routes
  auth(req, res, next);
};
