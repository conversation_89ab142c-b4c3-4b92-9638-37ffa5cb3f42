"use strict";
/**
 * <PERSON><PERSON><PERSON> to extract tables from a PDF file and log the result as JSON
 *
 * Usage:
 * ts-node src/scripts/extract-pdf.ts <path-to-pdf-file>
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const pdfTableExtractor_js_1 = require("../utils/pdfTableExtractor.js");
const polarisBankParser_js_1 = require("../utils/polarisBankParser.js");
// Get the PDF file path from command line arguments
const pdfFilePath = process.argv[2];
if (!pdfFilePath) {
    console.error('Please provide a PDF file path');
    console.error('Usage: ts-node src/scripts/extract-pdf.ts <path-to-pdf-file>');
    process.exit(1);
}
// Check if the file exists
if (!fs_1.default.existsSync(pdfFilePath)) {
    console.error(`File not found: ${pdfFilePath}`);
    process.exit(1);
}
// Function to clean up the data for JSON output
function cleanForJson(data) {
    if (Array.isArray(data)) {
        return data.map(item => cleanForJson(item));
    }
    else if (data && typeof data === 'object') {
        const result = {};
        for (const key of Object.keys(data)) {
            // Skip functions and circular references
            if (typeof data[key] !== 'function') {
                try {
                    // Test if it can be serialized
                    JSON.stringify(data[key]);
                    result[key] = cleanForJson(data[key]);
                }
                catch (e) {
                    result[key] = '[Circular or non-serializable]';
                }
            }
        }
        return result;
    }
    return data;
}
async function main() {
    var _a, _b;
    console.log(`PDF TABLE EXTRACTOR - Starting extraction for file: ${pdfFilePath}`);
    try {
        // Extract tables from the PDF
        const extractedData = await (0, pdfTableExtractor_js_1.extractTablesFromPDF)(pdfFilePath);
        console.log(`PDF TABLE EXTRACTOR - Extraction complete`);
        console.log(`PDF TABLE EXTRACTOR - Text length: ${((_a = extractedData.text) === null || _a === void 0 ? void 0 : _a.length) || 0} characters`);
        console.log(`PDF TABLE EXTRACTOR - Tables found: ${((_b = extractedData.tables) === null || _b === void 0 ? void 0 : _b.length) || 0}`);
        // Check if this is a Polaris Bank statement
        const isPolarisBank = extractedData.text &&
            (extractedData.text.includes('Polaris Bank Limited') || extractedData.text.includes('POLARIS BANK'));
        if (isPolarisBank) {
            console.log(`PDF TABLE EXTRACTOR - Detected Polaris Bank statement`);
            // Extract bank info
            const bankInfo = (0, polarisBankParser_js_1.extractPolarisBankInfo)(extractedData.text || '');
            console.log(`PDF TABLE EXTRACTOR - Bank info extracted:`, JSON.stringify(bankInfo, null, 2));
            // Try direct extraction with the Polaris Bank parser
            const polarisTable = (0, polarisBankParser_js_1.extractPolarisBankTable)(extractedData.text || '');
            if (polarisTable) {
                console.log(`PDF TABLE EXTRACTOR - Successfully extracted Polaris Bank table with ${polarisTable.rows.length} rows`);
                // Format the transactions for JSON output
                const transactions = polarisTable.rows.map(row => ({
                    entryDate: row.EntryDate || '',
                    details: row.Details || '',
                    valueDate: row.ValueDate || '',
                    debit: row.Debit || '',
                    credit: row.Credit || '',
                    balance: row.Balance || ''
                }));
                // Create the final output
                const output = {
                    bankInfo,
                    transactions,
                    rawTable: polarisTable
                };
                // Save the output to a JSON file
                const outputPath = path_1.default.join(path_1.default.dirname(pdfFilePath), `${path_1.default.basename(pdfFilePath, '.pdf')}-extraction.json`);
                fs_1.default.writeFileSync(outputPath, JSON.stringify(output, null, 2));
                console.log(`PDF TABLE EXTRACTOR - Output saved to: ${outputPath}`);
                // Log a sample of the transactions
                console.log(`PDF TABLE EXTRACTOR - Sample transactions (first 3):`);
                console.log(JSON.stringify(transactions.slice(0, 3), null, 2));
            }
            else {
                console.log(`PDF TABLE EXTRACTOR - Failed to extract Polaris Bank table`);
                // Save the raw extracted data
                const outputPath = path_1.default.join(path_1.default.dirname(pdfFilePath), `${path_1.default.basename(pdfFilePath, '.pdf')}-raw.json`);
                fs_1.default.writeFileSync(outputPath, JSON.stringify(cleanForJson(extractedData), null, 2));
                console.log(`PDF TABLE EXTRACTOR - Raw data saved to: ${outputPath}`);
            }
        }
        else {
            console.log(`PDF TABLE EXTRACTOR - Not a Polaris Bank statement`);
            // Save the raw extracted data
            const outputPath = path_1.default.join(path_1.default.dirname(pdfFilePath), `${path_1.default.basename(pdfFilePath, '.pdf')}-raw.json`);
            fs_1.default.writeFileSync(outputPath, JSON.stringify(cleanForJson(extractedData), null, 2));
            console.log(`PDF TABLE EXTRACTOR - Raw data saved to: ${outputPath}`);
        }
    }
    catch (error) {
        console.error('Error extracting tables from PDF:', error);
    }
}
main().catch(console.error);
