/**
 * Python PDF Table Extractor Wrapper
 *
 * This module provides a TypeScript wrapper for the Python PDF table extractor.
 * It spawns a Python process to extract tables from PDF files and returns the result.
 */

import { spawn } from 'child_process';
import path from 'path';
import { promisify } from 'util';
import fs from 'fs';
import { Table, ExtractedPdfData } from './pdfTableExtractor.js';

// Convert fs functions to Promise-based
const statAsync = promisify(fs.stat);

/**
 * Extract tables from PDF using the Python PDF table extractor
 */
export async function extractTablesWithPython(filePath: string): Promise<ExtractedPdfData> {
  try {
    // Check if the file exists
    await statAsync(filePath);
    
    // Path to the Python script (relative to the project root)
    const scriptPath = path.resolve(process.cwd(), '../pdf_bridge.py');
    
    return new Promise((resolve, reject) => {
      // Spawn a Python process
      const pythonProcess = spawn('python', [scriptPath, filePath]);
      
      let outputData = '';
      let errorData = '';
      
      // Collect data from stdout
      pythonProcess.stdout.on('data', (data) => {
        outputData += data.toString();
      });
      
      // Collect data from stderr
      pythonProcess.stderr.on('data', (data) => {
        errorData += data.toString();
      });
      
      // Handle process completion
      pythonProcess.on('close', (code) => {
        if (code !== 0) {
          console.error('Python process exited with code', code);
          console.error('Error output:', errorData);
          reject(new Error(`Python process failed with code ${code}: ${errorData}`));
          return;
        }
        
        try {
          // Parse the JSON output
          const result = JSON.parse(outputData);
          
          if (!result.success) {
            reject(new Error(result.error || 'Unknown error in Python script'));
            return;
          }
          
          // Convert to the expected format
          const extractedData: ExtractedPdfData = {
            text: result.text || '',
            tables: result.tables || [],
            bankInfo: {
              bankName: '',
              address: [],
              recipient: []
            },
            metadata: result.metadata || {}
          };
          
          resolve(extractedData);
        } catch (error) {
          console.error('Error parsing Python output:', error);
          reject(error);
        }
      });
    });
  } catch (error) {
    console.error('Error extracting tables with Python:', error);
    throw error;
  }
}

export default {
  extractTablesWithPython
};
