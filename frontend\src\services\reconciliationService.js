/**
 * Reconciliation Service
 *
 * This service handles all API calls related to reconciliation functionality
 */

import axios from 'axios';
import { createAuthenticatedAxiosInstance } from './api';
import { RECONCILIATION_ROUTES, STATEMENT_ROUTES, TRANSACTION_ROUTES } from '../apiRoutes';

// Statement-related API calls
export const statementService = {
  // Get all statements
  getAllStatements: async () => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(STATEMENT_ROUTES.GET_ALL);
      return response.data;
    } catch (error) {
      console.error('Error fetching statements:', error);
      throw error;
    }
  },

  // Get statement by ID
  getStatementById: async (id) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(STATEMENT_ROUTES.GET_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error(`Error fetching statement ${id}:`, error);
      throw error;
    }
  },

  // Delete statement
  deleteStatement: async (id) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.delete(STATEMENT_ROUTES.DELETE(id));
      return response.data;
    } catch (error) {
      console.error(`Error deleting statement ${id}:`, error);
      throw error;
    }
  },

  // Upload statement
  uploadStatement: async (formData) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.post(STATEMENT_ROUTES.UPLOAD, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading statement:', error);
      throw error;
    }
  },

  // Get statement preview
  getStatementPreview: async (id) => {
    try {
      const startTime = performance.now();
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(STATEMENT_ROUTES.GET_PREVIEW(id));

      // Calculate fetch time
      const fetchTime = Math.round(performance.now() - startTime);

      // Add fetch time to the response
      if (response.data && response.data.data) {
        response.data.data.fetchTime = fetchTime;
      }

      return response.data;
    } catch (error) {
      console.error(`Error fetching statement preview for ${id}:`, error);
      throw error;
    }
  },
};

// Transaction-related API calls
export const transactionService = {
  // Get all transactions
  getAllTransactions: async () => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(TRANSACTION_ROUTES.GET_ALL);
      return response.data;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }
  },

  // Get transaction by ID
  getTransactionById: async (id) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(TRANSACTION_ROUTES.GET_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error(`Error fetching transaction ${id}:`, error);
      throw error;
    }
  },

  // Create transaction
  createTransaction: async (transactionData) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.post(TRANSACTION_ROUTES.CREATE, transactionData);
      return response.data;
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw error;
    }
  },

  // Update transaction
  updateTransaction: async (id, transactionData) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.put(TRANSACTION_ROUTES.UPDATE(id), transactionData);
      return response.data;
    } catch (error) {
      console.error(`Error updating transaction ${id}:`, error);
      throw error;
    }
  },

  // Delete transaction
  deleteTransaction: async (id) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.delete(TRANSACTION_ROUTES.DELETE(id));
      return response.data;
    } catch (error) {
      console.error(`Error deleting transaction ${id}:`, error);
      throw error;
    }
  },

  // Get transactions by statement ID
  getTransactionsByStatement: async (statementId) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(TRANSACTION_ROUTES.GET_BY_STATEMENT(statementId));
      return response.data;
    } catch (error) {
      console.error(`Error fetching transactions for statement ${statementId}:`, error);
      throw error;
    }
  },

  // Match transactions
  matchTransactions: async (matchData) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.post(TRANSACTION_ROUTES.MATCH, matchData);
      return response.data;
    } catch (error) {
      console.error('Error matching transactions:', error);
      throw error;
    }
  },

  // Unmatch transaction
  unmatchTransaction: async (id) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.post(TRANSACTION_ROUTES.UNMATCH(id));
      return response.data;
    } catch (error) {
      console.error(`Error unmatching transaction ${id}:`, error);
      throw error;
    }
  },
};

// Reconciliation-related API calls
export const reconciliationService = {
  // Get all reconciliations
  getAllReconciliations: async () => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(RECONCILIATION_ROUTES.GET_ALL);
      return response.data;
    } catch (error) {
      console.error('Error fetching reconciliations:', error);
      throw error;
    }
  },

  // Get reconciliation by ID
  getReconciliationById: async (id) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(RECONCILIATION_ROUTES.GET_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error(`Error fetching reconciliation ${id}:`, error);
      throw error;
    }
  },

  // Create reconciliation
  createReconciliation: async (reconciliationData) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.post(RECONCILIATION_ROUTES.CREATE, reconciliationData);
      return response.data;
    } catch (error) {
      console.error('Error creating reconciliation:', error);
      throw error;
    }
  },

  // Get reconciliation transactions
  getReconciliationTransactions: async (id) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(RECONCILIATION_ROUTES.GET_TRANSACTIONS(id));
      return response.data;
    } catch (error) {
      console.error(`Error fetching transactions for reconciliation ${id}:`, error);
      throw error;
    }
  },

  // Complete reconciliation
  completeReconciliation: async (id) => {
    try {
      const api = createAuthenticatedAxiosInstance();
      const response = await api.post(RECONCILIATION_ROUTES.COMPLETE(id));
      return response.data;
    } catch (error) {
      console.error(`Error completing reconciliation ${id}:`, error);
      throw error;
    }
  },
};

// Export all services
export default {
  statement: statementService,
  transaction: transactionService,
  reconciliation: reconciliationService,
};
