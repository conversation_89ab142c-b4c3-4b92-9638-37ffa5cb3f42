# Bank Reconciliation API - Python Backend

This is the Python backend for the Bank Reconciliation application. It provides APIs for user authentication, file management, statement processing, and reconciliation.

## Directory Structure

```
backend/
├── src/
│   ├── config/           # Configuration files
│   ├── controllers/      # Request handlers
│   ├── middleware/       # Middleware functions
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   ├── services/         # Business logic
│   ├── utils/            # Utility functions
│   └── index.py          # Main application file
├── requirements.txt      # Python dependencies
├── run.py                # Production server script
└── dev.py                # Development server with hot reload
```

## Setup

1. Create a virtual environment:
   ```
   python -m venv venv
   ```

2. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Unix/MacOS: `source venv/bin/activate`

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create a `.env` file in the root directory with the following variables:
   ```
   PORT=8080
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/bank_reconciliation
   MONGODB_DATABASE=bank_reconciliation
   JWT_SECRET=your_jwt_secret_key
   JWT_EXPIRATION=86400
   ```

## Running the Server

### Development Mode (with hot reload)

```
python dev.py
```

### Production Mode

```
python run.py
```

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user

### Users

- `GET /api/users/me` - Get current user
- `PUT /api/users/me` - Update user profile
- `DELETE /api/users/me` - Delete user

### Files

- `POST /api/files/upload` - Upload a file
- `GET /api/files/preview/:filePath` - Preview file content
- `GET /api/files/download/:filePath` - Download file
- `POST /api/files/analyze` - Analyze file content

### Statements

- `GET /api/statements` - Get all statements
- `GET /api/statements/:id` - Get statement by ID
- `DELETE /api/statements/:id` - Delete statement
- `POST /api/statements/upload` - Upload statement
- `POST /api/statements/parse` - Parse statement file
- `GET /api/statements/:id/preview` - Get statement preview
- `GET /api/statements/:id/download` - Download statement file

### Transactions

- `GET /api/transactions` - Get all transactions
- `GET /api/transactions/:id` - Get transaction by ID
- `POST /api/transactions` - Create transaction
- `PUT /api/transactions/:id` - Update transaction
- `DELETE /api/transactions/:id` - Delete transaction
- `GET /api/transactions/statement/:id` - Get transactions by statement
- `POST /api/transactions/match` - Match transactions
- `POST /api/transactions/:id/unmatch` - Unmatch transaction

### Reconciliations

- `GET /api/reconciliations` - Get all reconciliations
- `GET /api/reconciliations/:id` - Get reconciliation by ID
- `POST /api/reconciliations` - Create reconciliation
- `GET /api/reconciliations/:id/transactions` - Get reconciliation transactions
- `POST /api/reconciliations/:id/complete` - Complete reconciliation

### Health

- `GET /api/health` - Health check
