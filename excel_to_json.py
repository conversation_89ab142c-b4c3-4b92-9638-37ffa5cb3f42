#!/usr/bin/env python
"""
Script to extract data from Excel files in the uploads directory and save it as JSON files.
This helps with debugging and inspecting the data structure.
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
import glob
import sys
from pathlib import Path

# Configure paths
UPLOADS_DIR = os.path.join(os.getcwd(), 'backend', 'uploads')  # Search in the backend/uploads directory
BACKEND_DIR = os.path.join(os.getcwd(), 'backend')  # Also search in the entire backend directory
OUTPUT_DIR = os.path.join(os.getcwd(), 'excel_logs')

# Create output directory if it doesn't exist
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Helper function to convert numpy types to Python native types
def convert_numpy_type(value):
    """Convert numpy data types to Python native types for JSON serialization."""
    if isinstance(value, np.integer):
        return int(value)
    elif isinstance(value, np.floating):
        return float(value)
    elif isinstance(value, np.ndarray):
        return value.tolist()
    elif isinstance(value, np.bool_):
        return bool(value)
    elif isinstance(value, (np.datetime64, pd.Timestamp)):
        return pd.Timestamp(value).isoformat()
    elif isinstance(value, datetime):
        return value.isoformat()
    elif pd.isna(value):
        return None
    else:
        return value

# Custom JSON encoder to handle numpy types
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (np.integer, np.floating, np.bool_)):
            return convert_numpy_type(obj)
        elif isinstance(obj, (np.ndarray,)):
            return obj.tolist()
        elif isinstance(obj, (np.datetime64, pd.Timestamp)):
            return pd.Timestamp(obj).isoformat()
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif pd.isna(obj):
            return None
        return super(NumpyEncoder, self).default(obj)

def normalize_bank_statement_dataframe(df):
    """
    Normalize a bank statement DataFrame to have consistent column names and structure.
    Specifically handles Randalpha bank statement format.
    """
    # Make a copy to avoid modifying the original
    clean_df = df.copy()

    # First, check if this is a Randalpha bank statement by looking for specific headers or patterns
    is_randalpha = False

    # Check for "RANDALPHA MICROFINANCE BANK LIMITED" in the first few rows
    for i in range(min(10, len(clean_df))):
        for col in clean_df.columns:
            cell_value = str(clean_df.iloc[i, clean_df.columns.get_loc(col)])
            if "RANDALPHA MICROFINANCE BANK LIMITED" in cell_value:
                is_randalpha = True
                break

    if is_randalpha:
        # This is a Randalpha bank statement, so we need to find the actual data table
        # Look for the row with "Financial Data" or "Transaction Date" as a header
        header_row_idx = None
        for idx, row in clean_df.iterrows():
            row_values = [str(val).lower() for val in row.values if str(val).strip()]
            if any('financial data' in val or 'transaction date' in val for val in row_values):
                header_row_idx = idx
                break

        if header_row_idx is not None:
            # Extract the header row
            header_row = clean_df.iloc[header_row_idx].tolist()

            # Clean up header names
            header_row = [str(h).strip() for h in header_row]

            # Create a new DataFrame with the correct headers
            new_df = pd.DataFrame(clean_df.iloc[header_row_idx+1:].values, columns=header_row)

            # Clean up any unnamed columns
            new_df = new_df.loc[:, ~new_df.columns.str.contains('^Unnamed')]

            # Extract account information from the top of the statement
            account_info = {}
            for i in range(header_row_idx):
                for col in clean_df.columns:
                    cell_value = str(clean_df.iloc[i, clean_df.columns.get_loc(col)])
                    if "Account Number" in cell_value:
                        parts = cell_value.split(":")
                        if len(parts) > 1:
                            account_info['account_number'] = parts[1].strip()
                    elif "Financial Date Range" in cell_value:
                        parts = cell_value.split(":")
                        if len(parts) > 1:
                            account_info['date_range'] = parts[1].strip()

            # Store account info in a global variable or return it separately
            print(f"Extracted account info: {account_info}")

            return new_df, account_info

    # If not a Randalpha statement or couldn't find the header row, proceed with generic normalization

    # Clean up column names - remove whitespace and special characters
    clean_df.columns = [str(col).strip() for col in clean_df.columns]

    # Remove unnamed columns
    clean_df = clean_df.loc[:, ~clean_df.columns.str.contains('^Unnamed')]

    return clean_df, {}

def process_excel_file(file_path):
    """Process an Excel file and return its data as a JSON-serializable dictionary."""
    print(f"Processing file: {file_path}")

    # Get file info
    file_name = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)

    try:
        # For Randalpha bank statements, we need a more specific approach
        if "Account_Ledger_Report" in file_name:
            print("Detected Randalpha bank statement format")

            # Read the Excel file with specific parameters
            # Skip the header rows and read the actual transaction data
            try:
                # First, read the header information
                header_df = pd.read_excel(file_path, nrows=10)

                # Extract account information
                account_info = {}
                for idx, row in header_df.iterrows():
                    for col in header_df.columns:
                        cell_value = str(row[col])
                        if "Account Number" in cell_value and ":" in cell_value:
                            parts = cell_value.split(":")
                            account_info['accountNumber'] = parts[1].strip()
                        elif "Financial Date Range" in cell_value and ":" in cell_value:
                            parts = cell_value.split(":")
                            account_info['dateRange'] = parts[1].strip()
                        elif "Opening Bal" in cell_value and ":" in cell_value:
                            parts = cell_value.split(":")
                            account_info['openingBalance'] = parts[1].strip()
                        elif "Closing Bal" in cell_value and ":" in cell_value:
                            parts = cell_value.split(":")
                            account_info['closingBalance'] = parts[1].strip()
                        elif "Total DR" in cell_value and ":" in cell_value:
                            parts = cell_value.split(":")
                            account_info['totalDebit'] = parts[1].strip()
                        elif "Total CR" in cell_value and ":" in cell_value:
                            parts = cell_value.split(":")
                            account_info['totalCredit'] = parts[1].strip()

                # Now find the row with the column headers
                header_row = None
                for idx, row in header_df.iterrows():
                    row_values = [str(row[col]) for col in header_df.columns]
                    row_text = ' '.join(row_values).lower()
                    if "financial date" in row_text and "transaction date" in row_text:
                        header_row = idx
                        break

                if header_row is None:
                    # Try another approach - look for specific column headers
                    for idx in range(10, 20):  # Check rows 10-20
                        try:
                            test_df = pd.read_excel(file_path, skiprows=idx, nrows=1)
                            cols = [str(col).lower() for col in test_df.columns]
                            if any("financial date" in col for col in cols) or any("transaction date" in col for col in cols):
                                header_row = idx
                                break
                        except:
                            continue

                if header_row is None:
                    print("Could not find header row, using default")
                    header_row = 9  # Default to row 10 (0-indexed) if not found

                print(f"Found header row at index {header_row}")

                # Now read the actual transaction data
                transaction_df = pd.read_excel(file_path, skiprows=header_row)

                # Print the first few column names to debug
                print("Column names found:", transaction_df.columns.tolist()[:10])

                # Clean up column names
                transaction_df.columns = [str(col).strip() for col in transaction_df.columns]

                # Remove unnamed columns
                transaction_df = transaction_df.loc[:, ~transaction_df.columns.str.contains('^Unnamed')]

                # Map column names to expected format
                column_mapping = {}
                for col in transaction_df.columns:
                    col_lower = col.lower()
                    if 'financial date' in col_lower:
                        column_mapping[col] = 'Financial Date'
                    elif 'transaction date' in col_lower:
                        column_mapping[col] = 'Transaction Date'
                    elif 'reference no' in col_lower or 'ref no' in col_lower or 'ref.' in col_lower:
                        column_mapping[col] = 'Reference No.'
                    elif 'instrument no' in col_lower or 'instr no' in col_lower or 'instr.' in col_lower:
                        column_mapping[col] = 'Instrument No.'
                    elif 'narration' in col_lower or 'details' in col_lower or 'description' in col_lower:
                        column_mapping[col] = 'Narration'
                    elif 'username' in col_lower or 'user' in col_lower:
                        column_mapping[col] = 'Username'
                    elif col_lower == 'dr' or 'debit' in col_lower:
                        column_mapping[col] = 'DR'
                    elif col_lower == 'cr' or 'credit' in col_lower:
                        column_mapping[col] = 'CR'
                    elif 'avail' in col_lower and 'bal' in col_lower or 'balance' in col_lower:
                        column_mapping[col] = 'Avail. Bal'
                    elif 'entry code' in col_lower or 'entry' in col_lower and 'code' in col_lower:
                        column_mapping[col] = 'Entry Code'

                # Rename columns
                if column_mapping:
                    transaction_df = transaction_df.rename(columns=column_mapping)
                    print("Mapped columns:", transaction_df.columns.tolist())

                # Convert DataFrame to records (list of dictionaries)
                records = transaction_df.to_dict(orient='records')

                # Convert numpy types to Python native types
                for record in records:
                    for key, value in record.items():
                        record[key] = convert_numpy_type(value)

                # Create a preview text
                preview_text = transaction_df.head(5).to_string()

                # Create the response structure in the expected format
                response = {
                    "status": "success",
                    "data": {
                        "metadata": {
                            "fileName": file_name,
                            "fileSize": file_size,
                            "fileType": "excel",
                            "lastModified": datetime.utcnow().isoformat(),
                            "mimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            "rowCount": len(transaction_df),
                            "columnCount": len(transaction_df.columns),
                            "columns": transaction_df.columns.tolist(),
                            "creationDate": datetime.utcnow().isoformat(),
                            "bankName": "RANDALPHA MICROFINANCE BANK LIMITED",
                            **account_info
                        },
                        "content": records,
                        "preview": preview_text,
                        "summary": {
                            "totalTransactions": len(records),
                            "dateRange": account_info.get('dateRange', ''),
                            "openingBalance": account_info.get('openingBalance', ''),
                            "closingBalance": account_info.get('closingBalance', ''),
                            "totalDebit": account_info.get('totalDebit', ''),
                            "totalCredit": account_info.get('totalCredit', '')
                        },
                        "insights": {
                            "suggestions": [],
                            "anomalies": [],
                            "patterns": []
                        }
                    },
                    "debug_info": {
                        "header_info": header_df.to_dict(orient='records'),  # Include header info for debugging
                        "account_info": account_info  # Include extracted account info
                    }
                }

                return response

            except Exception as e:
                print(f"Error processing Randalpha bank statement: {str(e)}")
                # Fall back to generic processing

        # Generic Excel file processing
        df = pd.read_excel(file_path)

        # Normalize the DataFrame
        normalized_df, account_info = normalize_bank_statement_dataframe(df)

        # Convert DataFrame to records (list of dictionaries)
        records = normalized_df.to_dict(orient='records')

        # Convert numpy types to Python native types
        for record in records:
            for key, value in record.items():
                record[key] = convert_numpy_type(value)

        # Create a preview text
        preview_text = normalized_df.head(5).to_string()

        # Create the response structure in the expected format
        response = {
            "status": "success",
            "data": {
                "metadata": {
                    "fileName": file_name,
                    "fileSize": file_size,
                    "fileType": "excel",
                    "lastModified": datetime.utcnow().isoformat(),
                    "mimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "rowCount": len(normalized_df),
                    "columnCount": len(normalized_df.columns),
                    "columns": normalized_df.columns.tolist(),
                    "creationDate": datetime.utcnow().isoformat(),
                    **account_info
                },
                "content": records,
                "preview": preview_text,
                "summary": {
                    "totalTransactions": len(records),
                    "dateRange": account_info.get('dateRange', ''),
                    "openingBalance": account_info.get('openingBalance', ''),
                    "closingBalance": account_info.get('closingBalance', ''),
                    "totalDebit": account_info.get('totalDebit', ''),
                    "totalCredit": account_info.get('totalCredit', '')
                },
                "insights": {
                    "suggestions": [],
                    "anomalies": [],
                    "patterns": []
                }
            },
            "debug_info": {
                "raw_dataframe": df.to_dict(orient='records')  # Include the original DataFrame for debugging
            }
        }

        return response

    except Exception as e:
        print(f"Error processing file {file_path}: {str(e)}")
        return {
            "error": str(e),
            "metadata": {
                "fileName": file_name,
                "fileSize": file_size,
                "fileType": "excel",
                "lastModified": datetime.utcnow().isoformat()
            }
        }

def main():
    """Main function to process all Excel files in the uploads directory."""
    # Find all Excel files in the uploads directory
    excel_files = []

    # Search in uploads directory
    if os.path.exists(UPLOADS_DIR):
        excel_files.extend(glob.glob(os.path.join(UPLOADS_DIR, '**', '*.xlsx'), recursive=True))
        excel_files.extend(glob.glob(os.path.join(UPLOADS_DIR, '**', '*.xls'), recursive=True))

    # Search in the entire backend directory
    if os.path.exists(BACKEND_DIR):
        excel_files.extend(glob.glob(os.path.join(BACKEND_DIR, '**', '*.xlsx'), recursive=True))
        excel_files.extend(glob.glob(os.path.join(BACKEND_DIR, '**', '*.xls'), recursive=True))

    # Remove duplicates
    excel_files = list(set(excel_files))

    if not excel_files:
        print(f"No Excel files found in {UPLOADS_DIR} or {BACKEND_DIR}")
        return

    print(f"Found {len(excel_files)} Excel files:")
    for file_path in excel_files:
        print(f"  - {file_path}")

    # Process each file
    for file_path in excel_files:
        try:
            # Process the file
            data = process_excel_file(file_path)

            # Create output file path
            file_name = os.path.basename(file_path)
            output_path = os.path.join(OUTPUT_DIR, f"{Path(file_name).stem}_log.json")

            # Save the data to a JSON file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, cls=NumpyEncoder)

            print(f"Saved data to {output_path}")

        except Exception as e:
            print(f"Error processing {file_path}: {str(e)}")

if __name__ == "__main__":
    main()
