{"name": "backend-ts", "version": "1.0.0", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1", "serve": "npm run build && npm run start", "extract-pdf": "node ee.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@mkas3/pdf-table-parser": "^1.2.18", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/morgan": "^1.9.9", "backend-ts": "file:", "bcrypt": "^6.0.0", "canvas": "^3.1.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "pdf-parse": "^1.1.1", "pdf-table-extractor-ts": "^1.1.1", "pdf.js-extract": "^0.2.1", "pdfjs-dist": "^5.2.133", "ts-node": "^10.9.2", "typescript": "^5.8.3", "xlsx": "^0.18.5"}, "devDependencies": {"@types/multer": "^1.4.12", "ts-node-dev": "^2.0.0"}}