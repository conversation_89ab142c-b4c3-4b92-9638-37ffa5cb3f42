"""
PDF Table Extractor utility for the Bank Reconciliation API.
"""

import os
import pdfplumber
from typing import Dict, List, Any, Optional
import json

class TableCell:
    """
    Table cell class.
    """
    def __init__(self, text, x, y, width, height, font_name=None):
        self.text = text
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.font_name = font_name

    def to_dict(self):
        """
        Convert cell to dictionary.
        """
        return {
            'text': self.text,
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height,
            'fontName': self.font_name
        }

class Table:
    """
    Table class.
    """
    def __init__(self, headers=None, rows=None):
        self.headers = headers or []
        self.rows = rows or []

    def to_dict(self):
        """
        Convert table to dictionary.
        """
        return {
            'headers': self.headers,
            'rows': self.rows
        }

class ExtractedPdfData:
    """
    Extracted PDF data class.
    """
    def __init__(self, text='', tables=None, page_count=0):
        self.text = text
        self.tables = tables or []
        self.page_count = page_count

    def to_dict(self):
        """
        Convert extracted data to dictionary.
        """
        return {
            'text': self.text,
            'tables': [table.to_dict() for table in self.tables],
            'pageCount': self.page_count
        }

def extract_tables_from_pdf(file_path: str) -> Dict[str, Any]:
    """
    Extract tables from PDF file.
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        # Open PDF with pdfplumber
        with pdfplumber.open(file_path) as pdf:
            page_count = len(pdf.pages)
            all_text = ""
            all_tables = []

            # Process each page
            for page_num, page in enumerate(pdf.pages):
                # Extract text
                page_text = page.extract_text() or ""
                all_text += page_text + "\n\n"

                # Extract tables
                tables = page.extract_tables()
                if tables:
                    for table in tables:
                        # Process table
                        headers = table[0] if table else []
                        rows = []

                        # Skip header row and process data rows
                        for row in table[1:] if table else []:
                            # Create a dictionary for each row
                            row_dict = {}
                            for i, cell in enumerate(row):
                                if i < len(headers) and headers[i]:
                                    header = headers[i].strip()
                                    row_dict[header] = cell.strip() if cell else ""

                            if row_dict:  # Only add non-empty rows
                                rows.append(row_dict)

                        # Add table to results
                        if headers and rows:
                            all_tables.append({
                                'headers': [h.strip() if h else "" for h in headers],
                                'rows': rows
                            })

            # Return extracted data
            return {
                'text': all_text,
                'tables': all_tables,
                'pageCount': page_count
            }

    except Exception as e:
        print(f"Error extracting tables from PDF: {str(e)}")
        # Return empty result on error
        return {
            'text': "",
            'tables': [],
            'pageCount': 0,
            'error': str(e)
        }
