import mongoose, { Document, Schema } from 'mongoose';

// Reconciliation status enum
export enum ReconciliationStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

// Reconciliation interface
export interface IReconciliation extends Document {
  name: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  status: ReconciliationStatus;
  totalTransactions: number;
  matchedTransactions: number;
  unmatchedTransactions: number;
  userId: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Reconciliation schema
const reconciliationSchema = new Schema<IReconciliation>(
  {
    name: {
      type: String,
      required: [true, 'Reconciliation name is required'],
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required'],
    },
    endDate: {
      type: Date,
      required: [true, 'End date is required'],
    },
    status: {
      type: String,
      enum: Object.values(ReconciliationStatus),
      default: ReconciliationStatus.IN_PROGRESS,
    },
    totalTransactions: {
      type: Number,
      default: 0,
    },
    matchedTransactions: {
      type: Number,
      default: 0,
    },
    unmatchedTransactions: {
      type: Number,
      default: 0,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
  },
  {
    timestamps: true,
  }
);

// Create and export Reconciliation model
export default mongoose.model<IReconciliation>('Reconciliation', reconciliationSchema);
