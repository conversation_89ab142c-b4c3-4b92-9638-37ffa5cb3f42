import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const DepartmentSidebar = ({ menuItems, department }) => {
  const location = useLocation();

  return (
    <div className="fixed left-0 h-screen w-64 bg-white/30 backdrop-blur-xl border-r border-white/20 p-6">
      <div className="mb-8">
        <h2 className="text-xl font-bold text-[#412D6C]">{department}</h2>
        <p className="text-sm text-gray-600">Dashboard</p>
      </div>
      
      <nav className="space-y-2">
        {menuItems.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all ${
              location.pathname === item.path
                ? 'bg-[#412D6C] text-white'
                : 'hover:bg-white/50 text-gray-700'
            }`}
          >
            <span className="text-xl">{item.icon}</span>
            <span>{item.label}</span>
          </Link>
        ))}
      </nav>

      <div className="absolute bottom-6 left-6 right-6">
        <div className="bg-white/50 rounded-lg p-4">
          <p className="text-sm text-gray-600">Need help?</p>
          <a href="/support" className="text-[#412D6C] text-sm font-medium hover:underline">
            Contact Support
          </a>
        </div>
      </div>
    </div>
  );
};

export default DepartmentSidebar;
