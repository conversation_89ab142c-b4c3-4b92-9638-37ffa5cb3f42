import React from 'react';
import useStore from '../../store/useStore';

const GlobalTimeTracker = () => {
  const { isClockIn, currentSession, clockIn, clockOut } = useStore(state => state.timeTracking);

  const formatTime = (ms) => {
    if (!ms) return "00:00:00";
    const seconds = Math.floor((ms / 1000) % 60);
    const minutes = Math.floor((ms / 1000 / 60) % 60);
    const hours = Math.floor(ms / 1000 / 60 / 60);
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleClockInOut = () => {
    if (!isClockIn) {
      clockIn();
    } else {
      clockOut();
    }
  };

  return (
    <div className="flex items-center gap-4">
      <div className="px-4 py-2 bg-teal-900/10 rounded-lg">
        <span className="text-sm mr-2">Session Time:</span>
        <span className="font-mono font-medium text-teal-800">
          {formatTime(currentSession)}
        </span>
      </div>
      <button
        onClick={handleClockInOut}
        className={`px-4 py-2 rounded-lg text-white transition-colors ${
          isClockIn 
            ? 'bg-red-600 hover:bg-red-700' 
            : 'bg-green-600 hover:bg-green-700'
        }`}
      >
        {isClockIn ? 'Clock Out' : 'Clock In'}
      </button>
    </div>
  );
};

export default GlobalTimeTracker;
