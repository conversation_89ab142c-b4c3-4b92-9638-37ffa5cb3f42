"""
Statement model for the Bank Reconciliation API.
"""

from datetime import datetime
from bson import ObjectId
from pymongo import MongoClient, ASCENDING
from config import config

# Connect to MongoDB
client = MongoClient(config['mongo_uri'])
db = client[config['mongo_database']]
statements_collection = db['statements']

# Create indexes
statements_collection.create_index([('userId', ASCENDING)])
statements_collection.create_index([('statement_type', ASCENDING)])
statements_collection.create_index([('statement_date', ASCENDING)])

class Statement:
    """
    Statement model class.
    """
    def __init__(self, name, bank_name, account_number, start_date, end_date, 
                 start_balance, end_balance, file_name, file_path, user_id,
                 statement_type='bank', statement_date=None):
        self.name = name
        self.bank_name = bank_name
        self.account_number = account_number
        self.start_date = start_date
        self.end_date = end_date
        self.start_balance = start_balance
        self.end_balance = end_balance
        self.file_name = file_name
        self.file_path = file_path
        self.statement_type = statement_type
        self.statement_date = statement_date or datetime.now()
        self.user_id = ObjectId(user_id)
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def to_dict(self):
        """
        Convert statement object to dictionary.
        """
        return {
            'name': self.name,
            'bankName': self.bank_name,
            'accountNumber': self.account_number,
            'startDate': self.start_date,
            'endDate': self.end_date,
            'startBalance': self.start_balance,
            'endBalance': self.end_balance,
            'fileName': self.file_name,
            'filePath': self.file_path,
            'statement_type': self.statement_type,
            'statement_date': self.statement_date,
            'userId': str(self.user_id),
            'createdAt': self.created_at,
            'updatedAt': self.updated_at
        }
    
    @classmethod
    def find_by_id(cls, statement_id, user_id=None):
        """
        Find statement by ID.
        """
        query = {'_id': ObjectId(statement_id)}
        if user_id:
            query['userId'] = ObjectId(user_id)
            
        statement_data = statements_collection.find_one(query)
        if not statement_data:
            return None
        
        return cls._create_from_data(statement_data)
    
    @classmethod
    def find_all_by_user(cls, user_id, limit=100, skip=0):
        """
        Find all statements by user ID.
        """
        cursor = statements_collection.find(
            {'userId': ObjectId(user_id)}
        ).sort('createdAt', -1).skip(skip).limit(limit)
        
        return [cls._create_from_data(data) for data in cursor]
    
    @classmethod
    def _create_from_data(cls, data):
        """
        Create statement object from database data.
        """
        statement = cls(
            name=data['name'],
            bank_name=data['bankName'],
            account_number=data['accountNumber'],
            start_date=data['startDate'],
            end_date=data['endDate'],
            start_balance=data['startBalance'],
            end_balance=data['endBalance'],
            file_name=data['fileName'],
            file_path=data['filePath'],
            user_id=data['userId'],
            statement_type=data.get('statement_type', 'bank'),
            statement_date=data.get('statement_date', datetime.now())
        )
        statement._id = data['_id']
        statement.created_at = data.get('createdAt', datetime.now())
        statement.updated_at = data.get('updatedAt', datetime.now())
        
        return statement
    
    def save(self):
        """
        Save statement to database.
        """
        statement_data = {
            'name': self.name,
            'bankName': self.bank_name,
            'accountNumber': self.account_number,
            'startDate': self.start_date,
            'endDate': self.end_date,
            'startBalance': self.start_balance,
            'endBalance': self.end_balance,
            'fileName': self.file_name,
            'filePath': self.file_path,
            'statement_type': self.statement_type,
            'statement_date': self.statement_date,
            'userId': self.user_id,
            'updatedAt': datetime.now()
        }
        
        if hasattr(self, '_id'):
            # Update existing statement
            statements_collection.update_one(
                {'_id': self._id},
                {'$set': statement_data}
            )
        else:
            # Create new statement
            statement_data['createdAt'] = self.created_at
            result = statements_collection.insert_one(statement_data)
            self._id = result.inserted_id
        
        return self
    
    def delete(self):
        """
        Delete statement from database.
        """
        if hasattr(self, '_id'):
            statements_collection.delete_one({'_id': self._id})
            return True
        return False
