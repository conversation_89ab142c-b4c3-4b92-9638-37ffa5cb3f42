/**
 * Bundled File Parser Engine
 * 
 * This is a self-contained JavaScript file that handles Excel and CSV parsing
 * without requiring any external dependencies except Node.js itself.
 * 
 * It can be used in any environment where Node.js is available, including
 * production environments where npm packages cannot be installed.
 */

// Node.js built-in modules
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

/**
 * Main entry point for parsing files
 * @param {string} filePath - Path to the file to parse
 * @returns {Object} Parsed file data
 */
function parseFile(filePath) {
  try {
    // Get file information
    const stats = fs.statSync(filePath);
    const fileName = path.basename(filePath);
    const fileExt = path.extname(filePath).toLowerCase();
    
    // Determine parser based on file extension
    if (fileExt === '.csv') {
      return parseCSV(filePath, fileName, stats);
    } else if (fileExt === '.xlsx' || fileExt === '.xls') {
      return parseExcel(filePath, fileName, stats);
    } else {
      throw new Error(`Unsupported file type: ${fileExt}`);
    }
  } catch (error) {
    console.error('Error parsing file:', error);
    return {
      error: error.message,
      metadata: {
        fileName: path.basename(filePath),
        fileSize: fs.statSync(filePath).size,
        fileType: path.extname(filePath).toLowerCase().substring(1),
        lastModified: new Date().toISOString()
      },
      content: [],
      preview: `Error parsing file: ${error.message}`
    };
  }
}

/**
 * Parse CSV file
 * @param {string} filePath - Path to the CSV file
 * @param {string} fileName - File name
 * @param {Object} stats - File stats
 * @returns {Object} Parsed CSV data
 */
function parseCSV(filePath, fileName, stats) {
  try {
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // Split into lines
    const lines = fileContent.split(/\r?\n/).filter(line => line.trim());
    if (lines.length === 0) {
      throw new Error('CSV file is empty');
    }
    
    // Get headers from first line
    const headers = parseCSVLine(lines[0]);
    
    // Parse rows
    const rows = [];
    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue;
      
      // Parse the line
      const values = parseCSVLine(lines[i]);
      
      // Create row object
      const row = {};
      for (let j = 0; j < Math.min(headers.length, values.length); j++) {
        row[headers[j]] = values[j];
      }
      
      rows.push(row);
    }
    
    // Calculate summary
    const summary = calculateSummary(rows);
    
    // Extract bank info
    const bankInfo = extractBankInfo(rows);
    
    return {
      metadata: {
        fileName,
        fileSize: stats.size,
        fileType: 'csv',
        lastModified: new Date(stats.mtime).toISOString(),
        mimeType: 'text/csv',
        title: bankInfo.bankName,
        subject: bankInfo.accountNumber ? `Account: ${bankInfo.accountNumber}` : null,
        keywords: bankInfo.dateRange ? [`Date Range: ${bankInfo.dateRange}`] : null
      },
      content: {
        rows,
        headers,
        bankInfo
      },
      preview: lines.slice(0, 10).join('\n'),
      summary: {
        rowCount: rows.length,
        columnCount: headers.length,
        ...summary
      }
    };
  } catch (error) {
    console.error('Error parsing CSV:', error);
    throw error;
  }
}

/**
 * Parse a CSV line handling quoted values
 * @param {string} line - CSV line to parse
 * @returns {string[]} Array of values
 */
function parseCSVLine(line) {
  const values = [];
  let currentValue = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      if (inQuotes && i + 1 < line.length && line[i + 1] === '"') {
        // Double quotes inside quotes - add a single quote
        currentValue += '"';
        i++;
      } else {
        // Toggle quote mode
        inQuotes = !inQuotes;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      values.push(currentValue.trim());
      currentValue = '';
    } else {
      // Add character to current value
      currentValue += char;
    }
  }
  
  // Add the last value
  values.push(currentValue.trim());
  
  return values;
}

/**
 * Parse Excel file using a simple CSV conversion approach
 * @param {string} filePath - Path to the Excel file
 * @param {string} fileName - File name
 * @param {Object} stats - File stats
 * @returns {Object} Parsed Excel data
 */
function parseExcel(filePath, fileName, stats) {
  try {
    // Create a temporary CSV file
    const tempDir = os.tmpdir();
    const tempFile = path.join(tempDir, `${Date.now()}_temp.csv`);
    
    // Convert Excel to CSV using a Python one-liner
    const pythonScript = `
import pandas as pd
import sys
import json

try:
    # Read Excel file
    excel_file = pd.ExcelFile("${filePath.replace(/\\/g, '\\\\')}")
    sheet_names = excel_file.sheet_names
    
    # Process each sheet
    result = {
        "sheets": sheet_names,
        "data": {}
    }
    
    for sheet_name in sheet_names:
        df = pd.read_excel("${filePath.replace(/\\/g, '\\\\')}", sheet_name=sheet_name)
        # Convert to records
        result["data"][sheet_name] = df.to_dict(orient='records')
    
    # Write to JSON file
    with open("${tempFile.replace(/\\/g, '\\\\')}", 'w') as f:
        json.dump(result, f)
    
    print("Conversion successful")
except Exception as e:
    print(f"Error: {str(e)}")
    sys.exit(1)
`;
    
    // Write the Python script to a temporary file
    const scriptFile = path.join(tempDir, `${Date.now()}_convert.py`);
    fs.writeFileSync(scriptFile, pythonScript);
    
    // Execute the Python script
    try {
      execSync(`python ${scriptFile}`, { encoding: 'utf8' });
    } catch (error) {
      console.error('Error executing Python script:', error);
      throw new Error('Failed to convert Excel file to JSON');
    }
    
    // Read the resulting JSON
    const excelData = JSON.parse(fs.readFileSync(tempFile, 'utf8'));
    
    // Clean up temporary files
    try {
      fs.unlinkSync(tempFile);
      fs.unlinkSync(scriptFile);
    } catch (cleanupError) {
      console.warn('Error cleaning up temporary files:', cleanupError);
    }
    
    // Extract first sheet data for summary and preview
    const firstSheetName = excelData.sheets[0];
    const firstSheetData = excelData.data[firstSheetName];
    
    // Calculate summary
    const summary = calculateSummary(firstSheetData);
    
    // Extract bank info
    const bankInfo = extractBankInfo(firstSheetData);
    
    // Generate preview
    const preview = JSON.stringify(firstSheetData.slice(0, 10), null, 2);
    
    return {
      metadata: {
        fileName,
        fileSize: stats.size,
        fileType: path.extname(fileName).substring(1),
        lastModified: new Date(stats.mtime).toISOString(),
        mimeType: fileName.endsWith('.xlsx') 
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
          : 'application/vnd.ms-excel',
        title: bankInfo.bankName,
        subject: bankInfo.accountNumber ? `Account: ${bankInfo.accountNumber}` : null,
        keywords: bankInfo.dateRange ? [`Date Range: ${bankInfo.dateRange}`] : null
      },
      content: excelData,
      preview,
      summary: {
        rowCount: Object.values(excelData.data).reduce((total, rows) => total + rows.length, 0),
        columnCount: firstSheetData.length > 0 ? Object.keys(firstSheetData[0]).length : 0,
        sheetCount: excelData.sheets.length,
        ...summary
      }
    };
  } catch (error) {
    console.error('Error parsing Excel:', error);
    throw error;
  }
}

/**
 * Calculate summary statistics from rows
 * @param {Object[]} rows - Array of data rows
 * @returns {Object} Summary statistics
 */
function calculateSummary(rows) {
  if (!rows || rows.length === 0) {
    return {
      startDate: null,
      endDate: null,
      startBalance: 0,
      endBalance: 0,
      totalCredits: 0,
      totalDebits: 0
    };
  }
  
  // Map common column names
  const dateColumns = ['Financial Date', 'Transaction Date', 'Date', 'EntryDate', 'ValueDate'];
  const debitColumns = ['DR', 'Debit', 'Withdrawal'];
  const creditColumns = ['CR', 'Credit', 'Deposit'];
  const balanceColumns = ['Avail. Bal', 'Balance', 'Available Balance'];
  
  // Find the actual column names in the data
  const dateColumn = findFirstMatchingColumn(rows[0], dateColumns);
  const debitColumn = findFirstMatchingColumn(rows[0], debitColumns);
  const creditColumn = findFirstMatchingColumn(rows[0], creditColumns);
  const balanceColumn = findFirstMatchingColumn(rows[0], balanceColumns);
  
  // Sort by date if possible
  let sortedRows = [...rows];
  if (dateColumn) {
    sortedRows.sort((a, b) => {
      const dateA = new Date(a[dateColumn] || '');
      const dateB = new Date(b[dateColumn] || '');
      return dateA - dateB;
    });
  }
  
  // Calculate summary
  let totalCredits = 0;
  let totalDebits = 0;
  
  sortedRows.forEach(row => {
    if (debitColumn) {
      const debit = parseFloat(row[debitColumn]) || 0;
      totalDebits += debit;
    }
    
    if (creditColumn) {
      const credit = parseFloat(row[creditColumn]) || 0;
      totalCredits += credit;
    }
  });
  
  // Get start and end dates
  const startDate = dateColumn && sortedRows.length > 0 ? sortedRows[0][dateColumn] : null;
  const endDate = dateColumn && sortedRows.length > 0 ? sortedRows[sortedRows.length - 1][dateColumn] : null;
  
  // Get start and end balances
  const startBalance = balanceColumn && sortedRows.length > 0 ? parseFloat(sortedRows[0][balanceColumn]) || 0 : 0;
  const endBalance = balanceColumn && sortedRows.length > 0 ? parseFloat(sortedRows[sortedRows.length - 1][balanceColumn]) || 0 : 0;
  
  return {
    startDate,
    endDate,
    startBalance,
    endBalance,
    totalCredits,
    totalDebits
  };
}

/**
 * Extract bank information from data rows
 * @param {Object[]} rows - Array of data rows
 * @returns {Object} Bank information
 */
function extractBankInfo(rows) {
  const bankInfo = {
    bankName: null,
    accountNumber: null,
    dateRange: null
  };
  
  if (!rows || rows.length === 0) {
    return bankInfo;
  }
  
  // Try to find bank name and account number in the first row's keys and values
  const firstRow = rows[0];
  for (const key in firstRow) {
    const keyLower = key.toLowerCase();
    const value = String(firstRow[key] || '');
    const valueLower = value.toLowerCase();
    
    if (keyLower.includes('bank')) {
      bankInfo.bankName = value;
    } else if (valueLower.includes('bank')) {
      bankInfo.bankName = value;
    }
    
    if (keyLower.includes('account') && /\d/.test(value)) {
      bankInfo.accountNumber = value;
    } else if (valueLower.includes('account') && /\d/.test(value)) {
      bankInfo.accountNumber = value;
    }
  }
  
  // Try to find date range from date columns
  const dateColumns = ['Financial Date', 'Transaction Date', 'Date', 'EntryDate', 'ValueDate'];
  const dateColumn = findFirstMatchingColumn(firstRow, dateColumns);
  
  if (dateColumn && rows.length > 1) {
    // Sort rows by date
    const sortedRows = [...rows].sort((a, b) => {
      const dateA = new Date(a[dateColumn] || '');
      const dateB = new Date(b[dateColumn] || '');
      return dateA - dateB;
    });
    
    const startDate = sortedRows[0][dateColumn];
    const endDate = sortedRows[sortedRows.length - 1][dateColumn];
    
    if (startDate && endDate) {
      bankInfo.dateRange = `${startDate} to ${endDate}`;
    }
  }
  
  return bankInfo;
}

/**
 * Find the first matching column name from a list of possibilities
 * @param {Object} row - Data row
 * @param {string[]} possibleColumns - Possible column names
 * @returns {string|null} Matching column name or null
 */
function findFirstMatchingColumn(row, possibleColumns) {
  for (const column of possibleColumns) {
    if (column in row) {
      return column;
    }
  }
  return null;
}

// Export functions for use in Node.js
module.exports = {
  parseFile,
  parseCSV,
  parseExcel
};
