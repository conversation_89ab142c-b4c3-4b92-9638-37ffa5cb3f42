import { toast } from 'react-toastify';

/**
 * Check if the current page is the login page
 * @returns {boolean} True if the current page is the login page
 */
export const isLoginPage = () => {
  // Check if the current path is /login
  return window.location.pathname === '/login';
};

/**
 * Check if the current page is the dashboard page
 * @returns {boolean} True if the current page is the dashboard page
 */
export const isDashboardPage = () => {
  // Check if the current path is /dashboard or starts with /dashboard/
  return window.location.pathname === '/dashboard' ||
         window.location.pathname.startsWith('/dashboard/');
};

/**
 * Check if the user is authenticated
 * @returns {boolean} True if the user is authenticated
 */
export const isAuthenticated = () => {
  // Check for token in localStorage
  const token = localStorage.getItem('authToken');
  return !!token;
};

/**
 * Check if we should make API calls
 * This prevents unnecessary API calls when redirected to login page
 * @returns {boolean} True if API calls should be made
 */
export const shouldMakeApiCalls = () => {
  // Don't make API calls if we're on the login page
  return !isLoginPage();
};

/**
 * Handles unauthorized errors by clearing local storage and redirecting to login
 * @param {Error} error - The error object
 * @param {Function} navigate - The navigate function from useNavigate
 * @returns {boolean} - Returns true if the error was handled as unauthorized
 */
export const handleUnauthorizedError = (error, navigate) => {
  // Check if the error is an unauthorized error (401)
  const isUnauthorized =
    error?.response?.status === 401 ||
    error?.message?.toLowerCase().includes('unauthorized') ||
    error?.message?.toLowerCase().includes('unauthenticated') ||
    error?.response?.data?.message?.toLowerCase().includes('unauthorized') ||
    error?.response?.data?.message?.toLowerCase().includes('unauthenticated') ||
    error?.response?.data?.message?.toLowerCase().includes('invalid token') ||
    error?.response?.data?.message?.toLowerCase().includes('expired token');

  if (isUnauthorized) {
    // Clear local storage
    localStorage.removeItem('authToken');
    localStorage.removeItem('auth_token_backup');
    sessionStorage.removeItem('authToken');

    // Show toast notification
    toast.error('Your session has expired. Please log in again.', {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true
    });

    // Redirect to login page
    if (navigate) {
      navigate('/login');
    } else {
      // Fallback if navigate is not available
      window.location.href = '/login';
    }

    return true;
  }

  return false;
};

/**
 * Clears all authentication data from storage
 */
export const clearAuthData = () => {
  localStorage.removeItem('authToken');
  localStorage.removeItem('auth_token_backup');
  sessionStorage.removeItem('authToken');

  // Clear any other auth-related data
  localStorage.removeItem('user');
  localStorage.removeItem('userRole');
  localStorage.removeItem('userData');
};

/**
 * Get the authentication token from storage
 * @returns {string|null} The authentication token or null if not found
 */
export const getAuthToken = () => {
  // Try to get the token from localStorage
  let token = localStorage.getItem('authToken');

  // If not found, try the backup
  if (!token) {
    token = localStorage.getItem('auth_token_backup');
  }

  // If still not found, try sessionStorage
  if (!token) {
    token = sessionStorage.getItem('authToken');
  }

  return token;
};
