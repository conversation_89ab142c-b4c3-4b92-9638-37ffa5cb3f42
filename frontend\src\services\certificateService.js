import { API_BASE_URL } from '../apiRoutes';
import { createAuthenticatedAxiosInstance } from './api';

// Create an authenticated axios instance
const api = createAuthenticatedAxiosInstance();

// Certificate Service
const certificateService = {
  // Get all certificates
  getAllCertificates: async () => {
    try {
      const token = localStorage.getItem('authToken');

      if (!token) {
        return {
          status: false,
          error: 'Authentication required. Please log in to view certificates.'
        };
      }

      const response = await api.get(`${API_BASE_URL}/certificates`);
      return response.data;
    } catch (error) {
      console.error('Error fetching all certificates:', error);

      if (error.response?.status === 401) {
        return {
          status: false,
          error: 'Your session has expired. Please log in again.'
        };
      }

      return {
        status: false,
        error: error.response?.data?.meta?.error || 'Failed to fetch certificates'
      };
    }
  },

  // Get all certificates for a user
  getUserCertificates: async (userId) => {
    try {
      const token = localStorage.getItem('authToken');

      if (!token) {
        return {
          status: false,
          error: 'Authentication required. Please log in to view your certificates.'
        };
      }

      const response = await api.get(`${API_BASE_URL}/certificates/user/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user certificates:', error);

      if (error.response?.status === 401) {
        return {
          status: false,
          error: 'Your session has expired. Please log in again.'
        };
      }

      return {
        status: false,
        error: error.response?.data?.meta?.error || 'Failed to fetch certificates'
      };
    }
  },

  // Get a certificate by ID
  getCertificateById: async (certificateId) => {
    try {
      const token = localStorage.getItem('authToken');

      if (!token) {
        return {
          status: false,
          error: 'Authentication required. Please log in to view this certificate.'
        };
      }

      const response = await api.get(`${API_BASE_URL}/certificates/${certificateId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching certificate:', error);

      if (error.response?.status === 401) {
        return {
          status: false,
          error: 'Your session has expired. Please log in again.'
        };
      }

      return {
        status: false,
        error: error.response?.data?.meta?.error || 'Failed to fetch certificate'
      };
    }
  },

  // Alias for getCertificateById for consistency
  getCertificate: async (certificateId) => {
    return certificateService.getCertificateById(certificateId);
  },

  // Verify a certificate
  verifyCertificate: async (credentialId) => {
    try {
      // This is a public endpoint, no authentication required
      const response = await fetch(`${API_BASE_URL}/certificates/verify/${credentialId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error verifying certificate:', error);
      return {
        status: false,
        error: 'Failed to verify certificate'
      };
    }
  },

  // Share a certificate
  shareCertificate: async (certificateId, platform) => {
    try {
      const token = localStorage.getItem('authToken');

      if (!token) {
        return {
          status: false,
          error: 'Authentication required. Please log in to share this certificate.'
        };
      }

      const response = await api.post(`${API_BASE_URL}/certificates/${certificateId}/share`, { platform });
      return response.data;
    } catch (error) {
      console.error('Error sharing certificate:', error);

      if (error.response?.status === 401) {
        return {
          status: false,
          error: 'Your session has expired. Please log in again.'
        };
      }

      return {
        status: false,
        error: error.response?.data?.meta?.error || 'Failed to share certificate'
      };
    }
  },

  // Delete a certificate
  deleteCertificate: async (certificateId) => {
    try {
      const token = localStorage.getItem('authToken');

      if (!token) {
        return {
          status: false,
          error: 'Authentication required. Please log in to delete this certificate.'
        };
      }

      const response = await api.delete(`${API_BASE_URL}/certificates/${certificateId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting certificate:', error);

      if (error.response?.status === 401) {
        return {
          status: false,
          error: 'Your session has expired. Please log in again.'
        };
      }

      return {
        status: false,
        error: error.response?.data?.meta?.error || 'Failed to delete certificate'
      };
    }
  },

  // Create a new certificate
  createCertificate: async (certificateData) => {
    try {
      const token = localStorage.getItem('authToken');

      if (!token) {
        return {
          status: false,
          error: 'Authentication required. Please log in to create a certificate.'
        };
      }

      const response = await api.post(`${API_BASE_URL}/certificates`, certificateData);
      return response.data;
    } catch (error) {
      console.error('Error creating certificate:', error);

      if (error.response?.status === 401) {
        return {
          status: false,
          error: 'Your session has expired. Please log in again.'
        };
      }

      return {
        status: false,
        error: error.response?.data?.meta?.error || 'Failed to create certificate'
      };
    }
  },

  // Update a certificate
  updateCertificate: async (certificateId, certificateData) => {
    try {
      const token = localStorage.getItem('authToken');

      if (!token) {
        return {
          status: false,
          error: 'Authentication required. Please log in to update this certificate.'
        };
      }

      const response = await api.put(`${API_BASE_URL}/certificates/${certificateId}`, certificateData);
      return response.data;
    } catch (error) {
      console.error('Error updating certificate:', error);

      if (error.response?.status === 401) {
        return {
          status: false,
          error: 'Your session has expired. Please log in again.'
        };
      }

      return {
        status: false,
        error: error.response?.data?.meta?.error || 'Failed to update certificate'
      };
    }
  },

  // Generate a certificate share URL
  generateShareUrl: (credentialId) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/certificates/verify/${credentialId}`;
  },

  // Generate social media share links
  generateSocialShareLinks: (certificate) => {
    const shareUrl = certificateService.generateShareUrl(certificate.credentialId);
    const shareTitle = `I earned a certificate in ${certificate.title}`;
    const shareText = `I'm proud to share my achievement from PooloTHQ: ${certificate.title}`;

    return {
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareTitle)}`,
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
      email: `mailto:?subject=${encodeURIComponent(shareTitle)}&body=${encodeURIComponent(`${shareText}\n\nVerify my certificate here: ${shareUrl}`)}`
    };
  }
};

export default certificateService;
