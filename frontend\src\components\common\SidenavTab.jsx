import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import useBackgroundFetch from '../../hooks/useBackgroundFetch';

/**
 * A reusable component for sidenav tabs with background data fetching
 * @param {Object} props - Component props
 * @returns {JSX.Element} - The rendered component
 */
const SidenavTab = ({
  to,                   // Route to navigate to
  label,                // Tab label
  icon,                 // Icon component
  fetchFunction,        // Function to fetch data
  isActive = false,     // Whether the tab is active
  refreshInterval = 0,  // Auto-refresh interval in milliseconds (0 = disabled)
  onDataLoaded = null,  // Callback when data is loaded
  badge = null,         // Badge to display (number or component)
}) => {
  const location = useLocation();
  const [wasActive, setWasActive] = useState(false);

  // Use the background fetch hook
  const {
    data,
    loading,
    backgroundLoading,
    error,
    refreshData
  } = useBackgroundFetch(
    fetchFunction,
    [],  // No dependencies - we'll manually control when to fetch
    {
      initialLoading: false,
      initialData: null,
      initialError: null,
      refreshInterval: isActive ? refreshInterval : 0, // Only auto-refresh when active
    }
  );

  // Fetch data when the tab becomes active
  useEffect(() => {
    if (isActive && !wasActive) {
      console.log(`Tab ${label} became active, fetching data`);
      try {
        refreshData();
      } catch (error) {
        console.error(`Error refreshing data for tab ${label}:`, error);
      }
      setWasActive(true);
    } else if (!isActive) {
      setWasActive(false);
    }
  }, [isActive, wasActive, label, refreshData]);

  // Call the onDataLoaded callback when data is loaded
  useEffect(() => {
    if (data && onDataLoaded) {
      try {
        onDataLoaded(data);
      } catch (error) {
        console.error(`Error in onDataLoaded callback for tab ${label}:`, error);
      }
    }
  }, [data, onDataLoaded, label]);

  return (
    <Link
      to={to}
      className={`flex items-center px-4 py-3 text-sm transition-colors ${
        isActive
          ? 'bg-[#F3F0F9] text-[#412D6C] font-medium border-l-4 border-[#412D6C]'
          : 'text-gray-600 hover:bg-gray-100'
      }`}
    >
      <div className="relative flex items-center">
        {/* Icon */}
        <span className={`mr-3 ${isActive ? 'text-[#412D6C]' : 'text-gray-500'}`}>
          {icon}
        </span>

        {/* Label */}
        <span>{label}</span>

        {/* Loading indicator */}
        {(isActive && backgroundLoading) && (
          <span className="ml-2">
            <svg className="animate-spin h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
        )}

        {/* Badge */}
        {badge && (
          <span className="ml-auto bg-[#412D6C] text-white text-xs font-medium px-2 py-0.5 rounded-full">
            {badge}
          </span>
        )}
      </div>
    </Link>
  );
};

export default SidenavTab;
