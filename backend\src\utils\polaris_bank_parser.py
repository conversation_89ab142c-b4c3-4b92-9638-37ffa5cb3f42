"""
Polaris Bank Parser utility for the Bank Reconciliation API.
"""

import re
from typing import Dict, List, Any, Optional

def extract_polaris_bank_table(text: str) -> Optional[Dict[str, Any]]:
    """
    Extract Polaris Bank statement table with improved formatting.
    """
    lines = text.split('\n')
    lines = [line.strip() for line in lines if line.strip()]
    
    # Initialize result
    result = {
        'headers': ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'],
        'rows': []
    }
    
    # Find the start of the transaction table
    table_start_idx = -1
    for i, line in enumerate(lines):
        if ('Entry Date' in line and 'Value Date' in line) or ('ENTRY DATE' in line and 'VALUE DATE' in line):
            table_start_idx = i
            break
    
    if table_start_idx == -1:
        return None
    
    # Process transaction rows
    current_row = {}
    in_transaction = False
    
    for i in range(table_start_idx + 1, len(lines)):
        line = lines[i]
        
        # Check if this is a new transaction row
        date_pattern = r'\d{2}[/-]\d{2}[/-]\d{2,4}'
        if re.search(date_pattern, line) and ('NGN' in line or 'CR' in line or 'DR' in line):
            # Save previous row if exists
            if current_row and in_transaction:
                result['rows'].append(current_row)
            
            # Start new row
            parts = line.split()
            if len(parts) >= 6:
                entry_date = parts[0]
                value_date = parts[1]
                
                # Extract debit, credit, balance
                debit = ""
                credit = ""
                balance = ""
                
                # Find balance (usually last number with NGN)
                balance_idx = -1
                for j, part in enumerate(parts):
                    if 'NGN' in part and j > 2:
                        balance_idx = j
                        balance = part.replace('NGN', '').replace(',', '')
                
                # Find debit/credit
                for j, part in enumerate(parts):
                    if 'DR' in part and j > 2 and j < balance_idx:
                        debit = part.replace('DR', '').replace(',', '')
                    elif 'CR' in part and j > 2 and j < balance_idx:
                        credit = part.replace('CR', '').replace(',', '')
                
                # Extract details (everything between dates and amounts)
                details_start = 2
                details_end = balance_idx if balance_idx > 0 else -1
                details = ' '.join(parts[details_start:details_end])
                
                current_row = {
                    'EntryDate': entry_date,
                    'ValueDate': value_date,
                    'Details': details,
                    'Debit': debit,
                    'Credit': credit,
                    'Balance': balance
                }
                in_transaction = True
            
        # If this is a continuation of details for the current transaction
        elif in_transaction and not re.search(date_pattern, line):
            current_row['Details'] += ' ' + line
    
    # Add the last row
    if current_row and in_transaction:
        result['rows'].append(current_row)
    
    return result if result['rows'] else None

def extract_polaris_bank_info(text: str) -> Dict[str, Any]:
    """
    Extract bank information from Polaris Bank statement.
    """
    bank_info = {
        'bankName': 'Polaris Bank',
        'accountNumber': None,
        'accountName': None,
        'dateRange': None,
        'startBalance': None,
        'endBalance': None
    }
    
    lines = text.split('\n')
    lines = [line.strip() for line in lines if line.strip()]
    
    # Extract account number
    for line in lines:
        if 'Account Number' in line or 'ACCOUNT NUMBER' in line:
            parts = line.split(':')
            if len(parts) > 1:
                bank_info['accountNumber'] = parts[1].strip()
        
        # Extract account name
        if 'Account Name' in line or 'ACCOUNT NAME' in line:
            parts = line.split(':')
            if len(parts) > 1:
                bank_info['accountName'] = parts[1].strip()
        
        # Extract date range
        if 'Statement Period' in line or 'STATEMENT PERIOD' in line:
            parts = line.split(':')
            if len(parts) > 1:
                bank_info['dateRange'] = parts[1].strip()
        
        # Extract opening balance
        if 'Opening Balance' in line or 'OPENING BALANCE' in line:
            parts = line.split(':')
            if len(parts) > 1:
                balance_str = parts[1].strip().replace('NGN', '').replace(',', '')
                try:
                    bank_info['startBalance'] = float(balance_str)
                except ValueError:
                    pass
        
        # Extract closing balance
        if 'Closing Balance' in line or 'CLOSING BALANCE' in line:
            parts = line.split(':')
            if len(parts) > 1:
                balance_str = parts[1].strip().replace('NGN', '').replace(',', '')
                try:
                    bank_info['endBalance'] = float(balance_str)
                except ValueError:
                    pass
    
    return bank_info
