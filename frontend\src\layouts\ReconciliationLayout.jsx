import React, { useState } from 'react';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { FiHome, FiFileText, FiDollarSign, FiRefreshCw, FiMenu, FiX, FiLogOut, FiUser } from 'react-icons/fi';
import { motion } from 'framer-motion';
import useStore from '../store/useStore';
import { useUser } from '../contexts/UserContext';

const ReconciliationLayout = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();

  // Get user data from both state management systems
  const { user: contextUser, logout: contextLogout } = useUser();
  const storeUser = useStore((state) => state.user);
  const clearStoreUser = useStore((state) => state.clearUser);

  // Use either the context user or the store user, preferring the store user if available
  const user = storeUser || contextUser;

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const navItems = [
    { path: '/dashboard', label: 'Dashboard', icon: <FiHome size={20} /> },
    { path: '/statements', label: 'Statements', icon: <FiFileText size={20} /> },
    { path: '/transactions', label: 'Transactions', icon: <FiDollarSign size={20} /> },
    { path: '/reconciliations', label: 'Reconciliations', icon: <FiRefreshCw size={20} /> },
  ];

  const isActive = (path) => {
    return location.pathname.startsWith(path);
  };

  return (
    <div className="flex h-screen bg-gray-100 overflow-hidden">
      {/* Sidebar */}
      <motion.div
        className={`fixed inset-y-0 left-0 z-30 w-64 bg-indigo-800 text-white transform ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } transition-transform duration-300 ease-in-out md:relative md:translate-x-0`}
        initial={false}
        animate={{ width: isSidebarOpen ? 256 : 0 }}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-indigo-700">
          <h1 className="text-xl font-bold">Bank Reconciliation</h1>
          <button
            onClick={toggleSidebar}
            className="p-1 rounded-md md:hidden focus:outline-none focus:ring-2 focus:ring-white"
          >
            <FiX size={24} />
          </button>
        </div>

        <nav className="mt-5 px-2">
          <div className="space-y-1">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                  isActive(item.path)
                    ? 'bg-indigo-900 text-white'
                    : 'text-indigo-100 hover:bg-indigo-700'
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                {item.label}
              </Link>
            ))}
          </div>
        </nav>

        <div className="absolute bottom-0 w-full border-t border-indigo-700 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-indigo-600 flex items-center justify-center">
                <FiUser className="text-white" />
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-white">{user?.firstName ? `${user.firstName} ${user.lastName || ''}` : 'User'}</p>
              <button
                onClick={() => {
                  // Clear user from both state management systems
                  contextLogout && contextLogout();
                  clearStoreUser && clearStoreUser();

                  // Clear tokens from storage
                  localStorage.removeItem('authToken');
                  localStorage.removeItem('userId');
                  sessionStorage.removeItem('authToken');

                  // Clear cookies
                  document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                  // Redirect to login
                  navigate('/login');
                }}
                className="flex items-center text-xs font-medium text-indigo-200 hover:text-white"
              >
                <FiLogOut className="mr-1" />
                Sign out
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top navigation */}
        <header className="bg-white shadow-sm z-10 sticky top-0">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex">
                <button
                  onClick={toggleSidebar}
                  className="px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 md:hidden"
                >
                  <FiMenu size={24} />
                </button>
              </div>
              <div className="flex items-center">
                <span className="text-gray-700">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto bg-gray-50 p-4 sm:p-6 lg:p-8">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default ReconciliationLayout;
