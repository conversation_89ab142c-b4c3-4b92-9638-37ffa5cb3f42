{"name": "reconciliation", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.22.11", "@headlessui/react": "^2.2.0", "@tailwindcss/vite": "^4.0.4", "axios": "^1.8.1", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "framer-motion": "^12.5.0", "headlessui": "^0.0.0", "humanize-duration": "^3.32.1", "quill": "^2.0.3", "rc-progress": "^4.0.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-player": "^2.16.0", "react-router-dom": "^7.1.5", "react-simple-maps": "^3.0.0", "react-simple-star-rating": "^5.1.7", "react-toastify": "^11.0.5", "react-youtube": "^10.1.0", "reconciliation": "file:", "swiper": "^11.2.4", "uniqid": "^5.4.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^4.0.4", "vite": "^6.1.0"}}