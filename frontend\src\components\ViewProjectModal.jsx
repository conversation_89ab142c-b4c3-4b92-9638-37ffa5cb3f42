import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { FaPlus, FaMinus } from 'react-icons/fa';

const ViewProjectModal = ({ isOpen, onClose, project }) => {
  const [milestones, setMilestones] = useState(project?.paymentSchedule || []);

  const addMilestone = () => {
    setMilestones([...milestones, { date: '', amount: 0 }]);
  };

  const removeMilestone = (index) => {
    const updatedMilestones = milestones.filter((_, i) => i !== index);
    setMilestones(updatedMilestones);
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-3xl bg-white rounded-xl p-6">
          <Dialog.Title className="text-xl font-bold mb-4">Project Details</Dialog.Title>
          {project ? (
            <div className="space-y-4">
              <div>
                <strong>Project Name:</strong> {project.name}
              </div>
              <div>
                <strong>Client:</strong> {project.client}
              </div>
              <div>
                <strong>Start Date:</strong> {project.startDate}
              </div>
              <div>
                <strong>End Date:</strong> {project.endDate}
              </div>
              <div>
                <strong>Contract Value:</strong> ${project.contractValue.toLocaleString()}
              </div>
              <div>
                <strong>Payment Schedule:</strong>
                <ul>
                  {milestones.map((milestone, index) => (
                    <li key={index} className="flex justify-between items-center">
                      <span>{milestone.date}: ${milestone.amount}</span>
                      <div className="flex items-center">
                        <button onClick={() => removeMilestone(index)} className="text-red-600">
                          <FaMinus />
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
                <button onClick={addMilestone} className="text-teal-700 hover:underline">
                  <FaPlus /> Add Milestone
                </button>
              </div>
              <div>
                <strong>Team Members:</strong>
                <ul>
                  {project.teamMembers.map((member, index) => (
                    <li key={index}>{member}</li>
                  ))}
                </ul>
              </div>
            </div>
          ) : (
            <p>No project details available.</p>
          )}
          <div className="flex justify-end mt-4">
            <button
              className="px-4 py-2 bg-teal-700 text-white rounded-lg hover:bg-teal-800"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default ViewProjectModal; 