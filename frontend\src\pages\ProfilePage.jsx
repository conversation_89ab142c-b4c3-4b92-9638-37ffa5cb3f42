import React, { useState, useEffect } from 'react';
import { useUser } from '../contexts/UserContext';
import { FaUser, FaEnvelope, FaPhone, FaGraduationCap, FaBriefcase, FaMapMarkerAlt } from 'react-icons/fa';
import Footer from '../components/common/Footer';
import { createAuthenticatedAxiosInstance } from '../services/api';
import { showSuccessToast, showErrorToast } from '../utils/toast';

const ProfilePage = () => {
  const { user } = useUser();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    education: '',
    occupation: '',
    location: '',
    bio: ''
  });

  // Fetch user profile data from the backend
  const fetchUserProfile = async () => {
    if (!user || !user.id) return;

    setIsLoading(true);
    try {
      // Use the authenticated axios instance
      const api = createAuthenticatedAxiosInstance();
      const response = await api.get(`/user/profile/${user.id}`);

      if (response.data.status) {
        const userData = response.data.data;
        setProfileData({
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          email: userData.email || '',
          phone: userData.phone || '',
          education: userData.education || '',
          occupation: userData.occupation || '',
          location: userData.location || '',
          bio: userData.bio || ''
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      // If API fails, fallback to user context data
      if (user) {
        setProfileData({
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          email: user.email || '',
          phone: user.phone || '',
          education: user.education || '',
          occupation: user.occupation || '',
          location: user.location || '',
          bio: user.bio || ''
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUserProfile();
  }, [user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!user || !user.id) {
      showErrorToast('User information not available. Please log in again.');
      return;
    }

    setIsLoading(true);
    try {
      // Use the authenticated axios instance
      const api = createAuthenticatedAxiosInstance();
      const response = await api.put(`/user/profile/${user.id}`, profileData);

      if (response.data.status) {
        showSuccessToast('Profile updated successfully!');
        setIsEditing(false);
        // Refresh profile data
        fetchUserProfile();
      } else {
        showErrorToast(response.data.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      showErrorToast(error.response?.data?.meta?.error || 'An error occurred while updating your profile');
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Please log in to view your profile</h2>
          <button
            onClick={() => window.location.href = '/'}
            className="bg-[#412D6C] text-white px-6 py-2 rounded-full hover:bg-[#362659] transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gray-100">
        <div className="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <div className="bg-white shadow rounded-lg overflow-hidden">
            {/* Profile Header */}
            <div className="bg-gradient-to-r from-[#412D6C] to-[#6A4CA6] px-6 py-8">
              <div className="flex flex-col md:flex-row items-center">
                <div className="w-24 h-24 rounded-full bg-white text-[#412D6C] flex items-center justify-center text-4xl font-bold mb-4 md:mb-0 md:mr-6">
                  {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                </div>
                <div className="text-center md:text-left">
                  <h1 className="text-2xl font-bold text-white">{user.firstName} {user.lastName}</h1>
                  <p className="text-purple-200">{profileData.occupation || 'Student'}</p>
                </div>
                <div className="ml-auto mt-4 md:mt-0">
                  <button
                    onClick={() => setIsEditing(!isEditing)}
                    className="bg-white text-[#412D6C] px-4 py-2 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    {isEditing ? 'Cancel' : 'Edit Profile'}
                  </button>
                </div>
              </div>
            </div>

            {/* Profile Content */}
            <div className="p-6">
              {isEditing ? (
                <form onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">First Name</label>
                      <input
                        type="text"
                        name="firstName"
                        value={profileData.firstName}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Last Name</label>
                      <input
                        type="text"
                        name="lastName"
                        value={profileData.lastName}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Email</label>
                      <input
                        type="email"
                        name="email"
                        value={profileData.email}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Phone</label>
                      <input
                        type="tel"
                        name="phone"
                        value={profileData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Education</label>
                      <input
                        type="text"
                        name="education"
                        value={profileData.education}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Occupation</label>
                      <input
                        type="text"
                        name="occupation"
                        value={profileData.occupation}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Location</label>
                      <input
                        type="text"
                        name="location"
                        value={profileData.location}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                      />
                    </div>
                  </div>
                  <div className="mb-6">
                    <label className="block text-gray-700 font-medium mb-2">Bio</label>
                    <textarea
                      name="bio"
                      value={profileData.bio}
                      onChange={handleChange}
                      rows="4"
                      className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
                    ></textarea>
                  </div>
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      className="bg-[#412D6C] text-white px-6 py-2 rounded-md hover:bg-[#362659] transition-colors"
                    >
                      Save Changes
                    </button>
                  </div>
                </form>
              ) : (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="flex items-center">
                      <FaUser className="text-[#412D6C] mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Full Name</p>
                        <p className="font-medium">{profileData.firstName} {profileData.lastName}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <FaEnvelope className="text-[#412D6C] mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="font-medium">{profileData.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <FaPhone className="text-[#412D6C] mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p className="font-medium">{profileData.phone || 'Not provided'}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <FaGraduationCap className="text-[#412D6C] mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Education</p>
                        <p className="font-medium">{profileData.education || 'Not provided'}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <FaBriefcase className="text-[#412D6C] mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Occupation</p>
                        <p className="font-medium">{profileData.occupation || 'Not provided'}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <FaMapMarkerAlt className="text-[#412D6C] mr-3" />
                      <div>
                        <p className="text-sm text-gray-500">Location</p>
                        <p className="font-medium">{profileData.location || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>
                  {profileData.bio && (
                    <div className="mt-6">
                      <h3 className="text-lg font-medium text-gray-800 mb-2">About Me</h3>
                      <p className="text-gray-700">{profileData.bio}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default ProfilePage;
