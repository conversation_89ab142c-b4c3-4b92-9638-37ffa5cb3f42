/**
 * Type definitions for the statement data engine
 */

export interface FileMetadata {
  fileName: string;
  fileSize: number;
  fileType: string;
  lastModified?: Date;
  mimeType?: string;
  pageCount?: number;
  author?: string;
  creationDate?: Date;
  keywords?: string[];
  title?: string;
  subject?: string;
}

export interface FileSummary {
  rowCount?: number;
  columnCount?: number;
  sheetCount?: number;
  startDate?: string | Date;
  endDate?: string | Date;
  startBalance?: number;
  endBalance?: number;
  totalCredits?: number;
  totalDebits?: number;
  transactionCount?: number;
  dataTypes?: Record<string, string | Record<string, string>>;
  statistics?: Record<string, any>;
}

export interface FileContent {
  sheets?: string[];
  data?: Record<string, any[]>;
  rows?: any[];
  tables?: any[];
  bankInfo?: {
    bankName?: string;
    accountNumber?: string;
    dateRange?: string;
  };
  rawPreview?: any[];
  dimensions?: Record<string, { rows: number; columns: number }>;
  pageCount?: number;
}

export interface FilePreviewData {
  metadata: FileMetadata;
  content: FileContent;
  preview: string;
  summary?: FileSummary;
  insights?: any;
}

export interface StandardizedTransaction {
  'Financial Date': string;
  'Transaction Date': string;
  'Reference No.': string;
  'Instrument No.': string;
  'Narration': string;
  'Username': string;
  'DR': number;
  'CR': number;
  'Avail. Bal': number;
  'Entry Code': string;
  _original: any;
}

export interface FormattedStatementData {
  bankName?: string;
  accountNumber?: string;
  content: StandardizedTransaction[];
  summary: {
    startDate?: string | Date;
    endDate?: string | Date;
    startBalance?: number;
    endBalance?: number;
    totalCredits?: number;
    totalDebits?: number;
    transactionCount?: number;
  };
  rawData: any;
}

export interface EngineResult {
  status: boolean;
  data?: FormattedStatementData;
  error?: string;
  processingTime?: number;
}
