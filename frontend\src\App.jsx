import React from "react";
import { Routes, Route, useMatch, Navigate } from "react-router-dom";
import { UserProvider } from "./contexts/UserContext.jsx";
import { StatementDataProvider } from "./contexts/StatementDataContext.jsx";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import UserDataSync from './components/common/UserDataSync';
// Import the test connection utility
import './utils/testConnection';

// Auth pages
import Login from "./pages/auth/Login";
import Signup from "./pages/auth/Signup";
import ForgotPassword from "./pages/auth/ForgotPassword";

// Core components
import Navbar from "./components/common/Navbar";
import ProfilePage from "./pages/ProfilePage";
import SettingsPage from "./pages/SettingsPage";

// Reconciliation pages
import ReconciliationLayout from "./layouts/ReconciliationLayout";
import DashboardPage from "./pages/reconciliation/DashboardPage";
import StatementsPage from "./pages/reconciliation/StatementsPage";
import StatementUploadPage from "./pages/reconciliation/StatementUploadPage";
import StatementDetailPage from "./pages/reconciliation/StatementDetailPage";
import ReconciliationsPage from "./pages/reconciliation/ReconciliationsPage";
import ReconciliationDetailPage from "./pages/reconciliation/ReconciliationDetailPage";
import NewReconciliationPage from "./pages/reconciliation/NewReconciliationPage";
import TransactionsPage from "./pages/reconciliation/TransactionsPage";
import FilePreviewPage from "./pages/FilePreviewPage";

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  // Check for token in multiple places
  const localStorageToken = localStorage.getItem('authToken');
  const backupToken = localStorage.getItem('auth_token_backup');
  const sessionStorageToken = sessionStorage.getItem('authToken');

  // Get token from cookie if available
  const getCookieToken = () => {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith('authToken=')) {
        return cookie.substring('authToken='.length, cookie.length);
      }
    }
    return null;
  };

  const cookieToken = getCookieToken();

  // Use any available token
  const isAuthenticated = !!(localStorageToken || backupToken || sessionStorageToken || cookieToken);

  // If we have a token in backup but not in primary, restore it
  if (!localStorageToken && (backupToken || sessionStorageToken || cookieToken)) {
    const token = backupToken || sessionStorageToken || cookieToken;
    console.log('Restoring token from backup source');
    localStorage.setItem('authToken', token);
  }

  // If no token is found, redirect to login
  if (!isAuthenticated) {
    console.log('No auth token found in any storage, redirecting to login');
    return <Navigate to="/login?session=expired" replace />;
  }

  return children;
};

function App() {
  const isInstructor = useMatch("/instructor/*");

  return (
    <UserProvider>
      {/* UserDataSync component to sync user data between context and store */}
      <UserDataSync />

      {/* StatementDataProvider to cache statement preview data */}
      <StatementDataProvider>
        <div className="text-default min-h-screen bg-white flex flex-col">
          {/* Toast notifications container */}
          <ToastContainer position="top-right" autoClose={5000} hideProgressBar={false} newestOnTop closeOnClick rtl={false} pauseOnFocusLoss draggable pauseOnHover />

        <Routes>
          {/* AUTH ROUTES */}
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />

          {/* RECONCILIATION ROUTES */}
          <Route path="/" element={
            <ProtectedRoute>
              <Navigate to="/dashboard" replace />
            </ProtectedRoute>
          } />

          {/* Main reconciliation layout with sidebar */}
          <Route element={
            <ProtectedRoute>
              <ReconciliationLayout />
            </ProtectedRoute>
          }>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/statements" element={<StatementsPage />} />
            <Route path="/statements/upload" element={<StatementUploadPage />} />
            <Route path="/statements/:id" element={<StatementDetailPage />} />
            <Route path="/statements/:id/preview" element={<FilePreviewPage />} />
            <Route path="/transactions" element={<TransactionsPage />} />
            <Route path="/reconciliations" element={<ReconciliationsPage />} />
            <Route path="/reconciliations/new" element={<NewReconciliationPage />} />
            <Route path="/reconciliations/:id" element={<ReconciliationDetailPage />} />
          </Route>

          {/* User Profile and Settings Routes */}
          <Route path="/profile" element={
            <ProtectedRoute>
              <ProfilePage />
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <SettingsPage />
            </ProtectedRoute>
          } />

          {/* Unauthorized Route */}
          <Route path="/unauthorized" element={
            <div className="min-h-screen flex items-center justify-center">
              <div className="text-center p-6 max-w-md">
                <div className="text-red-500 text-5xl mb-4">⚠️</div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">Access Denied</h2>
                <p className="text-gray-600 mb-4">You do not have permission to access this page.</p>
                <button
                  onClick={() => window.history.back()}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Go Back
                </button>
              </div>
            </div>
          } />

          {/* Catch-all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
        </div>
      </StatementDataProvider>
    </UserProvider>
  );
}

export default App;