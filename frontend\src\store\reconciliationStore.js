import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import api from '../services/api';

// Create a store for reconciliation state
const useReconciliationStore = create(
  persist(
    (set, get) => ({
      // Statements
      statements: [],
      currentStatement: null,
      statementsLoading: false,
      statementsError: null,

      // Transactions
      transactions: [],
      currentTransaction: null,
      transactionsLoading: false,
      transactionsError: null,

      // Reconciliations
      reconciliations: [],
      currentReconciliation: null,
      reconciliationsLoading: false,
      reconciliationsError: null,

      // Statement actions
      fetchStatements: async () => {
        set({ statementsLoading: true, statementsError: null });
        try {
          const response = await api.statement.getAllStatements();
          // Make sure we're setting the data array from the response
          set({
            statements: response && response.data ? response.data : [],
            statementsLoading: false
          });
        } catch (error) {
          console.error('Error fetching statements:', error);
          set({
            statementsError: error.message || 'Failed to fetch statements',
            statementsLoading: false,
            statements: [] // Reset to empty array on error
          });
        }
      },

      fetchStatementById: async (id) => {
        set({ statementsLoading: true, statementsError: null });
        try {
          const response = await api.statement.getStatementById(id);
          set({
            currentStatement: response && response.data ? response.data : null,
            statementsLoading: false
          });
          return response && response.data ? response.data : null;
        } catch (error) {
          console.error(`Error fetching statement ${id}:`, error);
          set({
            statementsError: error.message || `Failed to fetch statement ${id}`,
            statementsLoading: false,
            currentStatement: null // Reset on error
          });
          throw error;
        }
      },

      uploadStatement: async (formData) => {
        set({ statementsLoading: true, statementsError: null });
        try {
          const response = await api.statement.uploadStatement(formData);
          // Refresh statements list after upload
          await get().fetchStatements();
          set({ statementsLoading: false });
          return response;
        } catch (error) {
          set({
            statementsError: error.message || 'Failed to upload statement',
            statementsLoading: false
          });
          throw error;
        }
      },

      deleteStatement: async (id) => {
        set({ statementsLoading: true, statementsError: null });
        try {
          const response = await api.statement.deleteStatement(id);
          // Refresh statements list after deletion
          await get().fetchStatements();
          set({ statementsLoading: false });
          return response;
        } catch (error) {
          set({
            statementsError: error.message || `Failed to delete statement ${id}`,
            statementsLoading: false
          });
          throw error;
        }
      },

      // Transaction actions
      fetchTransactions: async () => {
        set({ transactionsLoading: true, transactionsError: null });
        try {
          const response = await api.transaction.getAllTransactions();
          set({ transactions: response.data || [], transactionsLoading: false });
        } catch (error) {
          set({
            transactionsError: error.message || 'Failed to fetch transactions',
            transactionsLoading: false
          });
        }
      },

      fetchTransactionsByStatement: async (statementId) => {
        set({ transactionsLoading: true, transactionsError: null });
        try {
          const response = await api.transaction.getTransactionsByStatement(statementId);
          set({
            transactions: response && response.data ? response.data : [],
            transactionsLoading: false
          });
          return response && response.data ? response.data : [];
        } catch (error) {
          console.error(`Error fetching transactions for statement ${statementId}:`, error);
          set({
            transactionsError: error.message || `Failed to fetch transactions for statement ${statementId}`,
            transactionsLoading: false,
            transactions: [] // Reset to empty array on error
          });
          throw error;
        }
      },

      createTransaction: async (transactionData) => {
        set({ transactionsLoading: true, transactionsError: null });
        try {
          const response = await api.transaction.createTransaction(transactionData);
          // Refresh transactions list after creation
          await get().fetchTransactions();
          set({ transactionsLoading: false });
          return response;
        } catch (error) {
          set({
            transactionsError: error.message || 'Failed to create transaction',
            transactionsLoading: false
          });
          throw error;
        }
      },

      updateTransaction: async (id, transactionData) => {
        set({ transactionsLoading: true, transactionsError: null });
        try {
          const response = await api.transaction.updateTransaction(id, transactionData);
          // Refresh transactions list after update
          await get().fetchTransactions();
          set({ transactionsLoading: false });
          return response;
        } catch (error) {
          set({
            transactionsError: error.message || `Failed to update transaction ${id}`,
            transactionsLoading: false
          });
          throw error;
        }
      },

      deleteTransaction: async (id) => {
        set({ transactionsLoading: true, transactionsError: null });
        try {
          const response = await api.transaction.deleteTransaction(id);
          // Refresh transactions list after deletion
          await get().fetchTransactions();
          set({ transactionsLoading: false });
          return response;
        } catch (error) {
          set({
            transactionsError: error.message || `Failed to delete transaction ${id}`,
            transactionsLoading: false
          });
          throw error;
        }
      },

      matchTransactions: async (matchData) => {
        set({ transactionsLoading: true, transactionsError: null });
        try {
          const response = await api.transaction.matchTransactions(matchData);
          // Refresh transactions list after matching
          await get().fetchTransactions();
          set({ transactionsLoading: false });
          return response;
        } catch (error) {
          set({
            transactionsError: error.message || 'Failed to match transactions',
            transactionsLoading: false
          });
          throw error;
        }
      },

      unmatchTransaction: async (id) => {
        set({ transactionsLoading: true, transactionsError: null });
        try {
          const response = await api.transaction.unmatchTransaction(id);
          // Refresh transactions list after unmatching
          await get().fetchTransactions();
          set({ transactionsLoading: false });
          return response;
        } catch (error) {
          set({
            transactionsError: error.message || `Failed to unmatch transaction ${id}`,
            transactionsLoading: false
          });
          throw error;
        }
      },

      // Reconciliation actions
      fetchReconciliations: async () => {
        set({ reconciliationsLoading: true, reconciliationsError: null });
        try {
          const response = await api.reconciliation.getAllReconciliations();
          set({ reconciliations: response.data || [], reconciliationsLoading: false });
        } catch (error) {
          set({
            reconciliationsError: error.message || 'Failed to fetch reconciliations',
            reconciliationsLoading: false
          });
        }
      },

      fetchReconciliationById: async (id) => {
        set({ reconciliationsLoading: true, reconciliationsError: null });
        try {
          const response = await api.reconciliation.getReconciliationById(id);
          set({
            currentReconciliation: response && response.data ? response.data : null,
            reconciliationsLoading: false
          });
          return response && response.data ? response.data : null;
        } catch (error) {
          console.error(`Error fetching reconciliation ${id}:`, error);
          set({
            reconciliationsError: error.message || `Failed to fetch reconciliation ${id}`,
            reconciliationsLoading: false,
            currentReconciliation: null // Reset on error
          });
          throw error;
        }
      },

      createReconciliation: async (reconciliationData) => {
        set({ reconciliationsLoading: true, reconciliationsError: null });
        try {
          const response = await api.reconciliation.createReconciliation(reconciliationData);
          // Refresh reconciliations list after creation
          await get().fetchReconciliations();
          set({ reconciliationsLoading: false });
          return response;
        } catch (error) {
          set({
            reconciliationsError: error.message || 'Failed to create reconciliation',
            reconciliationsLoading: false
          });
          throw error;
        }
      },

      fetchReconciliationTransactions: async (id) => {
        set({ transactionsLoading: true, transactionsError: null });
        try {
          const response = await api.reconciliation.getReconciliationTransactions(id);
          set({
            transactions: response && response.data ? response.data : [],
            transactionsLoading: false
          });
          return response && response.data ? response.data : [];
        } catch (error) {
          console.error(`Error fetching transactions for reconciliation ${id}:`, error);
          set({
            transactionsError: error.message || `Failed to fetch transactions for reconciliation ${id}`,
            transactionsLoading: false,
            transactions: [] // Reset to empty array on error
          });
          throw error;
        }
      },

      completeReconciliation: async (id) => {
        set({ reconciliationsLoading: true, reconciliationsError: null });
        try {
          const response = await api.reconciliation.completeReconciliation(id);
          // Refresh reconciliations list after completion
          await get().fetchReconciliations();
          set({ reconciliationsLoading: false });
          return response;
        } catch (error) {
          set({
            reconciliationsError: error.message || `Failed to complete reconciliation ${id}`,
            reconciliationsLoading: false
          });
          throw error;
        }
      },

      // Reset state
      resetReconciliationState: () => {
        set({
          statements: [],
          currentStatement: null,
          statementsLoading: false,
          statementsError: null,
          transactions: [],
          currentTransaction: null,
          transactionsLoading: false,
          transactionsError: null,
          reconciliations: [],
          currentReconciliation: null,
          reconciliationsLoading: false,
          reconciliationsError: null,
        });
      },
    }),
    {
      name: 'reconciliation-storage', // name for localStorage
      partialize: (state) => ({
        // Only persist these fields
        statements: state.statements,
        currentStatement: state.currentStatement,
        reconciliations: state.reconciliations,
        currentReconciliation: state.currentReconciliation,
      }),
    }
  )
);

export default useReconciliationStore;
