import React from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const ChartCard = ({ title, type, data, options }) => {
  const ChartComponent = {
    line: Line,
    bar: Bar,
    doughnut: Doughnut,
  }[type];

  return (
    <div className="bg-white/30 backdrop-blur-xl p-6 rounded-xl shadow-lg border border-white/20">
      <h3 className="text-[#412D6C] font-semibold mb-4">{title}</h3>
      <div className="h-[300px]">
        <ChartComponent data={data} options={options} />
      </div>
    </div>
  );
};

export default ChartCard;
