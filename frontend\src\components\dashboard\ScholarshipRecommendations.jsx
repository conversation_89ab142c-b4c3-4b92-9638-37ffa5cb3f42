import React from 'react';

const ScholarshipRecommendations = ({ recommendations, onApply }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
      <h3 className="text-lg font-medium text-gray-800 mb-4">Recommended For You</h3>
      
      <div className="space-y-4">
        {recommendations.map((scholarship, index) => (
          <div 
            key={index}
            className="p-4 rounded-lg border border-[#412D6C]/20 bg-[#412D6C]/5 hover:bg-[#412D6C]/10 transition-colors"
          >
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium text-[#412D6C]">{scholarship.title}</h4>
                <p className="mt-1 text-sm text-gray-600">{scholarship.description}</p>
              </div>
              <div className="flex flex-col items-end">
                <span className="text-sm font-medium text-green-600">{scholarship.match}% Match</span>
                <span className="text-xs text-gray-500 mt-1">Deadline: {scholarship.deadline}</span>
              </div>
            </div>
            
            <div className="mt-3 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                <span className="font-medium">Amount:</span> {scholarship.amount}
              </div>
              <button 
                onClick={() => onApply(scholarship)}
                className="px-3 py-1 text-sm bg-[#412D6C] text-white rounded hover:bg-[#362659] transition-colors"
              >
                Apply Now
              </button>
            </div>
            
            {scholarship.requirements && (
              <div className="mt-2 text-xs text-gray-500">
                <span className="font-medium">Requirements:</span> {scholarship.requirements.join(', ')}
              </div>
            )}
            
            {scholarship.tags && (
              <div className="mt-3 flex flex-wrap gap-2">
                {scholarship.tags.map((tag, tagIndex) => (
                  <span 
                    key={tagIndex}
                    className="px-2 py-1 text-xs rounded-full bg-[#412D6C]/10 text-[#412D6C]"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
      
      {recommendations.length === 0 && (
        <div className="text-center py-6 text-gray-500">
          <p>No scholarship recommendations available at this time.</p>
        </div>
      )}
    </div>
  );
};

export default ScholarshipRecommendations;
