/**
 * Lesson type definitions and utility functions
 */

// Lesson types
export const LESSON_TYPES = {
  TEXT: 'text',
  VIDEO: 'video',
  AUDIO: 'audio',
  QUIZ: 'quiz',
  ASSIGNMENT: 'assignment'
};

// Icons for lesson types
export const LESSON_TYPE_ICONS = {
  [LESSON_TYPES.TEXT]: '📝',
  [LESSON_TYPES.VIDEO]: '🎬',
  [LESSON_TYPES.AUDIO]: '🎧',
  [LESSON_TYPES.QUIZ]: '❓',
  [LESSON_TYPES.ASSIGNMENT]: '📋'
};

// Human-readable names for lesson types
export const LESSON_TYPE_NAMES = {
  [LESSON_TYPES.TEXT]: 'Text Lesson',
  [LESSON_TYPES.VIDEO]: 'Video Lesson',
  [LESSON_TYPES.AUDIO]: 'Audio Lesson',
  [LESSON_TYPES.QUIZ]: 'Quiz',
  [LESSON_TYPES.ASSIGNMENT]: 'Assignment'
};

/**
 * Create a new empty lesson of the specified type
 * @param {number} id - Lesson ID
 * @param {string} type - Lesson type from LESSON_TYPES
 * @returns {Object} - New lesson object
 */
export const createEmptyLesson = (id, type = LESSON_TYPES.TEXT) => {
  const baseLesson = {
    id,
    title: `New ${LESSON_TYPE_NAMES[type]}`,
    duration: '0 min',
    type
  };

  // Add type-specific properties
  switch (type) {
    case LESSON_TYPES.TEXT:
      return {
        ...baseLesson,
        content: ''
      };
    case LESSON_TYPES.VIDEO:
      return {
        ...baseLesson,
        videoUrl: '',
        transcript: ''
      };
    case LESSON_TYPES.AUDIO:
      return {
        ...baseLesson,
        audioUrl: '',
        transcript: ''
      };
    case LESSON_TYPES.QUIZ:
      return {
        ...baseLesson,
        questions: [],
        passingScore: 70,
        timeLimit: 0, // 0 means no time limit
        allowRetake: true,
        randomizeQuestions: false
      };
    case LESSON_TYPES.ASSIGNMENT:
      return {
        ...baseLesson,
        instructions: '',
        dueDate: '',
        totalPoints: 100,
        submissionType: 'text', // text, file, or both
        allowLateSubmissions: true
      };
    default:
      return baseLesson;
  }
};

/**
 * Validate a lesson object
 * @param {Object} lesson - Lesson to validate
 * @returns {Object} - { isValid: boolean, errors: string[] }
 */
export const validateLesson = (lesson) => {
  const errors = [];
  
  // Check common fields
  if (!lesson.title || lesson.title.trim() === '') {
    errors.push('Lesson title is required');
  }
  
  // Check type-specific fields
  switch (lesson.type) {
    case LESSON_TYPES.VIDEO:
      if (!lesson.videoUrl || lesson.videoUrl.trim() === '') {
        errors.push('Video URL is required');
      }
      break;
    case LESSON_TYPES.AUDIO:
      if (!lesson.audioUrl || lesson.audioUrl.trim() === '') {
        errors.push('Audio URL is required');
      }
      break;
    case LESSON_TYPES.QUIZ:
      if (!lesson.questions || lesson.questions.length === 0) {
        errors.push('Quiz must have at least one question');
      } else {
        // Check each question
        lesson.questions.forEach((question, index) => {
          if (!question.question || question.question.trim() === '') {
            errors.push(`Question ${index + 1} text is required`);
          }
          if (!question.options || question.options.length < 2) {
            errors.push(`Question ${index + 1} must have at least 2 options`);
          }
        });
      }
      break;
    case LESSON_TYPES.ASSIGNMENT:
      if (!lesson.instructions || lesson.instructions.trim() === '') {
        errors.push('Assignment instructions are required');
      }
      break;
    default:
      break;
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
