import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import User, { IUser } from '../models/user.model';
import config from '../config';

/**
 * Generate JWT token
 */
const generateToken = (user: IUser): string => {
  return jwt.sign(
    { sub: user._id, email: user.email, role: user.role },
    config.jwtSecret,
    { expiresIn: config.jwtExpiration }
  );
};

/**
 * Register a new user
 * @route POST /api/auth/register
 */
export const register = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password, firstName, lastName } = req.body;

    // Validate required fields
    if (!email || !password || !firstName || !lastName) {
      res.status(400).json({
        message: 'All fields are required',
        errors: {
          email: !email ? 'Email is required' : null,
          password: !password ? 'Password is required' : null,
          firstName: !firstName ? 'First name is required' : null,
          lastName: !lastName ? 'Last name is required' : null
        }
      });
      return;
    }

    console.log('Registration attempt with data:', { email, firstName, lastName });

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      res.status(400).json({ message: 'User already exists' });
      return;
    }

    // Create new user
    const user = new User({
      email,
      password,
      firstName,
      lastName,
    });

    // Save user to database
    try {
      await user.save();
    } catch (validationError: any) {
      // Handle validation errors
      if (validationError.name === 'ValidationError') {
        const errors: Record<string, string> = {};

        // Extract validation error messages
        for (const field in validationError.errors) {
          errors[field] = validationError.errors[field].message;
        }

        res.status(400).json({
          message: 'Validation failed',
          errors
        });
        return;
      }

      // Re-throw other errors to be caught by the outer catch block
      throw validationError;
    }

    // Generate JWT token
    const token = generateToken(user);

    // Return user data and token
    res.status(201).json({
      status: true,
      message: 'User registered successfully',
      data: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
      token,
    });
  } catch (error) {
    console.error('Error registering user:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Login user
 * @route POST /api/auth/login
 */
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      res.status(400).json({ message: 'Email and password are required' });
      return;
    }

    console.log(`Login attempt for email: ${email}`);

    // Check if user exists
    const user = await User.findOne({ email });
    if (!user) {
      console.log(`User not found for email: ${email}`);
      res.status(401).json({ message: 'Invalid credentials' });
      return;
    }

    console.log(`User found for email: ${email}, checking password`);

    // Check if password is correct
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      console.log(`Invalid password for email: ${email}`);
      res.status(401).json({ message: 'Invalid credentials' });
      return;
    }

    console.log(`Password match successful for email: ${email}`);

    // Generate JWT token
    const token = generateToken(user);

    // Return user data and token
    res.status(200).json({
      status: true,
      message: 'Login successful',
      data: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
      token,
    });
  } catch (error) {
    console.error('Error logging in:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
