import React from 'react';

/**
 * PopupOverlay component
 *
 * A reusable component for creating popups with a blurred background.
 * This can be used for all popups in the application to maintain consistency.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Content to display inside the popup
 * @param {Function} props.onClose - Function to call when the overlay is clicked
 * @param {boolean} props.closeOnOverlayClick - Whether to close the popup when the overlay is clicked (default: true)
 * @param {string} props.className - Additional classes for the popup container
 */
const PopupOverlay = ({
  children,
  onClose,
  closeOnOverlayClick = true,
  className = ''
}) => {
  // Handle overlay click
  const handleOverlayClick = (e) => {
    // Only close if clicking the overlay itself, not its children
    if (e.target === e.currentTarget && closeOnOverlayClick && onClose) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-md bg-black/40"
      onClick={handleOverlayClick}
    >
      <div
        className={`bg-white rounded-lg shadow-2xl p-6 max-w-md w-full m-4 ${className}`}
        onClick={(e) => e.stopPropagation()} // Prevent clicks inside the popup from closing it
      >
        {children}
      </div>
    </div>
  );
};

export default PopupOverlay;
