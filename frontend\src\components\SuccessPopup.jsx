import React from 'react';
import { Dialog } from '@headlessui/react';

const SuccessPopup = ({ isOpen, message, onClose }) => {
  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-md bg-white rounded-xl p-6">
          <Dialog.Title className="text-xl font-bold mb-4">Success</Dialog.Title>
          <p className="mb-4">{message}</p>
          <div className="flex justify-end">
            <button
              className="px-4 py-2 bg-teal-700 text-white rounded-lg hover:bg-teal-800"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default SuccessPopup; 