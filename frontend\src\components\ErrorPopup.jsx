import React from 'react';
import { Dialog } from '@headlessui/react';

const ErrorPopup = ({ isOpen, message, metaError, suggestions, onClose }) => {
  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-md bg-white rounded-xl p-6">
          <Dialog.Title className="text-xl font-bold mb-4">Error</Dialog.Title>
          <p className="mb-2">{message}</p>
          {metaError && <p className="mb-2 text-red-600">{metaError}</p>}
          {suggestions && suggestions.length > 0 && (
            <div>
              <h3 className="font-semibold">Suggestions:</h3>
              <ul className="list-disc list-inside">
                {suggestions.map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>
            </div>
          )}
          <div className="flex justify-end">
            <button
              className="px-4 py-2 bg-teal-700 text-white rounded-lg hover:bg-teal-800"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default ErrorPopup; 