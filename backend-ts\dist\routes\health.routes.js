"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const health_controller_1 = require("../controllers/health.controller");
const router = express_1.default.Router();
// @route   GET /api/health
// @desc    Health check endpoint
// @access  Public
router.get('/', health_controller_1.healthCheck);
exports.default = router;
