import express from 'express';
import {
  uploadFile,
  previewFile,
  downloadFile,
  analyzeFile
} from '../controllers/file.controller';

const router = express.Router();

// @route   POST /api/files/upload
// @desc    Upload a file
// @access  Private
router.post('/upload', uploadFile);

// @route   GET /api/files/preview/:filePath
// @desc    Preview file content
// @access  Private
router.get('/preview/:filePath', previewFile);

// @route   GET /api/files/download/:filePath
// @desc    Download file
// @access  Private
router.get('/download/:filePath', downloadFile);

// @route   POST /api/files/analyze
// @desc    Analyze file content and extract insights
// @access  Private
router.post('/analyze', analyzeFile);

export default router;
