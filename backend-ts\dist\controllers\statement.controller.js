"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStatementPreview = exports.downloadStatementFile = exports.parseStatementFile = exports.uploadStatement = exports.deleteStatement = exports.getStatementById = exports.getAllStatements = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const statement_model_1 = __importDefault(require("../models/statement.model"));
const transaction_model_1 = __importDefault(require("../models/transaction.model"));
const statementData_model_1 = __importDefault(require("../models/statementData.model"));
const statementData_service_1 = require("../services/statementData.service");
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const os_1 = __importDefault(require("os"));
// Create uploads directory if it doesn't exist
const uploadsDir = path_1.default.join(process.cwd(), 'uploads');
try {
    if (!fs_1.default.existsSync(uploadsDir)) {
        fs_1.default.mkdirSync(uploadsDir, { recursive: true });
        console.log('Created uploads directory:', uploadsDir);
    }
}
catch (err) {
    console.error('Error creating uploads directory:', err);
}
// Configure multer for file uploads - use OS temp directory as fallback
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        // Try to use our uploads directory first
        if (fs_1.default.existsSync(uploadsDir) && fs_1.default.accessSync(uploadsDir, fs_1.default.constants.W_OK)) {
            cb(null, uploadsDir);
        }
        else {
            // Fallback to OS temp directory
            const tempDir = os_1.default.tmpdir();
            console.log('Using OS temp directory for uploads:', tempDir);
            cb(null, tempDir);
        }
    },
    filename: (req, file, cb) => {
        cb(null, `${Date.now()}-${file.originalname}`);
    }
});
const upload = (0, multer_1.default)({
    storage,
    fileFilter: (req, file, cb) => {
        const allowedTypes = [
            'application/pdf',
            'text/csv',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];
        const allowedExtensions = ['.pdf', '.csv', '.xlsx', '.xls'];
        const ext = path_1.default.extname(file.originalname).toLowerCase();
        if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(ext)) {
            cb(null, true);
        }
        else {
            cb(new Error('Invalid file type. Only PDF, CSV, and Excel files are allowed.'));
        }
    }
}).single('file');
/**
 * Get all statements
 * @route GET /api/statements
 */
const getAllStatements = async (req, res) => {
    try {
        // Get user ID from authenticated user
        const userId = req.user.sub;
        // Find all statements for this user
        const statements = await statement_model_1.default.find({ userId }).sort({ createdAt: -1 });
        res.status(200).json({
            status: true,
            count: statements.length,
            data: statements,
        });
    }
    catch (error) {
        console.error('Error getting statements:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getAllStatements = getAllStatements;
/**
 * Get statement by ID
 * @route GET /api/statements/:id
 */
const getStatementById = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid statement ID' });
            return;
        }
        // Find statement by ID and user ID
        const statement = await statement_model_1.default.findOne({
            _id: id,
            userId,
        });
        if (!statement) {
            res.status(404).json({ message: 'Statement not found' });
            return;
        }
        // Get transaction count for this statement
        const transactionCount = await transaction_model_1.default.countDocuments({
            statementId: id,
            userId,
        });
        res.status(200).json({
            status: true,
            data: {
                ...statement.toObject(),
                transactionCount,
            },
        });
    }
    catch (error) {
        console.error('Error getting statement:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getStatementById = getStatementById;
/**
 * Delete statement
 * @route DELETE /api/statements/:id
 */
const deleteStatement = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid statement ID' });
            return;
        }
        // Find and delete statement
        const statement = await statement_model_1.default.findOneAndDelete({
            _id: id,
            userId,
        });
        if (!statement) {
            res.status(404).json({ message: 'Statement not found' });
            return;
        }
        // Delete all transactions associated with this statement
        await transaction_model_1.default.deleteMany({
            statementId: id,
            userId,
        });
        // Delete statement data if it exists
        await statementData_model_1.default.deleteOne({
            statementId: id,
            userId,
        });
        res.status(200).json({
            status: true,
            message: 'Statement and associated data deleted successfully',
        });
    }
    catch (error) {
        console.error('Error deleting statement:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.deleteStatement = deleteStatement;
/**
 * Upload statement
 * @route POST /api/statements/upload
 */
const uploadStatement = async (req, res) => {
    // Use multer middleware to handle file upload
    upload(req, res, async (err) => {
        if (err) {
            return res.status(400).json({ message: err.message });
        }
        try {
            if (!req.file) {
                return res.status(400).json({ message: 'No file uploaded' });
            }
            const { name, statement_type, statement_date } = req.body;
            if (!name) {
                return res.status(400).json({ message: 'Statement name is required' });
            }
            const userId = req.user.sub;
            // Create new statement with basic info
            const statement = new statement_model_1.default({
                name,
                statement_type: statement_type || 'bank',
                statement_date: statement_date ? new Date(statement_date) : new Date(),
                fileName: req.file.filename,
                filePath: req.file.path,
                userId,
                // Set default values for required fields in the schema
                bankName: 'Pending Processing',
                accountNumber: 'Pending Processing',
                startDate: new Date(),
                endDate: new Date(),
                startBalance: 0,
                endBalance: 0
            });
            // Save statement to database
            await statement.save();
            // Start processing the statement data in the background
            (0, statementData_service_1.processAndStoreStatementData)(statement._id.toString(), userId)
                .then(() => {
                console.log(`Statement data processing completed for statement ${statement._id}`);
            })
                .catch(err => {
                console.error(`Error processing statement data for statement ${statement._id}:`, err);
            });
            res.status(201).json({
                status: true,
                message: 'Statement uploaded successfully',
                data: statement,
            });
        }
        catch (error) {
            console.error('Error uploading statement:', error);
            res.status(500).json({ message: 'Server error' });
        }
    });
};
exports.uploadStatement = uploadStatement;
/**
 * Parse statement file (CSV, Excel, etc.)
 * @route POST /api/statements/parse
 */
const parseStatementFile = async (req, res) => {
    try {
        const { statementId } = req.body;
        const userId = req.user.sub;
        if (!statementId || !mongoose_1.default.Types.ObjectId.isValid(statementId)) {
            res.status(400).json({ message: 'Valid statement ID is required' });
            return;
        }
        // Check if statement exists and belongs to user
        const statement = await statement_model_1.default.findOne({
            _id: statementId,
            userId,
        });
        if (!statement) {
            res.status(404).json({ message: 'Statement not found' });
            return;
        }
        // Process and store statement data
        const statementData = await (0, statementData_service_1.processAndStoreStatementData)(statementId, userId);
        res.status(200).json({
            status: true,
            message: 'Statement file parsed successfully',
            data: {
                id: statement._id,
                name: statement.name,
                status: statementData.status,
                summary: statementData.summary || {
                    startDate: statement.startDate,
                    endDate: statement.endDate,
                    startBalance: statement.startBalance,
                    endBalance: statement.endBalance,
                    totalCredits: 0,
                    totalDebits: 0,
                    transactionCount: 0,
                },
            },
        });
    }
    catch (error) {
        console.error('Error parsing statement file:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.parseStatementFile = parseStatementFile;
/**
 * Download statement file
 * @route GET /api/statements/:id/download
 */
const downloadStatementFile = async (req, res) => {
    try {
        const { id } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid statement ID' });
            return;
        }
        const statement = await statement_model_1.default.findById(id);
        if (!statement) {
            res.status(404).json({ message: 'Statement not found' });
            return;
        }
        // Check if file exists
        if (!statement.filePath || !fs_1.default.existsSync(statement.filePath)) {
            // Try to find the file in the uploads directory using the fileName
            const possiblePaths = [
                path_1.default.join(uploadsDir, statement.fileName),
                path_1.default.join(os_1.default.tmpdir(), statement.fileName)
            ];
            let fileFound = false;
            for (const possiblePath of possiblePaths) {
                if (fs_1.default.existsSync(possiblePath)) {
                    // Update the statement with the correct file path
                    statement.filePath = possiblePath;
                    await statement.save();
                    fileFound = true;
                    console.log(`Found statement file at ${possiblePath}`);
                    break;
                }
            }
            if (!fileFound) {
                console.error(`Statement file not found: ${statement.fileName}`);
                res.status(404).json({ message: 'Statement file not found' });
                return;
            }
        }
        // Set headers for file download
        res.setHeader('Content-Disposition', `attachment; filename="${statement.fileName}"`);
        res.setHeader('Content-Type', 'application/octet-stream');
        // Stream the file
        const fileStream = fs_1.default.createReadStream(statement.filePath);
        fileStream.pipe(res);
    }
    catch (error) {
        console.error('Error downloading statement file:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.downloadStatementFile = downloadStatementFile;
/**
 * Get statement preview
 * @route GET /api/statements/:id/preview
 */
const getStatementPreview = async (req, res) => {
    try {
        const { id } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid statement ID' });
            return;
        }
        const statement = await statement_model_1.default.findById(id);
        if (!statement) {
            res.status(404).json({ message: 'Statement not found' });
            return;
        }
        // Check if file exists
        if (!statement.filePath || !fs_1.default.existsSync(statement.filePath)) {
            // Try to find the file in the uploads directory using the fileName
            const possiblePaths = [
                path_1.default.join(uploadsDir, statement.fileName),
                path_1.default.join(os_1.default.tmpdir(), statement.fileName)
            ];
            let fileFound = false;
            for (const possiblePath of possiblePaths) {
                if (fs_1.default.existsSync(possiblePath)) {
                    // Update the statement with the correct file path
                    statement.filePath = possiblePath;
                    await statement.save();
                    fileFound = true;
                    console.log(`Found statement file at ${possiblePath}`);
                    break;
                }
            }
            if (!fileFound) {
                console.error(`Statement file not found: ${statement.fileName}`);
                res.status(404).json({ message: 'Statement file not found' });
                return;
            }
        }
        // Try to get statement data from MongoDB first
        try {
            // Check if we have stored statement data
            const statementData = await (0, statementData_service_1.getStatementData)(statement._id.toString());
            if (statementData && statementData.status === 'completed') {
                // Return the stored data
                res.status(200).json({
                    status: true,
                    data: {
                        id: statement._id,
                        name: statement.name,
                        fileName: statement.fileName,
                        fileSize: statementData.metadata.fileSize,
                        fileType: statementData.metadata.fileType,
                        uploadDate: statement.createdAt,
                        preview: statement.name, // Use statement name as preview
                        content: statementData.content,
                        metadata: statementData.metadata,
                        summary: statementData.summary,
                        insights: statementData.insights || {},
                        processingStatus: statementData.status
                    }
                });
                return;
            }
            else if (statementData && statementData.status === 'processing') {
                // Data is still being processed
                res.status(200).json({
                    status: true,
                    data: {
                        id: statement._id,
                        name: statement.name,
                        fileName: statement.fileName,
                        fileSize: statementData.metadata.fileSize || 0,
                        fileType: statementData.metadata.fileType,
                        uploadDate: statement.createdAt,
                        preview: 'Processing statement data...',
                        processingStatus: 'processing'
                    }
                });
                return;
            }
            else if (statementData && statementData.status === 'failed') {
                // Processing failed, try to parse the file again
                console.log(`Statement data processing failed for statement ${statement._id}, trying to parse file again`);
                // Start processing again in the background
                (0, statementData_service_1.processAndStoreStatementData)(statement._id.toString(), statement.userId.toString())
                    .then(() => {
                    console.log(`Statement data processing completed for statement ${statement._id}`);
                })
                    .catch(err => {
                    console.error(`Error processing statement data for statement ${statement._id}:`, err);
                });
            }
            else {
                // No statement data found, start processing
                console.log(`No statement data found for statement ${statement._id}, starting processing`);
                // Start processing in the background
                (0, statementData_service_1.processAndStoreStatementData)(statement._id.toString(), statement.userId.toString())
                    .then(() => {
                    console.log(`Statement data processing completed for statement ${statement._id}`);
                })
                    .catch(err => {
                    console.error(`Error processing statement data for statement ${statement._id}:`, err);
                });
            }
            // Fall back to parsing the file directly
            const { parseFile } = require('../utils/fileParser');
            const filePreview = await parseFile(statement.filePath);
            res.status(200).json({
                status: true,
                data: {
                    id: statement._id,
                    name: statement.name,
                    fileName: statement.fileName,
                    fileSize: filePreview.metadata.fileSize,
                    fileType: filePreview.metadata.fileType,
                    uploadDate: statement.createdAt,
                    preview: filePreview.preview,
                    content: filePreview.content,
                    metadata: filePreview.metadata,
                    summary: filePreview.summary,
                    insights: filePreview.insights || {},
                    processingStatus: statementData ? statementData.status : 'processing'
                }
            });
        }
        catch (error) {
            // If all else fails, fall back to basic metadata
            console.error('Error getting statement preview:', error);
            const fileStats = fs_1.default.statSync(statement.filePath);
            res.status(200).json({
                status: true,
                data: {
                    id: statement._id,
                    name: statement.name,
                    fileName: statement.fileName,
                    fileSize: fileStats.size,
                    fileType: path_1.default.extname(statement.fileName).toLowerCase(),
                    uploadDate: statement.createdAt,
                    preview: 'File preview not available',
                    parseError: error instanceof Error ? error.message : String(error),
                    processingStatus: 'failed'
                }
            });
        }
    }
    catch (error) {
        console.error('Error getting statement preview:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getStatementPreview = getStatementPreview;
