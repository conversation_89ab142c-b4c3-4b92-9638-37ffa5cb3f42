import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiUpload, FiFileText, FiAlertCircle, FiCheck } from 'react-icons/fi';
import { motion } from 'framer-motion';
import useReconciliationStore from '../../store/reconciliationStore';

const StatementUploadPage = () => {
  const navigate = useNavigate();
  const { uploadStatement, statementsLoading, statementsError } = useReconciliationStore();

  const [file, setFile] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    statement_type: 'bank',
    statement_date: new Date().toISOString().split('T')[0],
  });
  const [dragActive, setDragActive] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileChange(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (selectedFile) => {
    // Check if file is a PDF, CSV or Excel file
    if (selectedFile.type === 'application/pdf' ||
        selectedFile.name.endsWith('.pdf') ||
        selectedFile.type === 'text/csv' || 
        selectedFile.name.endsWith('.csv') ||
        selectedFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        selectedFile.name.endsWith('.xlsx') ||
        selectedFile.name.endsWith('.xls')) {
      setFile(selectedFile);
      setUploadError(null);
    } else {
      setFile(null);
      setUploadError('Please upload a PDF, CSV or Excel (XLSX/XLS) file');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!file) {
      setUploadError('Please select a file to upload');
      return;
    }

    if (!formData.name) {
      setUploadError('Please enter a statement name');
      return;
    }

    try {
      setUploadError(null);
      
      const data = new FormData();
      data.append('file', file);
      data.append('name', formData.name);
      data.append('statement_type', formData.statement_type);
      data.append('statement_date', formData.statement_date);
      
      await uploadStatement(data);
      setUploadSuccess(true);
      
      // Redirect after a short delay
      setTimeout(() => {
        navigate('/statements');
      }, 2000);
    } catch (error) {
      setUploadError(error.message || 'Failed to upload statement');
    }
  };

  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Upload Statement</h1>
        <button
          onClick={() => navigate('/statements')}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel
        </button>
      </div>

      {uploadSuccess ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 p-6 rounded-lg shadow-sm mb-6"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FiCheck className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-green-800">Upload Successful</h3>
              <div className="mt-2 text-green-700">
                <p>Your statement has been uploaded successfully. Redirecting to statements list...</p>
              </div>
            </div>
          </div>
        </motion.div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error message */}
          {(uploadError || statementsError) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 p-4 rounded-md"
            >
              <div className="flex">
                <div className="flex-shrink-0">
                  <FiAlertCircle className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{uploadError || statementsError}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Statement details */}
          <div className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Statement Details</h2>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Statement Name
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="e.g. January 2023 Bank Statement"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="statement_date" className="block text-sm font-medium text-gray-700">
                  Statement Date
                </label>
                <input
                  type="date"
                  name="statement_date"
                  id="statement_date"
                  value={formData.statement_date}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required
                />
              </div>
              
              <div className="sm:col-span-2">
                <label htmlFor="statement_type" className="block text-sm font-medium text-gray-700">
                  Statement Type
                </label>
                <select
                  id="statement_type"
                  name="statement_type"
                  value={formData.statement_type}
                  onChange={handleInputChange}
                  className="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="bank">Bank Statement</option>
                  <option value="cashbook">Cashbook Statement</option>
                </select>
              </div>
            </div>
          </div>

          {/* File upload */}
          <div className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Upload File</h2>
            
            <div 
              className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
                dragActive ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <div className="space-y-1 text-center">
                <FiUpload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="flex text-sm text-gray-600">
                  <label
                    htmlFor="file-upload"
                    className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                  >
                    <span>Upload a file</span>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      className="sr-only"
                      accept=".pdf,.csv,.xlsx,.xls"
                      onChange={(e) => handleFileChange(e.target.files[0])}
                    />
                  </label>
                  <p className="pl-1">or drag and drop</p>
                </div>
                <p className="text-xs text-gray-500">PDF, CSV, or Excel files up to 10MB</p>
                
                {file && (
                  <div className="mt-2 flex items-center justify-center text-sm text-gray-600">
                    <FiFileText className="mr-2 h-5 w-5 text-indigo-500" />
                    <span>{file.name}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Submit button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={statementsLoading}
              className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                statementsLoading ? 'opacity-75 cursor-not-allowed' : ''
              }`}
            >
              {statementsLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Uploading...
                </>
              ) : (
                <>
                  <FiUpload className="-ml-1 mr-2 h-5 w-5" />
                  Upload Statement
                </>
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default StatementUploadPage;

