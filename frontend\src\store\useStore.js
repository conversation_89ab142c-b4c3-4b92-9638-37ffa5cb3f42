import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useStore = create(
  persist(
    (set, get) => ({
      user: null,
      timeTracking: {
        isClockIn: false,
        startTime: null,
        currentSession: 0,
        dailyLogs: [],
        lastUpdate: null,
      },
      setUser: (userData) => {
        console.log('Setting user data in store:', userData);
        set({ user: userData });
      },
      clearUser: () => set({ user: null }),
      clockIn: () => {
        const now = Date.now();
        set(state => ({
          timeTracking: {
            ...state.timeTracking,
            isClockIn: true,
            startTime: now,
            lastUpdate: now,
          }
        }));
      },
      clockOut: () => {
        const now = Date.now();
        set(state => {
          const session = {
            start: state.timeTracking.startTime,
            end: now,
            duration: now - state.timeTracking.startTime,
          };

          return {
            timeTracking: {
              ...state.timeTracking,
              isClockIn: false,
              startTime: null,
              currentSession: 0,
              dailyLogs: [...state.timeTracking.dailyLogs, session],
            }
          };
        });
      },
      updateSession: () => {
        set(state => {
          if (!state.timeTracking.isClockIn) return state;
          const now = Date.now();
          return {
            timeTracking: {
              ...state.timeTracking,
              currentSession: now - state.timeTracking.startTime,
              lastUpdate: now,
            }
          };
        });
      },
    }),
    {
      name: 'user-storage',
    }
  )
);

// Start session timer
if (typeof window !== 'undefined') {
  setInterval(() => {
    useStore.getState().updateSession();
  }, 1000);
}

export default useStore;
