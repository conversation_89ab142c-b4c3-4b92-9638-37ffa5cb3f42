import mongoose, { Document, Schema } from 'mongoose';

// Statement interface
export interface IStatement extends Document {
  name: string;
  bankName: string;
  accountNumber: string;
  startDate: Date;
  endDate: Date;
  startBalance: number;
  endBalance: number;
  fileName: string;
  filePath: string;
  statement_type?: string;
  statement_date?: Date;
  userId: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Statement schema
const statementSchema = new Schema<IStatement>(
  {
    name: {
      type: String,
      required: [true, 'Statement name is required'],
      trim: true,
    },
    bankName: {
      type: String,
      required: [true, 'Bank name is required'],
      trim: true,
    },
    accountNumber: {
      type: String,
      required: [true, 'Account number is required'],
      trim: true,
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required'],
      index: true, // Add index for date-based queries
    },
    endDate: {
      type: Date,
      required: [true, 'End date is required'],
      index: true, // Add index for date-based queries
    },
    startBalance: {
      type: Number,
      required: [true, 'Start balance is required'],
    },
    endBalance: {
      type: Number,
      required: [true, 'End balance is required'],
    },
    fileName: {
      type: String,
      required: [true, 'File name is required'],
    },
    filePath: {
      type: String,
      required: [true, 'File path is required'],
    },
    statement_type: {
      type: String,
      enum: ['bank', 'credit_card', 'investment', 'cashbook', 'other'],
      default: 'bank',
      index: true, // Add index for type-based queries
    },
    statement_date: {
      type: Date,
      default: Date.now,
      index: true, // Add index for date-based queries
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true, // Add index for user-based queries
    },
  },
  {
    timestamps: true,
  }
);

// Create compound indexes for common query patterns
statementSchema.index({ userId: 1, createdAt: -1 }); // For listing statements by user, sorted by creation date

// Create and export Statement model
export default mongoose.model<IStatement>('Statement', statementSchema);
