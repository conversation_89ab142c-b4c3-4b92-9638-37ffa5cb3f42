import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Transaction, { ITransaction, TransactionStatus } from '../models/transaction.model';

/**
 * Get all transactions
 * @route GET /api/transactions
 */
export const getAllTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get user ID from authenticated user
    const userId = req.user.sub;

    // Find all transactions for this user
    const transactions = await Transaction.find({ userId })
      .sort({ date: -1 })
      .populate('statementId', 'name bankName')
      .populate('matchedWith', 'date description amount reference');

    res.status(200).json({
      status: true,
      count: transactions.length,
      data: transactions,
    });
  } catch (error) {
    console.error('Error getting transactions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get transaction by ID
 * @route GET /api/transactions/:id
 */
export const getTransactionById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid transaction ID' });
      return;
    }

    // Find transaction by ID and user ID
    const transaction = await Transaction.findOne({
      _id: id,
      userId,
    })
      .populate('statementId', 'name bankName')
      .populate('matchedWith', 'date description amount reference');

    if (!transaction) {
      res.status(404).json({ message: 'Transaction not found' });
      return;
    }

    res.status(200).json({
      status: true,
      data: transaction,
    });
  } catch (error) {
    console.error('Error getting transaction:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Create a new transaction
 * @route POST /api/transactions
 */
export const createTransaction = async (req: Request, res: Response): Promise<void> => {
  try {
    const { date, description, amount, reference, source, statementId } = req.body;
    const userId = req.user.sub;

    // Create new transaction
    const transaction = new Transaction({
      date,
      description,
      amount,
      reference,
      source,
      statementId,
      userId,
    });

    // Save transaction to database
    await transaction.save();

    res.status(201).json({
      status: true,
      message: 'Transaction created successfully',
      data: transaction,
    });
  } catch (error) {
    console.error('Error creating transaction:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update transaction
 * @route PUT /api/transactions/:id
 */
export const updateTransaction = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { date, description, amount, reference, source, status } = req.body;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid transaction ID' });
      return;
    }

    // Find transaction by ID and user ID
    const transaction = await Transaction.findOne({
      _id: id,
      userId,
    });

    if (!transaction) {
      res.status(404).json({ message: 'Transaction not found' });
      return;
    }

    // Update transaction fields
    if (date) transaction.date = new Date(date);
    if (description) transaction.description = description;
    if (amount) transaction.amount = amount;
    if (reference) transaction.reference = reference;
    if (source) transaction.source = source;
    if (status) transaction.status = status;

    // Save updated transaction
    await transaction.save();

    res.status(200).json({
      status: true,
      message: 'Transaction updated successfully',
      data: transaction,
    });
  } catch (error) {
    console.error('Error updating transaction:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Delete transaction
 * @route DELETE /api/transactions/:id
 */
export const deleteTransaction = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid transaction ID' });
      return;
    }

    // Find and delete transaction
    const transaction = await Transaction.findOneAndDelete({
      _id: id,
      userId,
    });

    if (!transaction) {
      res.status(404).json({ message: 'Transaction not found' });
      return;
    }

    // If this transaction was matched with another, update the other transaction
    if (transaction.matchedWith) {
      await Transaction.findByIdAndUpdate(transaction.matchedWith, {
        $unset: { matchedWith: 1 },
        status: TransactionStatus.UNMATCHED,
      });
    }

    res.status(200).json({
      status: true,
      message: 'Transaction deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting transaction:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get transactions by statement ID
 * @route GET /api/transactions/statement/:id
 */
export const getTransactionsByStatement = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid statement ID' });
      return;
    }

    // Find all transactions for this statement and user
    const transactions = await Transaction.find({
      statementId: id,
      userId,
    }).sort({ date: -1 });

    res.status(200).json({
      status: true,
      count: transactions.length,
      data: transactions,
    });
  } catch (error) {
    console.error('Error getting transactions by statement:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Match transactions
 * @route POST /api/transactions/match
 */
export const matchTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { transaction1Id, transaction2Id } = req.body;
    const userId = req.user.sub;

    // Validate ObjectIds
    if (!mongoose.Types.ObjectId.isValid(transaction1Id) || !mongoose.Types.ObjectId.isValid(transaction2Id)) {
      res.status(400).json({ message: 'Invalid transaction ID' });
      return;
    }

    // Find both transactions
    const transaction1 = await Transaction.findOne({
      _id: transaction1Id,
      userId,
    });

    const transaction2 = await Transaction.findOne({
      _id: transaction2Id,
      userId,
    });

    if (!transaction1 || !transaction2) {
      res.status(404).json({ message: 'One or both transactions not found' });
      return;
    }

    // Update both transactions to be matched with each other
    transaction1.matchedWith = transaction2._id as unknown as mongoose.Types.ObjectId;
    transaction1.status = TransactionStatus.MATCHED;
    await transaction1.save();

    transaction2.matchedWith = transaction1._id as unknown as mongoose.Types.ObjectId;
    transaction2.status = TransactionStatus.MATCHED;
    await transaction2.save();

    res.status(200).json({
      status: true,
      message: 'Transactions matched successfully',
      data: {
        transaction1,
        transaction2,
      },
    });
  } catch (error) {
    console.error('Error matching transactions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Unmatch transaction
 * @route POST /api/transactions/unmatch/:id
 */
export const unmatchTransaction = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid transaction ID' });
      return;
    }

    // Find transaction
    const transaction = await Transaction.findOne({
      _id: id,
      userId,
    });

    if (!transaction) {
      res.status(404).json({ message: 'Transaction not found' });
      return;
    }

    // If this transaction is matched with another, update the other transaction
    if (transaction.matchedWith) {
      await Transaction.findByIdAndUpdate(transaction.matchedWith, {
        $unset: { matchedWith: 1 },
        status: TransactionStatus.UNMATCHED,
      });
    }

    // Update this transaction
    transaction.matchedWith = undefined;
    transaction.status = TransactionStatus.UNMATCHED;
    await transaction.save();

    res.status(200).json({
      status: true,
      message: 'Transaction unmatched successfully',
      data: transaction,
    });
  } catch (error) {
    console.error('Error unmatching transaction:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
