"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const reconciliation_controller_1 = require("../controllers/reconciliation.controller");
const router = express_1.default.Router();
// @route   GET /api/reconciliations
// @desc    Get all reconciliations
// @access  Private
router.get('/', reconciliation_controller_1.getAllReconciliations);
// @route   GET /api/reconciliations/:id
// @desc    Get reconciliation by ID
// @access  Private
router.get('/:id', reconciliation_controller_1.getReconciliationById);
// @route   POST /api/reconciliations
// @desc    Create a new reconciliation
// @access  Private
router.post('/', reconciliation_controller_1.createReconciliation);
// @route   GET /api/reconciliations/:id/transactions
// @desc    Get transactions for a reconciliation
// @access  Private
router.get('/:id/transactions', reconciliation_controller_1.getReconciliationTransactions);
// @route   POST /api/reconciliations/:id/complete
// @desc    Complete a reconciliation
// @access  Private
router.post('/:id/complete', reconciliation_controller_1.completeReconciliation);
exports.default = router;
