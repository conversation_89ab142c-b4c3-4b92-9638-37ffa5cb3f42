import React, { useState } from 'react';
import { FiChevronLeft, FiChevronRight, FiInfo, FiBarChart2, FiAlertCircle, FiClock } from 'react-icons/fi';
import ProgressLoader from '../common/ProgressLoader';

const CSVPreview = ({ data, processingStatus = 'completed', fetchTime = 0 }) => {
  const [showDataTypes, setShowDataTypes] = useState(false);

  // Handle the new data structure
  const content = data.content || {};
  const summary = data.summary || {};
  const dataTypes = summary.dataTypes || {};
  const statistics = summary.statistics || {};

  // We don't need to show processing status anymore since Python/Rust processing is very fast
  // This block is intentionally removed

  // If processing failed, show error
  if (processingStatus === 'failed') {
    return (
      <div className="bg-white p-4 rounded border border-gray-200">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            {data.name || 'CSV File'}
          </h3>
          <p className="text-sm text-gray-600">
            {data.fileName || 'Failed to process file'}
          </p>
        </div>

        <div className="my-8 px-4">
          <ProgressLoader
            status="failed"
            progress={0}
            text="Failed to process CSV data"
            showPercentage={false}
          />
        </div>

        <div className="text-sm text-red-500 mt-4 text-center">
          <p>{data.error || 'An error occurred while processing the file.'}</p>
        </div>
      </div>
    );
  }

  // If we don't have parsed content, show the raw preview
  if (!content.headers || !content.rows) {
    return (
      <div className="bg-white p-4 rounded border border-gray-200">
        <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto">
          {data.preview || 'No preview available'}
        </pre>

        {/* Show fetch time if available */}
        {fetchTime > 0 && (
          <div className="mt-4 text-xs text-gray-500 text-right">
            Fetched in {fetchTime}ms
          </div>
        )}
      </div>
    );
  }

  const { headers, rows, totalRows } = content;

  // Get the data type for a column
  const getDataType = (header) => {
    if (!dataTypes[header]) return null;

    const type = dataTypes[header];
    if (type === 'number') return 'Number';
    if (type === 'string') return 'Text';
    if (type === 'date') return 'Date';
    if (type === 'boolean') return 'Boolean';
    if (type.includes('/')) return 'Mixed';
    return type;
  };

  // Get the color for a data type
  const getTypeColor = (type) => {
    if (!type) return 'bg-gray-100';
    if (type === 'Number') return 'bg-blue-100 text-blue-800';
    if (type === 'Text') return 'bg-green-100 text-green-800';
    if (type === 'Date') return 'bg-purple-100 text-purple-800';
    if (type === 'Boolean') return 'bg-yellow-100 text-yellow-800';
    if (type === 'Mixed') return 'bg-red-100 text-red-800';
    return 'bg-gray-100';
  };

  // Get statistics for a column
  const getColumnStats = (header) => {
    if (!statistics[header]) return null;

    return statistics[header];
  };

  return (
    <div className="border rounded-md overflow-hidden">
      {/* Data type toggle */}
      {Object.keys(dataTypes).length > 0 && (
        <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex justify-between items-center">
          <div className="flex items-center">
            <FiInfo className="text-gray-500 mr-2" />
            <span className="text-sm text-gray-600">
              {Object.keys(dataTypes).length} columns detected
            </span>

            {/* Show fetch time if available */}
            {fetchTime > 0 && processingStatus === 'completed' && (
              <span className="ml-3 text-xs text-gray-500">
                Fetched in {fetchTime}ms
              </span>
            )}
          </div>
          <button
            onClick={() => setShowDataTypes(!showDataTypes)}
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            {showDataTypes ? 'Hide Data Types' : 'Show Data Types'}
          </button>
        </div>
      )}

      {/* Data type info */}
      {showDataTypes && Object.keys(dataTypes).length > 0 && (
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
            <FiBarChart2 className="mr-1" /> Column Data Types
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            {headers.map((header) => {
              const type = getDataType(header);
              const stats = getColumnStats(header);

              return (
                <div key={header} className="bg-white p-2 rounded border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div className="font-medium text-sm truncate" title={header}>
                      {header}
                    </div>
                    {type && (
                      <span className={`text-xs px-1.5 py-0.5 rounded ${getTypeColor(type)}`}>
                        {type}
                      </span>
                    )}
                  </div>

                  {stats && stats.min !== undefined && (
                    <div className="mt-1 text-xs text-gray-500">
                      <div className="flex justify-between">
                        <span>Min:</span>
                        <span className="font-medium">{stats.min}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Max:</span>
                        <span className="font-medium">{stats.max}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Avg:</span>
                        <span className="font-medium">{stats.average.toFixed(2)}</span>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Table with scrollbars */}
      <div className="overflow-auto custom-scrollbar" style={{
        maxHeight: '70vh',
        maxWidth: '100%',
        overflowY: 'scroll',
        overflowX: 'scroll',
        border: '1px solid #ddd',
        borderRadius: '4px'
      }}>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50 sticky top-0 z-10">
            <tr>
              {/* Line number header */}
              <th scope="col" className="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100 w-12">
                #
              </th>
              {headers.map((header, index) => (
                <th
                  key={index}
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {header}
                  {showDataTypes && getDataType(header) && (
                    <span className={`ml-1 text-xs px-1.5 py-0.5 rounded ${getTypeColor(getDataType(header))}`}>
                      {getDataType(header)}
                    </span>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {rows.map((row, rowIndex) => (
              <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                {/* Line number column */}
                <td className="px-2 py-4 whitespace-nowrap text-xs text-gray-500 bg-gray-100 text-right font-mono w-12">
                  {rowIndex + 1}
                </td>
                {headers.map((header, colIndex) => (
                  <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {row[header] || ''}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Total rows info */}
      <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
        <p className="text-sm text-gray-700">
          Total rows: <span className="font-medium">{rows.length}</span>
          {totalRows > rows.length && (
            <span> (of {totalRows} total rows)</span>
          )}
        </p>
      </div>
    </div>
  );
};

export default CSVPreview;
