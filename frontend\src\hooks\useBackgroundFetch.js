import { useState, useEffect, useRef } from 'react';

/**
 * Custom hook for background data fetching
 * @param {Function} fetchFunction - The function to fetch data
 * @param {Array} dependencies - Dependencies that trigger a refetch when changed
 * @param {Object} options - Additional options
 * @returns {Object} - The fetch state and control functions
 */
const useBackgroundFetch = (fetchFunction, dependencies = [], options = {}) => {
  const {
    initialData = null,
    initialLoading = true,
    initialError = null,
    onSuccess = () => {},
    onError = () => {},
    refreshInterval = null, // Auto-refresh interval in milliseconds
  } = options;

  const [data, setData] = useState(initialData);
  const [loading, setLoading] = useState(initialLoading);
  const [backgroundLoading, setBackgroundLoading] = useState(false);
  const [error, setError] = useState(initialError);
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  // Use refs to avoid stale closures in the interval
  const dataRef = useRef(data);
  const fetchFunctionRef = useRef(fetchFunction);
  const intervalRef = useRef(null);

  // Update refs when dependencies change
  useEffect(() => {
    dataRef.current = data;
    fetchFunctionRef.current = fetchFunction;
  }, [data, fetchFunction]);

  // Function to refresh data in the background
  const refreshData = async (showLoading = false) => {
    try {
      // If showLoading is true, show the loading indicator
      if (showLoading) {
        setLoading(true);
      } else {
        setBackgroundLoading(true);
      }

      // Call the fetch function
      const result = await fetchFunctionRef.current();

      // Update the data
      setData(result);
      dataRef.current = result;

      // Call the success callback
      onSuccess(result);

      return result;
    } catch (err) {
      // Convert error to string if it's an Error object
      const errorMessage = err instanceof Error ? err.message : String(err);

      // Set the error as a string
      setError(errorMessage);

      // Call the error callback
      onError(errorMessage);

      return null;
    } finally {
      // Reset loading states
      setLoading(false);
      setBackgroundLoading(false);

      // Update the last refresh timestamp
      setLastRefresh(Date.now());
    }
  };

  // Initial fetch and dependency-based refetch
  useEffect(() => {
    refreshData(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...dependencies, lastRefresh]);

  // Set up auto-refresh interval if specified
  useEffect(() => {
    if (refreshInterval) {
      intervalRef.current = setInterval(() => {
        refreshData(false);
      }, refreshInterval);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshInterval]);

  return {
    data,
    loading,
    backgroundLoading,
    error,
    refreshData: () => refreshData(false),
    refreshDataWithLoading: () => refreshData(true),
    setData,
    lastRefresh,
  };
};

export default useBackgroundFetch;
