"""
Development server with hot reloading using watchdog.
This script watches for file changes and automatically restarts the server.
It also loads environment variables from .env file.
"""

import os
import sys
import time
import logging
import subprocess
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Dependencies: watchdog, python-dotenv

# MongoDB connection will be handled by the application

# Path to watch for changes
WATCH_PATH = os.path.dirname(os.path.abspath(__file__))

# Server process
server_process = None

class ServerRestartHandler(FileSystemEventHandler):
    """Handler for restarting the server when files change."""

    def __init__(self, restart_func):
        self.restart_func = restart_func
        self.last_modified = time.time()
        self.debounce_seconds = 1.0  # Debounce period to avoid multiple restarts

    def on_any_event(self, event):
        # Skip directory events and hidden files
        if event.is_directory or event.src_path.startswith('.'):
            return

        # Skip __pycache__ and .pyc files
        if '__pycache__' in event.src_path or event.src_path.endswith('.pyc'):
            return

        # Check if enough time has passed since the last restart
        if time.time() - self.last_modified < self.debounce_seconds:
            return

        self.last_modified = time.time()
        logging.info(f"File {event.src_path} has been {event.event_type}")
        self.restart_func()

def start_server():
    """Start the server process."""
    global server_process

    # Kill existing process if it exists
    if server_process:
        logging.info("Stopping server...")
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_process.kill()
        server_process = None

    # Get environment variables
    env = os.environ.copy()

    # Ensure encryption configuration is set
    if 'JWT_SECRET' not in env:
        logging.warning("JWT_SECRET not set in environment variables")

    if 'JWT_EXPIRATION' not in env:
        logging.warning("JWT_EXPIRATION not set in environment variables")

    # Start new server process
    logging.info("Starting server...")

    # Check which Python entry point file exists
    entry_points = ["run.py", "dev.py", "app.py", "main.py"]
    entry_file = None

    for file in entry_points:
        if os.path.exists(file):
            entry_file = file
            break

    if not entry_file:
        logging.error("No entry point file found. Tried: " + ", ".join(entry_points))
        return

    logging.info(f"Using entry point: {entry_file}")

    server_process = subprocess.Popen(
        [sys.executable, entry_file],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        bufsize=1,
        env=env
    )

    # Log server output in a non-blocking way
    def log_output():
        for line in server_process.stdout:
            print(line, end='')

    # Start a thread to log output
    import threading
    log_thread = threading.Thread(target=log_output)
    log_thread.daemon = True
    log_thread.start()

def watch_files():
    """Watch for file changes and restart the server."""
    # Start the server initially
    start_server()

    # Set up the file watcher
    event_handler = ServerRestartHandler(start_server)
    observer = Observer()
    observer.schedule(event_handler, WATCH_PATH, recursive=True)
    observer.start()

    try:
        logging.info(f"Watching for changes in {WATCH_PATH}")
        logging.info("Press Ctrl+C to stop")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logging.info("Stopping server and watcher...")
        if server_process:
            server_process.terminate()
        observer.stop()

    observer.join()

if __name__ == "__main__":
    watch_files()
