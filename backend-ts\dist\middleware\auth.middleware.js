"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.conditionalAuth = exports.publicRoutes = exports.auth = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = __importDefault(require("../config"));
/**
 * Authentication middleware
 * Verifies JWT token and adds user to request object
 */
const auth = (req, res, next) => {
    // Get token from header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    // Check if no token
    if (!token) {
        res.status(401).json({ message: 'No token, authorization denied' });
        return;
    }
    try {
        // Verify token
        const decoded = jsonwebtoken_1.default.verify(token, config_1.default.jwtSecret);
        // Add user from payload
        req.user = decoded;
        next();
    }
    catch (err) {
        res.status(401).json({ message: 'Token is not valid' });
    }
};
exports.auth = auth;
/**
 * List of public routes that don't require authentication
 */
exports.publicRoutes = [
    '/api/health',
    '/api/auth/login',
    '/api/auth/register'
];
/**
 * Conditional authentication middleware
 * Only applies auth middleware to protected routes
 */
const conditionalAuth = (req, res, next) => {
    // Check if the route is public
    const isPublicRoute = exports.publicRoutes.some(route => req.path.startsWith(route));
    if (isPublicRoute) {
        // Skip authentication for public routes
        return next();
    }
    // Apply authentication for protected routes
    (0, exports.auth)(req, res, next);
};
exports.conditionalAuth = conditionalAuth;
