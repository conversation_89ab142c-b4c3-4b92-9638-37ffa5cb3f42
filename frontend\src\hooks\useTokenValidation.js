import { useEffect, useState } from 'react';
import { devService } from '../services/api';

/**
 * Hook to validate token on page load and periodically
 * @param {Object} options - Configuration options
 * @param {boolean} options.validateOnMount - Whether to validate token when component mounts
 * @param {number} options.interval - Interval in milliseconds to validate token (default: 5 minutes)
 * @param {boolean} options.validateOnFocus - Whether to validate token when window regains focus
 * @returns {Object} - Token validation state
 */
const useTokenValidation = ({
  validateOnMount = true,
  interval = 5 * 60 * 1000, // 5 minutes
  validateOnFocus = true
} = {}) => {
  const [isValidating, setIsValidating] = useState(false);
  const [lastValidated, setLastValidated] = useState(null);

  // Function to validate token
  const validateToken = async () => {
    if (isValidating) return;
    
    setIsValidating(true);
    try {
      await devService.validateToken();
      setLastValidated(new Date());
    } catch (error) {
      console.error('Token validation failed:', error);
    } finally {
      setIsValidating(false);
    }
  };

  // Validate token on mount if enabled
  useEffect(() => {
    if (validateOnMount) {
      validateToken();
    }
  }, [validateOnMount]);

  // Set up interval for periodic validation
  useEffect(() => {
    if (interval > 0) {
      const intervalId = setInterval(validateToken, interval);
      return () => clearInterval(intervalId);
    }
  }, [interval]);

  // Set up focus event listener if enabled
  useEffect(() => {
    if (validateOnFocus) {
      const handleFocus = () => {
        // Only validate if it's been more than 1 minute since last validation
        const now = new Date();
        if (!lastValidated || (now - lastValidated) > 60000) {
          validateToken();
        }
      };

      window.addEventListener('focus', handleFocus);
      return () => window.removeEventListener('focus', handleFocus);
    }
  }, [validateOnFocus, lastValidated]);

  return {
    isValidating,
    lastValidated,
    validateToken
  };
};

export default useTokenValidation;
