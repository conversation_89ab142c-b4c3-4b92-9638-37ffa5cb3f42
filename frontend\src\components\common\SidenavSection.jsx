import React from 'react';
import { useLocation } from 'react-router-dom';
import SidenavTab from './SidenavTab';

/**
 * A reusable component for sidenav sections with background data fetching
 * @param {Object} props - Component props
 * @returns {JSX.Element} - The rendered component
 */
const SidenavSection = ({
  title,                // Section title
  tabs,                 // Array of tab objects
  basePath,             // Base path for the section
  refreshInterval = 0,  // Auto-refresh interval in milliseconds (0 = disabled)
}) => {
  const location = useLocation();
  const currentPath = location.pathname;
  
  return (
    <div className="mb-6">
      {title && (
        <h3 className="px-4 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
          {title}
        </h3>
      )}
      
      <div className="space-y-1">
        {tabs.map((tab) => {
          const isActive = currentPath === `${basePath}${tab.path}`;
          
          return (
            <SidenavTab
              key={tab.path}
              to={`${basePath}${tab.path}`}
              label={tab.label}
              icon={tab.icon}
              fetchFunction={tab.fetchFunction}
              isActive={isActive}
              refreshInterval={refreshInterval}
              onDataLoaded={tab.onDataLoaded}
              badge={tab.badge}
            />
          );
        })}
      </div>
    </div>
  );
};

export default SidenavSection;
