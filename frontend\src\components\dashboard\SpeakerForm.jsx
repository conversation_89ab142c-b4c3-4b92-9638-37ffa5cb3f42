import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import speakerService from '../../services/speakerService';
import { FaUpload, FaSave, FaTimes } from 'react-icons/fa';

const SpeakerForm = ({ speaker = null, onSuccess, onCancel }) => {
  const navigate = useNavigate();
  const isEditing = !!speaker;

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    title: '',
    bio: '',
    topics: [],
    image: '',
    verified: false,
    socialLinks: {
      linkedin: '',
      twitter: '',
      website: ''
    }
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [topicsInput, setTopicsInput] = useState('');
  const [previewImage, setPreviewImage] = useState('');

  // Initialize form with speaker data if editing
  useEffect(() => {
    if (speaker) {
      setFormData({
        name: speaker.name || '',
        email: speaker.email || '',
        title: speaker.title || '',
        bio: speaker.bio || '',
        topics: speaker.topics || [],
        image: speaker.image || '',
        verified: speaker.verified || false,
        socialLinks: {
          linkedin: speaker.social?.linkedin || '',
          twitter: speaker.social?.twitter || '',
          website: speaker.social?.website || ''
        }
      });

      setTopicsInput(speaker.topics ? speaker.topics.join(', ') : '');
      setPreviewImage(speaker.image || '');
    }
  }, [speaker]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name.startsWith('social_')) {
      const socialType = name.replace('social_', '');
      setFormData({
        ...formData,
        socialLinks: {
          ...formData.socialLinks,
          [socialType]: value
        }
      });
    } else if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleTopicsChange = (e) => {
    setTopicsInput(e.target.value);
    const topicsArray = e.target.value
      .split(',')
      .map(topic => topic.trim())
      .filter(topic => topic !== '');

    setFormData({
      ...formData,
      topics: topicsArray
    });
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // In a real app, you would upload this to a server and get a URL back
      // For now, we'll just create a local URL for preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
        setFormData({
          ...formData,
          image: reader.result
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (formData.topics.length === 0) {
      newErrors.topics = 'At least one topic is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Format the data for the API
      const speakerData = {
        name: formData.name,
        email: formData.email,
        title: formData.title,
        bio: formData.bio,
        topics: formData.topics,
        image: formData.image,
        verified: formData.verified,
        social: {
          linkedin: formData.socialLinks.linkedin,
          twitter: formData.socialLinks.twitter,
          website: formData.socialLinks.website
        }
      };

      if (isEditing) {
        await speakerService.updateSpeaker(speaker.id, speakerData);
        if (onSuccess) {
          onSuccess('Speaker updated successfully');
        }
      } else {
        await speakerService.registerSpeaker(speakerData);
        if (onSuccess) {
          onSuccess('Speaker added successfully');
        } else {
          navigate('/dashboard/projects/speakers');
        }
      }
    } catch (error) {
      setErrors({
        submit: error.message || 'Failed to save speaker. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 max-w-5xl mx-auto">
      <h2 className="text-xl font-semibold mb-6">{isEditing ? 'Edit Speaker' : 'Add New Speaker'}</h2>

      {errors.submit && (
        <div className="mb-4 p-3 bg-red-50 border-l-4 border-red-500 text-red-700">
          {errors.submit}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={`w-full p-2 border rounded-lg ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full p-2 border rounded-lg ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className={`w-full p-2 border rounded-lg ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="e.g. CEO at Company, Professor of AI"
              />
              {errors.title && <p className="mt-1 text-sm text-red-500">{errors.title}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Topics <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="topics"
                value={topicsInput}
                onChange={handleTopicsChange}
                className={`w-full p-2 border rounded-lg ${errors.topics ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="AI, Machine Learning, Web3 (comma separated)"
              />
              {errors.topics && <p className="mt-1 text-sm text-red-500">{errors.topics}</p>}

              {formData.topics.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-2">
                  {formData.topics.map((topic, index) => (
                    <span key={index} className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                      {topic}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Profile Image
              </label>
              <div className="flex items-center space-x-4">
                <div className="w-24 h-24 border border-gray-300 rounded-lg overflow-hidden flex items-center justify-center bg-gray-50">
                  {previewImage ? (
                    <img
                      src={previewImage}
                      alt="Speaker preview"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.name || 'Speaker')}&background=412D6C&color=fff&size=256`;
                      }}
                    />
                  ) : (
                    <div className="text-gray-400">No image</div>
                  )}
                </div>
                <div>
                  <label className="cursor-pointer bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg flex items-center space-x-2 text-sm">
                    <FaUpload />
                    <span>Upload Image</span>
                    <input
                      type="file"
                      className="hidden"
                      accept="image/*"
                      onChange={handleImageChange}
                    />
                  </label>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Bio
              </label>
              <textarea
                name="bio"
                value={formData.bio}
                onChange={handleChange}
                rows="4"
                className="w-full p-2 border border-gray-300 rounded-lg"
                placeholder="Brief biography of the speaker"
              ></textarea>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                LinkedIn URL
              </label>
              <input
                type="url"
                name="social_linkedin"
                value={formData.socialLinks.linkedin}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-lg"
                placeholder="https://linkedin.com/in/username"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Twitter URL
              </label>
              <input
                type="url"
                name="social_twitter"
                value={formData.socialLinks.twitter}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-lg"
                placeholder="https://twitter.com/username"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Website
              </label>
              <input
                type="url"
                name="social_website"
                value={formData.socialLinks.website}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-lg"
                placeholder="https://example.com"
              />
            </div>

            {isEditing && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="verified"
                  name="verified"
                  checked={formData.verified}
                  onChange={handleChange}
                  className="h-4 w-4 text-[#412D6C] focus:ring-[#412D6C] border-gray-300 rounded"
                />
                <label htmlFor="verified" className="ml-2 block text-sm text-gray-700">
                  Verified Speaker
                </label>
              </div>
            )}
          </div>
        </div>

        <div className="mt-8 flex justify-between">
          <div></div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onCancel || (() => navigate('/dashboard/projects/speakers'))}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
              disabled={isSubmitting}
            >
              <FaTimes className="text-sm" />
              <span>Cancel</span>
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center space-x-2"
              disabled={isSubmitting}
            >
              <FaSave className="text-sm" />
              <span>{isSubmitting ? 'Saving...' : 'Save Speaker'}</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default SpeakerForm;
