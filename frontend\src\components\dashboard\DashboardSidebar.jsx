import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const DashboardSidebar = () => {
  const location = useLocation();
  
  const departments = [
    { name: 'CEO Dashboard', path: '/dashboard/ceo', icon: '👑' },
    { name: 'Operations', path: '/dashboard/operations', icon: '⚙️' },
    { name: 'HR', path: '/dashboard/hr', icon: '👥' },
    { name: 'Growth', path: '/dashboard/growth', icon: '📈' },
    { name: 'Marketing', path: '/dashboard/marketing', icon: '📣' },
    { name: 'Accounts', path: '/dashboard/accounts', icon: '💰' },
    { name: 'Ambassadors', path: '/dashboard/ambassadors', icon: '🤝' },
    { name: 'Engineering', path: '/dashboard/engineering', icon: '💻' },
    { name: 'Projects', path: '/dashboard/projects', icon: '📋' }, // New Projects department
  ];

  return (
    <div className="w-64 bg-white shadow-md min-h-screen">
      <div className="p-6">
        <h2 className="text-xl font-semibold text-gray-800">Departments</h2>
      </div>
      <nav className="mt-2">
        {departments.map((dept) => (
          <Link
            key={dept.path}
            to={dept.path}
            className={`flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100 hover:text-[#412D6C] transition-colors ${
              location.pathname === dept.path ? 'bg-gray-100 text-[#412D6C] border-r-4 border-[#412D6C]' : ''
            }`}
          >
            <span className="mr-3">{dept.icon}</span>
            <span>{dept.name}</span>
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default DashboardSidebar;
