import React from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * A stat card component that displays a metric with an icon and trend
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Card title
 * @param {string} props.value - Metric value
 * @param {string} props.icon - Emoji or icon
 * @param {string} props.trend - Trend direction ('up' or 'down')
 * @param {string} props.trendValue - Trend percentage value
 * @param {string} props.linkTo - Route to navigate to on click
 * @returns {React.ReactElement} Stat card component
 */
const StatCard = ({ title, value, icon, trend, trendValue, linkTo }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (linkTo) {
      navigate(linkTo);
    }
  };

  return (
    <div
      className={`bg-white/30 backdrop-blur-xl p-6 rounded-xl shadow-lg border border-white/20
        ${linkTo ? 'cursor-pointer hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1' : ''}`}
      onClick={handleClick}
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-gray-600 text-sm font-medium">{title}</h3>
          <p className="text-2xl font-bold text-[#412D6C] mt-2">{value}</p>
        </div>
        <div className={`p-3 rounded-full bg-[#412D6C]/10`}>
          {icon}
        </div>
      </div>
      {trend && (
        <div className={`flex items-center mt-4 text-sm ${
          trend === 'up' ? 'text-green-600' : 'text-red-600'
        }`}>
          {trend === 'up' ? '↑' : '↓'} {trendValue}%
        </div>
      )}
    </div>
  );
};

export default StatCard;
