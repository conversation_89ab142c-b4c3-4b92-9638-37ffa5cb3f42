import React from 'react';

const ScholarshipDetails = ({ scholarship, onClose }) => {
  // Status color mapping
  const statusColors = {
    'Approved': 'bg-green-100 text-green-800 border-green-200',
    'Rejected': 'bg-red-100 text-red-800 border-red-200',
    'Under Review': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'Pending': 'bg-blue-100 text-blue-800 border-blue-200'
  };
  
  // Get status color or default
  const statusColor = statusColors[scholarship.status] || 'bg-gray-100 text-gray-800 border-gray-200';
  
  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-auto">
        {/* Header */}
        <div className="bg-[#412D6C] text-white p-6 rounded-t-xl flex justify-between items-center">
          <h3 className="text-xl font-bold">Scholarship Details</h3>
          <button 
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          <div className="flex flex-col md:flex-row md:items-start gap-6">
            {/* Left column - Basic info */}
            <div className="flex-1">
              <div className="mb-6">
                <div className="text-sm text-gray-500 mb-1">Application ID</div>
                <div className="text-lg font-medium">#{scholarship.id}</div>
              </div>
              
              <div className="mb-6">
                <div className="text-sm text-gray-500 mb-1">Program</div>
                <div className="text-lg font-medium">{scholarship.program}</div>
              </div>
              
              <div className="mb-6">
                <div className="text-sm text-gray-500 mb-1">Application Date</div>
                <div className="text-lg font-medium">{scholarship.date}</div>
              </div>
              
              <div className="mb-6">
                <div className="text-sm text-gray-500 mb-1">Status</div>
                <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${statusColor}`}>
                  {scholarship.status}
                </div>
              </div>
            </div>
            
            {/* Right column - Scores and details */}
            <div className="flex-1">
              <div className="mb-6">
                <div className="text-sm text-gray-500 mb-1">Quiz Score</div>
                <div className="text-lg font-medium">{scholarship.quizScore}</div>
              </div>
              
              <div className="mb-6">
                <div className="text-sm text-gray-500 mb-1">Interview Score</div>
                <div className="text-lg font-medium">{scholarship.interviewScore || 'N/A'}</div>
              </div>
              
              <div className="mb-6">
                <div className="text-sm text-gray-500 mb-1">Scholarship Amount</div>
                <div className="text-lg font-medium">{scholarship.amount || 'To be determined'}</div>
              </div>
              
              <div className="mb-6">
                <div className="text-sm text-gray-500 mb-1">Decision Date</div>
                <div className="text-lg font-medium">{scholarship.decisionDate || 'Pending'}</div>
              </div>
            </div>
          </div>
          
          {/* Timeline */}
          <div className="mt-8">
            <h4 className="text-lg font-medium mb-4">Application Timeline</h4>
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-gray-200"></div>
              
              {/* Timeline events */}
              <div className="space-y-6">
                <div className="flex">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-green-500 z-10 mt-1"></div>
                  <div className="ml-4">
                    <div className="font-medium">Application Submitted</div>
                    <div className="text-sm text-gray-500">{scholarship.date}</div>
                  </div>
                </div>
                
                <div className="flex">
                  <div className={`flex-shrink-0 w-6 h-6 rounded-full ${
                    scholarship.status === 'Under Review' || scholarship.status === 'Approved' || scholarship.status === 'Rejected'
                      ? 'bg-green-500' 
                      : 'bg-gray-300'
                  } z-10 mt-1`}></div>
                  <div className="ml-4">
                    <div className="font-medium">Application Review</div>
                    <div className="text-sm text-gray-500">
                      {scholarship.reviewDate || 'Pending'}
                    </div>
                  </div>
                </div>
                
                <div className="flex">
                  <div className={`flex-shrink-0 w-6 h-6 rounded-full ${
                    scholarship.status === 'Approved' || scholarship.status === 'Rejected'
                      ? 'bg-green-500' 
                      : 'bg-gray-300'
                  } z-10 mt-1`}></div>
                  <div className="ml-4">
                    <div className="font-medium">Decision Made</div>
                    <div className="text-sm text-gray-500">
                      {scholarship.decisionDate || 'Pending'}
                    </div>
                  </div>
                </div>
                
                {scholarship.status === 'Approved' && (
                  <div className="flex">
                    <div className={`flex-shrink-0 w-6 h-6 rounded-full ${
                      scholarship.disbursementDate ? 'bg-green-500' : 'bg-gray-300'
                    } z-10 mt-1`}></div>
                    <div className="ml-4">
                      <div className="font-medium">Funds Disbursed</div>
                      <div className="text-sm text-gray-500">
                        {scholarship.disbursementDate || 'Scheduled'}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Feedback section */}
          {scholarship.feedback && (
            <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <h4 className="text-lg font-medium mb-2">Feedback</h4>
              <p className="text-gray-700">{scholarship.feedback}</p>
            </div>
          )}
        </div>
        
        {/* Actions */}
        <div className="p-6 border-t border-gray-200 flex justify-end">
          {scholarship.status === 'Rejected' && (
            <button className="px-4 py-2 bg-[#412D6C] text-white rounded-lg hover:bg-[#362659] transition-colors mr-4">
              Apply Again
            </button>
          )}
          
          {scholarship.status === 'Under Review' && (
            <button className="px-4 py-2 border border-[#412D6C] text-[#412D6C] rounded-lg hover:bg-[#412D6C]/5 transition-colors">
              Update Application
            </button>
          )}
          
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ScholarshipDetails;
