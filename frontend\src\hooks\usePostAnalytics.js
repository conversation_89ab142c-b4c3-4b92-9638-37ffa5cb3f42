import { useEffect, useState } from 'react';
import { analyticsService, blogService } from '../services/api';

const usePostAnalytics = (postId) => {
  const [analytics, setAnalytics] = useState({
    startTime: Date.now(),
    scrollDepth: 0,
    timeOnPage: 0,
    interactions: 0,
    interactionTypes: {},
    views: 0,
    likes: 0,
    shares: 0,
    deviceInfo: {
      userAgent: navigator.userAgent,
      language: navigator.language,
      screenSize: `${window.screen.width}x${window.screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    },
    referrer: document.referrer,
    utmParams: Object.fromEntries(new URLSearchParams(window.location.search))
  });

  const [interactions, setInteractions] = useState({
    hasLiked: false,
    hasBookmarked: false
  });

  // Track post view and interactions on component mount
  useEffect(() => {
    if (postId) {
      // Check if user has already interacted with this post
      const storedInteractions = localStorage.getItem(`blog_interactions_${postId}`);
      if (storedInteractions) {
        setInteractions(JSON.parse(storedInteractions));
      }

      // Track view in backend
      trackView();
    }

    let interval;
    const updateTimeSpent = () => {
      setAnalytics(prev => ({
        ...prev,
        timeOnPage: Math.floor((Date.now() - prev.startTime) / 1000)
      }));
    };

    const trackScrollDepth = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollTop = window.scrollY;
      const scrollPercentage = (scrollTop / (documentHeight - windowHeight)) * 100;

      setAnalytics(prev => ({
        ...prev,
        scrollDepth: Math.round(scrollPercentage)
      }));
    };

    // Start tracking
    interval = setInterval(updateTimeSpent, 1000);
    window.addEventListener('scroll', trackScrollDepth);

    // Cleanup
    return () => {
      clearInterval(interval);
      window.removeEventListener('scroll', trackScrollDepth);

      // Send analytics data to backend
      analyticsService.postAnalytics({
        postId,
        ...analytics,
        endTime: Date.now()
      });
    };
  }, [postId]);

  // Track post view
  const trackView = async () => {
    try {
      // Check if this view has already been counted in this session
      const viewedPosts = JSON.parse(localStorage.getItem('viewed_posts') || '[]');
      if (!viewedPosts.includes(postId)) {
        // Add post to viewed posts
        viewedPosts.push(postId);
        localStorage.setItem('viewed_posts', JSON.stringify(viewedPosts));

        // Get auth token if available
        const token = localStorage.getItem('authToken');

        try {
          // Track view in backend by fetching the post (view count increments on backend)
          await blogService.getPostById(postId);
        } catch (error) {
          console.error('Error tracking view in backend:', error);
        }
      }
    } catch (error) {
      console.error('Error tracking view:', error);
    }
  };

  const trackInteraction = (type) => {
    setAnalytics(prev => ({
      ...prev,
      interactions: prev.interactions + 1,
      interactionTypes: {
        ...prev.interactionTypes,
        [type]: (prev.interactionTypes[type] || 0) + 1
      },
      lastInteraction: {
        type,
        timestamp: Date.now()
      }
    }));

    // Handle specific interactions
    handlePostAction(type);
  };

  // Handle post interactions (like, bookmark, share)
  const handlePostAction = async (action) => {
    try {
      // Check if user is authenticated
      const token = localStorage.getItem('authToken');
      if (!token && (action === 'like' || action === 'bookmark')) {
        console.warn('Authentication required for this action');
        return;
      }

      // Update local state
      if (action === 'like') {
        const newLikedState = !interactions.hasLiked;
        setInteractions(prev => ({
          ...prev,
          hasLiked: newLikedState
        }));

        // Update analytics
        setAnalytics(prev => ({
          ...prev,
          likes: prev.likes + (newLikedState ? 1 : -1)
        }));

        // Call backend API to like the post
        try {
          await blogService.likePost(postId);
        } catch (error) {
          console.error('Error liking post in backend:', error);
        }
      } else if (action === 'bookmark') {
        const newBookmarkedState = !interactions.hasBookmarked;
        setInteractions(prev => ({
          ...prev,
          hasBookmarked: newBookmarkedState
        }));
      } else if (action === 'share') {
        // Increment share count
        setAnalytics(prev => ({
          ...prev,
          shares: prev.shares + 1
        }));

        // Call backend API to track share
        try {
          await blogService.sharePost(postId, { platform: 'generic' });
        } catch (error) {
          console.error('Error sharing post in backend:', error);
        }
      }

      // Save interactions to localStorage
      localStorage.setItem(`blog_interactions_${postId}`, JSON.stringify(interactions));
    } catch (error) {
      console.error(`Error handling ${action}:`, error);
    }
  };

  return { analytics, interactions, trackInteraction };
};

export default usePostAnalytics;
