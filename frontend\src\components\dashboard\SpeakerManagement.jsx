import React, { useState, useEffect } from 'react';
import { FaCheck, FaTimes, FaLink, FaEdit, FaTrash } from 'react-icons/fa';
import speakerService from '../../services/speakerService';

const SpeakerManagement = () => {
  const [speakers, setSpeakers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSpeaker, setSelectedSpeaker] = useState(null);
  const [isEditing, setIsEditing] = useState(false);



  useEffect(() => {
    const fetchSpeakers = async () => {
      try {
        setLoading(true);

        // Call the API to get all speakers
        const response = await speakerService.getAllSpeakers();

        // Check if the response has data property and it's an array
        if (response && response.data && Array.isArray(response.data)) {
          // Add dateAdded property if it doesn't exist (using createdAt or current date)
          const speakersWithDates = response.data.map(speaker => ({
            ...speaker,
            dateAdded: speaker.dateAdded || speaker.createdAt || new Date().toISOString().split('T')[0],
            // Ensure topics is an array
            topics: speaker.topics && Array.isArray(speaker.topics) ? speaker.topics : ['General']
          }));

          setSpeakers(speakersWithDates);
        } else {
          // If the response structure is not as expected, set empty array
          setSpeakers([]);
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching speakers:', err);
        setError('Failed to load speakers. Please try again later.');
        setSpeakers([]);
        setLoading(false);
      }
    };

    fetchSpeakers();
  }, []);

  const handleVerify = async (id) => {
    try {
      // Call the API to verify/unverify the speaker
      await speakerService.verifySpeaker(id);

      // Update the local state
      setSpeakers(speakers.map(speaker =>
        speaker.id === id ? { ...speaker, verified: !speaker.verified } : speaker
      ));
    } catch (err) {
      console.error('Error verifying speaker:', err);
      alert('Failed to update verification status. Please try again.');
    }
  };

  const handleEdit = (speaker) => {
    setSelectedSpeaker(speaker);
    setIsEditing(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this speaker?')) {
      try {
        // Call the API to delete the speaker
        await speakerService.deleteSpeaker(id);

        // Update the local state
        setSpeakers(speakers.filter(speaker => speaker.id !== id));
      } catch (err) {
        console.error('Error deleting speaker:', err);
        alert('Failed to delete speaker. Please try again.');
      }
    }
  };

  const handleCopyUrl = (slug) => {
    const speakerUrl = `${window.location.origin}/speaker/${slug}`;
    navigator.clipboard.writeText(speakerUrl);
    alert('Speaker URL copied to clipboard!');
  };

  const handleCloseEdit = () => {
    setSelectedSpeaker(null);
    setIsEditing(false);
  };

  const handleSaveEdit = async (e) => {
    e.preventDefault();
    try {
      // Call the API to update the speaker
      await speakerService.updateSpeaker(selectedSpeaker.id, selectedSpeaker);

      // Update the local state
      setSpeakers(speakers.map(speaker =>
        speaker.id === selectedSpeaker.id ? selectedSpeaker : speaker
      ));

      setIsEditing(false);
      setSelectedSpeaker(null);
    } catch (err) {
      console.error('Error updating speaker:', err);
      alert('Failed to update speaker. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Speaker Management</h2>
          <p className="text-sm text-gray-600 mt-1">Add, edit, and verify speakers. Only verified speakers will show the verification badge.</p>
        </div>
        <button className="bg-[#412D6C] text-white px-4 py-2 rounded-lg hover:bg-[#362659] transition-colors">
          Add New Speaker
        </button>
      </div>

      {/* Speakers Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {speakers.length > 0 ? (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Speaker
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Topics
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date Added
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {speakers.map((speaker) => (
              <tr key={speaker.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      {speaker.image ? (
                        <img
                          className="h-10 w-10 rounded-full object-cover"
                          src={speaker.image}
                          alt={speaker.name || 'Speaker'}
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(speaker.name || 'Speaker')}&background=412D6C&color=fff&size=256`;
                          }}
                        />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-[#412D6C] text-white flex items-center justify-center text-sm font-bold">
                          {(speaker.name && speaker.name.charAt(0)) || 'S'}
                        </div>
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{speaker.name || 'Unknown Speaker'}</div>
                      <div className="text-sm text-gray-500">{speaker.email || 'No email provided'}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex flex-wrap gap-1">
                    {speaker.topics && Array.isArray(speaker.topics) && speaker.topics.length > 0 ? (
                      speaker.topics.map((topic, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-purple-100 text-[#412D6C] rounded-full">
                          {topic}
                        </span>
                      ))
                    ) : (
                      <span className="px-2 py-1 text-xs bg-purple-100 text-[#412D6C] rounded-full">
                        General
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    speaker.verified
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {speaker.verified ? 'Verified' : 'Pending'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {speaker.dateAdded || 'Unknown date'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-3">
                    <button
                      onClick={() => handleVerify(speaker.id)}
                      className={`${
                        speaker.verified
                          ? 'text-red-600 hover:text-red-900'
                          : 'text-green-600 hover:text-green-900'
                      }`}
                      title={speaker.verified ? 'Unverify' : 'Verify'}
                    >
                      {speaker.verified ? <FaTimes /> : <FaCheck />}
                    </button>
                    <button
                      onClick={() => handleCopyUrl(speaker.slug)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Copy Speaker URL"
                    >
                      <FaLink />
                    </button>
                    <button
                      onClick={() => handleEdit(speaker)}
                      className="text-indigo-600 hover:text-indigo-900"
                      title="Edit"
                    >
                      <FaEdit />
                    </button>
                    <button
                      onClick={() => handleDelete(speaker.id)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        ) : (
          <div className="text-center py-12">
            <div className="mb-6">
              <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-700 mb-2">No speakers found</h3>
            <p className="text-gray-500 text-lg mb-6">
              There are no speakers available at the moment.
            </p>
          </div>
        )}
      </div>

      {/* Edit Modal */}
      {isEditing && selectedSpeaker && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Edit Speaker</h3>

            <form onSubmit={handleSaveEdit}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={selectedSpeaker.name}
                  onChange={(e) => setSelectedSpeaker({...selectedSpeaker, name: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                  required
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={selectedSpeaker.email}
                  onChange={(e) => setSelectedSpeaker({...selectedSpeaker, email: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                  required
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  value={selectedSpeaker.title}
                  onChange={(e) => setSelectedSpeaker({...selectedSpeaker, title: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                  required
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Topics (comma separated)
                </label>
                <input
                  type="text"
                  value={selectedSpeaker.topics.join(', ')}
                  onChange={(e) => setSelectedSpeaker({
                    ...selectedSpeaker,
                    topics: e.target.value.split(',').map(topic => topic.trim())
                  })}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                  required
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Verification Status
                </label>
                <select
                  value={selectedSpeaker.verified ? 'verified' : 'pending'}
                  onChange={(e) => setSelectedSpeaker({
                    ...selectedSpeaker,
                    verified: e.target.value === 'verified'
                  })}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                >
                  <option value="verified">Verified</option>
                  <option value="pending">Pending</option>
                </select>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleCloseEdit}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#412D6C] text-white rounded-lg hover:bg-[#362659]"
                >
                  Save Changes
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default SpeakerManagement;
