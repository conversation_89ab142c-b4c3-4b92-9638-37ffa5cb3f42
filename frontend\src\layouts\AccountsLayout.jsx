import React from 'react';
import { Outlet } from 'react-router-dom';
import ResponsiveDepartmentSidebar from '../components/dashboard/ResponsiveDepartmentSidebar';
import { departmentConfigs } from '../data/departmentData';
import MessagingWidget from '../components/messaging/MessagingWidget';

const AccountsLayout = () => {
  const { color, menuItems } = departmentConfigs.accounts;

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-teal-50 via-white to-teal-50">
      <ResponsiveDepartmentSidebar 
        menuItems={menuItems} 
        department="Accounts"
        departmentColor={color}
      />
      
      <div className="lg:ml-64 flex-1 p-6">
        <Outlet />
        <MessagingWidget />
      </div>
    </div>
  );
};

export default AccountsLayout; 