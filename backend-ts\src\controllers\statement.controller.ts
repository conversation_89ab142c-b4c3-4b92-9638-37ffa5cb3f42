import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Statement, { IStatement } from '../models/statement.model';
import Transaction, { TransactionSource } from '../models/transaction.model';
import StatementData from '../models/statementData.model';
import { processAndStoreStatementData, getStatementData } from '../services/statementData.service';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import os from 'os';

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'uploads');
try {
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('Created uploads directory:', uploadsDir);
  }
} catch (err) {
  console.error('Error creating uploads directory:', err);
}

// Configure multer for file uploads - use OS temp directory as fallback
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Try to use our uploads directory first
    if (fs.existsSync(uploadsDir) && fs.accessSync(uploadsDir, fs.constants.W_OK)) {
      cb(null, uploadsDir);
    } else {
      // Fallback to OS temp directory
      const tempDir = os.tmpdir();
      console.log('Using OS temp directory for uploads:', tempDir);
      cb(null, tempDir);
    }
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    const allowedExtensions = ['.pdf', '.csv', '.xlsx', '.xls'];
    const ext = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF, CSV, and Excel files are allowed.'));
    }
  }
}).single('file');

/**
 * Get all statements
 * @route GET /api/statements
 */
export const getAllStatements = async (req: Request, res: Response): Promise<void> => {
  try {
    const startTime = Date.now(); // Track performance

    // Get user ID from authenticated user
    const userId = req.user.sub;

    // Get pagination parameters from query string
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    // Get filter parameters
    const statementType = req.query.type as string;
    const bankName = req.query.bank as string;
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;

    // Build query object
    const query: any = { userId };

    if (statementType) {
      query.statement_type = statementType;
    }

    if (bankName) {
      query.bankName = { $regex: bankName, $options: 'i' }; // Case-insensitive search
    }

    // Date range filter
    if (startDate || endDate) {
      query.statement_date = {};
      if (startDate) {
        query.statement_date.$gte = new Date(startDate);
      }
      if (endDate) {
        query.statement_date.$lte = new Date(endDate);
      }
    }

    // Use lean() for faster query execution when we don't need a Mongoose document
    // Only select fields we need to display in the list
    const statements = await Statement.find(query)
      .select('_id name bankName accountNumber statement_type statement_date startDate endDate startBalance endBalance createdAt')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const totalCount = await Statement.countDocuments(query);

    console.log(`Fetched ${statements.length} statements in ${Date.now() - startTime}ms`);

    res.status(200).json({
      status: true,
      count: totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
      data: statements,
      fetchTime: Date.now() - startTime
    });
  } catch (error) {
    console.error('Error getting statements:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get statement by ID
 * @route GET /api/statements/:id
 */
export const getStatementById = async (req: Request, res: Response): Promise<void> => {
  try {
    const startTime = Date.now(); // Track performance
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid statement ID' });
      return;
    }

    // Find statement by ID and user ID - use lean() for faster query
    const statement = await Statement.findOne({
      _id: id,
      userId,
    }).lean();

    if (!statement) {
      res.status(404).json({ message: 'Statement not found' });
      return;
    }

    // Get transaction count for this statement
    const transactionCount = await Transaction.countDocuments({
      statementId: id,
      userId,
    });

    // Check if statement data exists and is completed
    const statementData = await StatementData.findOne(
      { statementId: id, status: 'completed' },
      { status: 1, summary: 1 } // Only fetch the fields we need
    ).lean();

    // Combine data for response
    const responseData = {
      ...statement,
      transactionCount,
      processingStatus: statementData?.status || 'unknown',
      // Include additional summary data if available
      ...(statementData?.summary ? {
        summaryData: statementData.summary
      } : {})
    };

    console.log(`Fetched statement ${id} in ${Date.now() - startTime}ms`);

    res.status(200).json({
      status: true,
      data: responseData,
      fetchTime: Date.now() - startTime
    });
  } catch (error) {
    console.error('Error getting statement:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Delete statement
 * @route DELETE /api/statements/:id
 */
export const deleteStatement = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid statement ID' });
      return;
    }

    // Find and delete statement
    const statement = await Statement.findOneAndDelete({
      _id: id,
      userId,
    });

    if (!statement) {
      res.status(404).json({ message: 'Statement not found' });
      return;
    }

    // Delete all transactions associated with this statement
    await Transaction.deleteMany({
      statementId: id,
      userId,
    });

    // Delete statement data if it exists
    await StatementData.deleteOne({
      statementId: id,
      userId,
    });

    res.status(200).json({
      status: true,
      message: 'Statement and associated data deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting statement:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Upload statement
 * @route POST /api/statements/upload
 */
export const uploadStatement = async (req: Request, res: Response): Promise<void> => {
  // Use multer middleware to handle file upload
  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const { name, statement_type, statement_date } = req.body;

      if (!name) {
        return res.status(400).json({ message: 'Statement name is required' });
      }

      const userId = req.user.sub;

      // Create new statement with basic info
      const statement = new Statement({
        name,
        statement_type: statement_type || 'bank',
        statement_date: statement_date ? new Date(statement_date) : new Date(),
        fileName: req.file.filename,
        filePath: req.file.path,
        userId,
        // Set default values for required fields in the schema
        bankName: 'Pending Processing',
        accountNumber: 'Pending Processing',
        startDate: new Date(),
        endDate: new Date(),
        startBalance: 0,
        endBalance: 0
      });

      // Save statement to database
      await statement.save();

      // Start processing the statement data in the background
      const statementId = (statement._id as mongoose.Types.ObjectId).toString();
      processAndStoreStatementData(statementId, userId)
        .then(() => {
          console.log(`Statement data processing completed for statement ${statementId}`);
        })
        .catch(err => {
          console.error(`Error processing statement data for statement ${statementId}:`, err);
        });

      res.status(201).json({
        status: true,
        message: 'Statement uploaded successfully',
        data: {
          ...statement.toObject(),
          processingStatus: 'processing'
        },
      });
    } catch (error) {
      console.error('Error uploading statement:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });
};

/**
 * Parse statement file (CSV, Excel, etc.)
 * @route POST /api/statements/parse
 */
export const parseStatementFile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { statementId } = req.body;
    const userId = req.user.sub;

    if (!statementId || !mongoose.Types.ObjectId.isValid(statementId)) {
      res.status(400).json({ message: 'Valid statement ID is required' });
      return;
    }

    // Check if statement exists and belongs to user
    const statement = await Statement.findOne({
      _id: statementId,
      userId,
    });

    if (!statement) {
      res.status(404).json({ message: 'Statement not found' });
      return;
    }

    // Process and store statement data
    const statementData = await processAndStoreStatementData(statementId, userId);

    res.status(200).json({
      status: true,
      message: 'Statement file parsed successfully',
      data: {
        id: statement._id,
        name: statement.name,
        status: statementData.status,
        summary: statementData.summary || {
          startDate: statement.startDate,
          endDate: statement.endDate,
          startBalance: statement.startBalance,
          endBalance: statement.endBalance,
          totalCredits: 0,
          totalDebits: 0,
          transactionCount: 0,
        },
      },
    });
  } catch (error) {
    console.error('Error parsing statement file:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Download statement file
 * @route GET /api/statements/:id/download
 */
export const downloadStatementFile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid statement ID' });
      return;
    }

    const statement = await Statement.findById(id);

    if (!statement) {
      res.status(404).json({ message: 'Statement not found' });
      return;
    }

    // Check if file exists
    if (!statement.filePath || !fs.existsSync(statement.filePath)) {
      // Try to find the file in the uploads directory using the fileName
      const possiblePaths = [
        path.join(uploadsDir, statement.fileName),
        path.join(os.tmpdir(), statement.fileName)
      ];

      let fileFound = false;
      for (const possiblePath of possiblePaths) {
        if (fs.existsSync(possiblePath)) {
          // Update the statement with the correct file path
          statement.filePath = possiblePath;
          await statement.save();
          fileFound = true;
          console.log(`Found statement file at ${possiblePath}`);
          break;
        }
      }

      if (!fileFound) {
        console.error(`Statement file not found: ${statement.fileName}`);
        res.status(404).json({ message: 'Statement file not found' });
        return;
      }
    }

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${statement.fileName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // Stream the file
    const fileStream = fs.createReadStream(statement.filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error downloading statement file:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get statement preview
 * @route GET /api/statements/:id/preview
 */
export const getStatementPreview = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const startTime = Date.now(); // Track performance

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid statement ID' });
      return;
    }

    // Get statement with only necessary fields
    const statement = await Statement.findById(id).select('_id name fileName filePath userId createdAt');

    if (!statement) {
      res.status(404).json({ message: 'Statement not found' });
      return;
    }

    // Cast statement to IStatement to fix TypeScript errors
    const typedStatement = statement as IStatement;
    const statementId = (typedStatement._id as mongoose.Types.ObjectId).toString();
    const userId = typedStatement.userId.toString();

    console.log(`Getting preview for statement ${statementId}`);

    // STEP 1: Try to get statement data from MongoDB first (FAST PATH)
    // Define projection to only fetch fields we need
    const projection = {
      status: 1,
      content: 1,
      metadata: 1,
      summary: 1,
      insights: 1
    };

    const statementData = await getStatementData(statementId, projection);

    // If we have completed data in the database, return it immediately
    if (statementData && statementData.status === 'completed') {
      console.log(`Found completed statement data in database for ${statementId}, returning immediately`);
      console.log(`Database fetch time: ${Date.now() - startTime}ms`);

      res.status(200).json({
        status: true,
        data: {
          id: statement._id,
          name: statement.name,
          fileName: statement.fileName,
          fileSize: statementData.metadata.fileSize,
          fileType: statementData.metadata.fileType,
          uploadDate: statement.createdAt,
          preview: statement.name, // Use statement name as preview
          content: statementData.content,
          metadata: statementData.metadata,
          summary: statementData.summary,
          insights: statementData.insights || {},
          processingStatus: 'completed',
          fetchTime: Date.now() - startTime
        }
      });
      return;
    }

    // STEP 2: If we don't have data or processing failed, check if file exists and process it immediately
    // Only check file if we need to process it
    let filePath = typedStatement.filePath;
    if (!filePath || !fs.existsSync(filePath)) {
      // Try to find the file in the uploads directory using the fileName
      const possiblePaths = [
        path.join(uploadsDir, typedStatement.fileName),
        path.join(os.tmpdir(), typedStatement.fileName)
      ];

      let fileFound = false;
      for (const possiblePath of possiblePaths) {
        if (fs.existsSync(possiblePath)) {
          // Update the statement with the correct file path
          filePath = possiblePath;
          typedStatement.filePath = possiblePath;
          await typedStatement.save();
          fileFound = true;
          console.log(`Found statement file at ${possiblePath}`);
          break;
        }
      }

      if (!fileFound) {
        console.error(`Statement file not found: ${typedStatement.fileName}`);
        res.status(404).json({ message: 'Statement file not found' });
        return;
      }
    }

    // STEP 3: Process the file immediately (Python/Rust processing is very fast)
    console.log(`Processing statement data for ${statementId} immediately`);

    try {
      // Process and store the data synchronously
      await processAndStoreStatementData(statementId, userId);

      // Fetch the newly processed data
      const freshData = await getStatementData(statementId, projection);

      if (freshData && freshData.status === 'completed') {
        console.log(`Successfully processed statement data for ${statementId}`);
        console.log(`Total processing time: ${Date.now() - startTime}ms`);

        res.status(200).json({
          status: true,
          data: {
            id: statement._id,
            name: statement.name,
            fileName: statement.fileName,
            fileSize: freshData.metadata.fileSize,
            fileType: freshData.metadata.fileType,
            uploadDate: statement.createdAt,
            preview: statement.name,
            content: freshData.content,
            metadata: freshData.metadata,
            summary: freshData.summary,
            insights: freshData.insights || {},
            processingStatus: 'completed',
            fetchTime: Date.now() - startTime
          }
        });
        return;
      } else {
        // If processing failed or is incomplete, return an error
        console.error(`Failed to process statement data for ${statementId}`);

        res.status(500).json({
          status: false,
          message: 'Failed to process statement data',
          data: {
            id: statement._id,
            name: statement.name,
            fileName: statement.fileName,
            fileType: path.extname(statement.fileName).toLowerCase().substring(1),
            uploadDate: statement.createdAt,
            preview: 'Failed to process statement data',
            processingStatus: 'failed',
            fetchTime: Date.now() - startTime
          }
        });
      }
    } catch (processingError) {
      console.error(`Error processing statement data for ${statementId}:`, processingError);

      res.status(500).json({
        status: false,
        message: 'Error processing statement data',
        data: {
          id: statement._id,
          name: statement.name,
          fileName: statement.fileName,
          fileType: path.extname(statement.fileName).toLowerCase().substring(1),
          uploadDate: statement.createdAt,
          preview: 'Error processing statement data',
          processingStatus: 'failed',
          fetchTime: Date.now() - startTime,
          error: processingError instanceof Error ? processingError.message : String(processingError)
        }
      });
    }
  } catch (error) {
    console.error('Error getting statement preview:', error);
    res.status(500).json({ message: 'Server error' });
  }
};




