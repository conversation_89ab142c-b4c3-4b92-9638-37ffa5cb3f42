import React from 'react';
import Popup from './Popup';

/**
 * AlertPopup component
 * 
 * A reusable component for creating alert popups with a single button.
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Title of the popup
 * @param {string} props.message - Message to display in the popup
 * @param {string} props.buttonText - Text for the button (default: "OK")
 * @param {Function} props.onClose - Function to call when the button is clicked
 * @param {string} props.type - Type of alert: 'info', 'success', 'warning', 'error' (default: 'info')
 * @param {boolean} props.closeOnOverlayClick - Whether to close the popup when the overlay is clicked (default: true)
 */
const AlertPopup = ({ 
  title, 
  message, 
  buttonText = "OK", 
  onClose,
  type = 'info',
  closeOnOverlayClick = true
}) => {
  // Define styles based on alert type
  const getAlertStyles = () => {
    switch (type) {
      case 'success':
        return {
          icon: '✓',
          iconClass: 'bg-green-100 text-green-600',
          buttonClass: 'bg-green-600 hover:bg-green-700'
        };
      case 'warning':
        return {
          icon: '⚠',
          iconClass: 'bg-yellow-100 text-yellow-600',
          buttonClass: 'bg-yellow-600 hover:bg-yellow-700'
        };
      case 'error':
        return {
          icon: '✕',
          iconClass: 'bg-red-100 text-red-600',
          buttonClass: 'bg-red-600 hover:bg-red-700'
        };
      case 'info':
      default:
        return {
          icon: 'ℹ',
          iconClass: 'bg-blue-100 text-blue-600',
          buttonClass: 'bg-[#412D6C] hover:bg-[#362659]'
        };
    }
  };

  const { icon, iconClass, buttonClass } = getAlertStyles();

  return (
    <Popup 
      title={title} 
      onClose={onClose} 
      closeOnOverlayClick={closeOnOverlayClick}
    >
      <div className="flex items-start mb-6">
        <div className={`flex-shrink-0 p-2 rounded-full mr-3 ${iconClass}`}>
          <span className="text-xl">{icon}</span>
        </div>
        <p className="text-gray-700">{message}</p>
      </div>
      
      <div className="flex justify-end">
        <button
          onClick={onClose}
          className={`px-4 py-2 rounded-md text-white transition-colors ${buttonClass}`}
        >
          {buttonText}
        </button>
      </div>
    </Popup>
  );
};

export default AlertPopup;
