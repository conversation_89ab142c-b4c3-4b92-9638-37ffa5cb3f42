import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Reconciliation, { IReconciliation, ReconciliationStatus } from '../models/reconciliation.model';
import Transaction, { TransactionStatus } from '../models/transaction.model';

/**
 * Get all reconciliations
 * @route GET /api/reconciliations
 */
export const getAllReconciliations = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get user ID from authenticated user
    const userId = req.user.sub;

    // Find all reconciliations for this user
    const reconciliations = await Reconciliation.find({ userId }).sort({ createdAt: -1 });

    res.status(200).json({
      status: true,
      count: reconciliations.length,
      data: reconciliations,
    });
  } catch (error) {
    console.error('Error getting reconciliations:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get reconciliation by ID
 * @route GET /api/reconciliations/:id
 */
export const getReconciliationById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid reconciliation ID' });
      return;
    }

    // Find reconciliation by ID and user ID
    const reconciliation = await Reconciliation.findOne({
      _id: id,
      userId,
    });

    if (!reconciliation) {
      res.status(404).json({ message: 'Reconciliation not found' });
      return;
    }

    res.status(200).json({
      status: true,
      data: reconciliation,
    });
  } catch (error) {
    console.error('Error getting reconciliation:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Create a new reconciliation
 * @route POST /api/reconciliations
 */
export const createReconciliation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, description, startDate, endDate } = req.body;
    const userId = req.user.sub;

    // Create new reconciliation
    const reconciliation = new Reconciliation({
      name,
      description,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      userId,
    });

    // Save reconciliation to database
    await reconciliation.save();

    // Get transaction counts for this date range
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    const totalTransactions = await Transaction.countDocuments({
      userId,
      date: { $gte: startDateObj, $lte: endDateObj },
    });

    const matchedTransactions = await Transaction.countDocuments({
      userId,
      date: { $gte: startDateObj, $lte: endDateObj },
      status: TransactionStatus.MATCHED,
    });

    const unmatchedTransactions = totalTransactions - matchedTransactions;

    // Update reconciliation with transaction counts
    reconciliation.totalTransactions = totalTransactions;
    reconciliation.matchedTransactions = matchedTransactions;
    reconciliation.unmatchedTransactions = unmatchedTransactions;
    await reconciliation.save();

    res.status(201).json({
      status: true,
      message: 'Reconciliation created successfully',
      data: reconciliation,
    });
  } catch (error) {
    console.error('Error creating reconciliation:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get transactions for a reconciliation
 * @route GET /api/reconciliations/:id/transactions
 */
export const getReconciliationTransactions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid reconciliation ID' });
      return;
    }

    // Find reconciliation by ID and user ID
    const reconciliation = await Reconciliation.findOne({
      _id: id,
      userId,
    });

    if (!reconciliation) {
      res.status(404).json({ message: 'Reconciliation not found' });
      return;
    }

    // Get transactions for this date range
    const transactions = await Transaction.find({
      userId,
      date: { $gte: reconciliation.startDate, $lte: reconciliation.endDate },
    })
      .sort({ date: -1 })
      .populate('statementId', 'name bankName')
      .populate('matchedWith', 'date description amount reference');

    res.status(200).json({
      status: true,
      count: transactions.length,
      data: transactions,
    });
  } catch (error) {
    console.error('Error getting reconciliation transactions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Complete a reconciliation
 * @route POST /api/reconciliations/:id/complete
 */
export const completeReconciliation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user.sub;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid reconciliation ID' });
      return;
    }

    // Find reconciliation by ID and user ID
    const reconciliation = await Reconciliation.findOne({
      _id: id,
      userId,
    });

    if (!reconciliation) {
      res.status(404).json({ message: 'Reconciliation not found' });
      return;
    }

    // Update reconciliation status
    reconciliation.status = ReconciliationStatus.COMPLETED;
    await reconciliation.save();

    // Update all matched transactions in this date range to reconciled
    await Transaction.updateMany(
      {
        userId,
        date: { $gte: reconciliation.startDate, $lte: reconciliation.endDate },
        status: TransactionStatus.MATCHED,
      },
      {
        status: TransactionStatus.RECONCILED,
      }
    );

    res.status(200).json({
      status: true,
      message: 'Reconciliation completed successfully',
      data: reconciliation,
    });
  } catch (error) {
    console.error('Error completing reconciliation:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
