"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStatementData = exports.processAndStoreStatementData = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const fileParser_1 = require("../utils/fileParser");
const statementData_model_1 = __importDefault(require("../models/statementData.model"));
const statement_model_1 = __importDefault(require("../models/statement.model"));
const path_1 = __importDefault(require("path"));
/**
 * Process and store statement data in MongoDB
 * @param statementId The ID of the statement
 * @param userId The ID of the user
 * @returns The stored statement data
 */
const processAndStoreStatementData = async (statementId, userId) => {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
    try {
        // Check if data already exists
        const existingData = await statementData_model_1.default.findOne({ statementId });
        if (existingData && existingData.status === 'completed') {
            console.log(`Statement data already processed for statement ${statementId}`);
            return existingData;
        }
        // Get the statement
        const statement = await statement_model_1.default.findById(statementId);
        if (!statement) {
            throw new Error(`Statement not found with ID ${statementId}`);
        }
        // Create or update statement data with processing status
        let statementData = existingData;
        if (!statementData) {
            statementData = new statementData_model_1.default({
                statementId: new mongoose_1.default.Types.ObjectId(statementId),
                userId: new mongoose_1.default.Types.ObjectId(userId),
                status: 'processing',
                content: {},
                metadata: {
                    fileType: path_1.default.extname(statement.fileName).toLowerCase().substring(1),
                    fileName: statement.fileName,
                    fileSize: 0, // Will be updated after parsing
                    uploadDate: statement.createdAt,
                },
            });
            await statementData.save();
        }
        else {
            statementData.status = 'processing';
            await statementData.save();
        }
        // Start processing time
        const startTime = Date.now();
        // Parse the file
        const fileData = await (0, fileParser_1.parseFile)(statement.filePath);
        // Extract and format the data
        const formattedData = formatStatementData(fileData);
        // Update statement data with the processed content
        statementData.content = formattedData;
        statementData.status = 'completed';
        statementData.metadata = {
            ...statementData.metadata,
            fileSize: fileData.metadata.fileSize || 0,
            processingTime: Date.now() - startTime,
            pageCount: fileData.content.pages || 0,
            rowCount: ((_a = fileData.summary) === null || _a === void 0 ? void 0 : _a.rowCount) || 0,
            columnCount: ((_b = fileData.summary) === null || _b === void 0 ? void 0 : _b.columnCount) || 0,
        };
        statementData.summary = {
            startDate: (_c = formattedData.summary) === null || _c === void 0 ? void 0 : _c.startDate,
            endDate: (_d = formattedData.summary) === null || _d === void 0 ? void 0 : _d.endDate,
            startBalance: (_e = formattedData.summary) === null || _e === void 0 ? void 0 : _e.startBalance,
            endBalance: (_f = formattedData.summary) === null || _f === void 0 ? void 0 : _f.endBalance,
            totalCredits: (_g = formattedData.summary) === null || _g === void 0 ? void 0 : _g.totalCredits,
            totalDebits: (_h = formattedData.summary) === null || _h === void 0 ? void 0 : _h.totalDebits,
            transactionCount: ((_j = formattedData.content) === null || _j === void 0 ? void 0 : _j.length) || 0,
        };
        statementData.insights = fileData.insights || {};
        // Save the updated statement data
        await statementData.save();
        // Update the statement with extracted metadata if it was pending processing
        if (statement.bankName === 'Pending Processing') {
            statement.bankName = formattedData.bankName || 'Unknown Bank';
            statement.accountNumber = formattedData.accountNumber || 'Unknown';
            statement.startDate = ((_k = formattedData.summary) === null || _k === void 0 ? void 0 : _k.startDate) || statement.startDate;
            statement.endDate = ((_l = formattedData.summary) === null || _l === void 0 ? void 0 : _l.endDate) || statement.endDate;
            statement.startBalance = ((_m = formattedData.summary) === null || _m === void 0 ? void 0 : _m.startBalance) || 0;
            statement.endBalance = ((_o = formattedData.summary) === null || _o === void 0 ? void 0 : _o.endBalance) || 0;
            await statement.save();
        }
        return statementData;
    }
    catch (error) {
        console.error('Error processing statement data:', error);
        // Update statement data with failed status
        const statementData = await statementData_model_1.default.findOne({ statementId });
        if (statementData) {
            statementData.status = 'failed';
            await statementData.save();
        }
        throw error;
    }
};
exports.processAndStoreStatementData = processAndStoreStatementData;
/**
 * Get statement data from MongoDB
 * @param statementId The ID of the statement
 * @returns The statement data
 */
const getStatementData = async (statementId) => {
    return statementData_model_1.default.findOne({ statementId });
};
exports.getStatementData = getStatementData;
/**
 * Format statement data for storage
 * @param fileData The parsed file data
 * @returns Formatted statement data
 */
const formatStatementData = (fileData) => {
    var _a, _b;
    // Extract bank name and account number
    const bankName = ((_a = fileData.content.bankInfo) === null || _a === void 0 ? void 0 : _a.bankName) || 'Unknown Bank';
    const accountNumber = ((_b = fileData.content.bankInfo) === null || _b === void 0 ? void 0 : _b.accountNumber) || 'Unknown';
    // Format content based on file type
    let content = [];
    let summary = {
        startDate: null,
        endDate: null,
        startBalance: 0,
        endBalance: 0,
        totalCredits: 0,
        totalDebits: 0,
    };
    // Handle Excel files
    if (fileData.metadata.fileType === 'xlsx' || fileData.metadata.fileType === 'xls') {
        // For Excel, extract data from the first sheet or the one with transactions
        const sheetNames = fileData.content.sheets || [];
        if (sheetNames.length > 0) {
            const firstSheetName = sheetNames[0];
            const sheetData = fileData.content.data[firstSheetName] || [];
            // Format the data according to the standardized structure
            content = formatExcelData(sheetData);
            // Calculate summary
            summary = calculateSummary(content);
        }
    }
    // Handle CSV files
    else if (fileData.metadata.fileType === 'csv') {
        // For CSV, extract data from rows
        const rows = fileData.content.rows || [];
        // Format the data according to the standardized structure
        content = formatCsvData(rows);
        // Calculate summary
        summary = calculateSummary(content);
    }
    // Handle PDF files
    else if (fileData.metadata.fileType === 'pdf') {
        // For PDF, extract data from tables
        const tables = fileData.content.tables || [];
        if (tables.length > 0) {
            // Format the data according to the standardized structure
            content = formatPdfData(tables);
            // Calculate summary
            summary = calculateSummary(content);
        }
    }
    return {
        bankName,
        accountNumber,
        content,
        summary,
        rawData: fileData.content,
    };
};
/**
 * Format Excel data
 * @param sheetData The Excel sheet data
 * @returns Formatted data
 */
const formatExcelData = (sheetData) => {
    // Map Excel data to standardized format
    return sheetData.map((row) => {
        // Try to map common column headers to standardized format
        const financialDate = row['Financial Date'] || row['Transaction Date'] || row['Date'] || '';
        const transactionDate = row['Transaction Date'] || row['Value Date'] || row['Date'] || '';
        const referenceNo = row['Reference No.'] || row['Reference'] || row['Ref'] || '';
        const instrumentNo = row['Instrument No.'] || row['Cheque No.'] || '';
        const narration = row['Narration'] || row['Description'] || row['Details'] || '';
        const username = row['Username'] || '';
        const debit = parseFloat(row['DR'] || row['Debit'] || row['Withdrawal'] || '0') || 0;
        const credit = parseFloat(row['CR'] || row['Credit'] || row['Deposit'] || '0') || 0;
        const balance = parseFloat(row['Avail. Bal'] || row['Balance'] || '0') || 0;
        const entryCode = row['Entry Code'] || '';
        return {
            'Financial Date': financialDate,
            'Transaction Date': transactionDate,
            'Reference No.': referenceNo,
            'Instrument No.': instrumentNo,
            'Narration': narration,
            'Username': username,
            'DR': debit,
            'CR': credit,
            'Avail. Bal': balance,
            'Entry Code': entryCode,
            // Store original row data for reference
            _original: row
        };
    });
};
/**
 * Format CSV data
 * @param rows The CSV rows
 * @returns Formatted data
 */
const formatCsvData = (rows) => {
    // Similar to Excel formatting
    return formatExcelData(rows);
};
/**
 * Format PDF data
 * @param tables The PDF tables
 * @returns Formatted data
 */
const formatPdfData = (tables) => {
    // Extract rows from all tables
    const allRows = [];
    tables.forEach(table => {
        if (table.rows && Array.isArray(table.rows)) {
            allRows.push(...table.rows);
        }
    });
    // Map PDF data to standardized format
    return allRows.map((row) => {
        // Try to map common column headers to standardized format
        const financialDate = row['EntryDate'] || '';
        const transactionDate = row['ValueDate'] || '';
        const narration = row['Details'] || '';
        const debit = parseFloat(row['Debit'] || '0') || 0;
        const credit = parseFloat(row['Credit'] || '0') || 0;
        const balance = parseFloat(row['Balance'] || '0') || 0;
        return {
            'Financial Date': financialDate,
            'Transaction Date': transactionDate,
            'Reference No.': '',
            'Instrument No.': '',
            'Narration': narration,
            'Username': '',
            'DR': debit,
            'CR': credit,
            'Avail. Bal': balance,
            'Entry Code': '',
            // Store original row data for reference
            _original: row
        };
    });
};
/**
 * Calculate summary from formatted data
 * @param content The formatted content
 * @returns Summary data
 */
const calculateSummary = (content) => {
    if (!content || content.length === 0) {
        return {
            startDate: null,
            endDate: null,
            startBalance: 0,
            endBalance: 0,
            totalCredits: 0,
            totalDebits: 0,
        };
    }
    // Sort by date
    const sortedContent = [...content].sort((a, b) => {
        const dateA = new Date(a['Financial Date'] || a['Transaction Date'] || '');
        const dateB = new Date(b['Financial Date'] || b['Transaction Date'] || '');
        return dateA.getTime() - dateB.getTime();
    });
    // Calculate summary
    const startDate = new Date(sortedContent[0]['Financial Date'] || sortedContent[0]['Transaction Date'] || '');
    const endDate = new Date(sortedContent[sortedContent.length - 1]['Financial Date'] || sortedContent[sortedContent.length - 1]['Transaction Date'] || '');
    const startBalance = parseFloat(sortedContent[0]['Avail. Bal'] || '0') - parseFloat(sortedContent[0]['CR'] || '0') + parseFloat(sortedContent[0]['DR'] || '0');
    const endBalance = parseFloat(sortedContent[sortedContent.length - 1]['Avail. Bal'] || '0');
    // Calculate totals
    let totalCredits = 0;
    let totalDebits = 0;
    sortedContent.forEach(row => {
        totalCredits += parseFloat(row['CR'] || '0') || 0;
        totalDebits += parseFloat(row['DR'] || '0') || 0;
    });
    return {
        startDate: !isNaN(startDate.getTime()) ? startDate : null,
        endDate: !isNaN(endDate.getTime()) ? endDate : null,
        startBalance,
        endBalance,
        totalCredits,
        totalDebits,
    };
};
