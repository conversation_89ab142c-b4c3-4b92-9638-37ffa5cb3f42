import mongoose, { Document, Schema } from 'mongoose';

// Transaction source enum
export enum TransactionSource {
  BANK = 'BANK',
  INTERNAL = 'INTERNAL',
}

// Transaction status enum
export enum TransactionStatus {
  UNMATCHED = 'UNMATCHED',
  MATCHED = 'MATCHED',
  RECONCILED = 'RECONCILED',
}

// Transaction interface
export interface ITransaction extends Document {
  date: Date;
  description: string;
  amount: number;
  reference: string;
  source: TransactionSource;
  status: TransactionStatus;
  statementId?: mongoose.Types.ObjectId;
  matchedWith?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Transaction schema
const transactionSchema = new Schema<ITransaction>(
  {
    date: {
      type: Date,
      required: [true, 'Transaction date is required'],
    },
    description: {
      type: String,
      required: [true, 'Transaction description is required'],
      trim: true,
    },
    amount: {
      type: Number,
      required: [true, 'Transaction amount is required'],
    },
    reference: {
      type: String,
      trim: true,
    },
    source: {
      type: String,
      enum: Object.values(TransactionSource),
      required: [true, 'Transaction source is required'],
    },
    status: {
      type: String,
      enum: Object.values(TransactionStatus),
      default: TransactionStatus.UNMATCHED,
    },
    statementId: {
      type: Schema.Types.ObjectId,
      ref: 'Statement',
    },
    matchedWith: {
      type: Schema.Types.ObjectId,
      ref: 'Transaction',
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
  },
  {
    timestamps: true,
  }
);

// Create and export Transaction model
export default mongoose.model<ITransaction>('Transaction', transactionSchema);
