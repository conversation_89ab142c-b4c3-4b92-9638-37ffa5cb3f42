"""
Authentication middleware for the Bank Reconciliation API.
"""

from functools import wraps
from flask import request, jsonify
import jwt
from config import config

def auth_required(f):
    """
    Authentication middleware decorator.
    Verifies JWT token and adds user to request context.
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        # For development/testing purposes, use a mock user if no token is provided
        if config['node_env'] == 'development' and not request.headers.get('Authorization'):
            # Mock user for development
            request.user = {
                'id': '6460f1a1b74d3e001c123456',  # Mock user ID
                'email': '<EMAIL>',
                'name': 'Development User',
                'sub': '6460f1a1b74d3e001c123456'  # Same as ID
            }
            print("Using mock user for development")
            return f(*args, **kwargs)

        # Get token from header
        auth_header = request.headers.get('Authorization')

        if not auth_header:
            return jsonify({'message': 'No token, authorization denied'}), 401

        try:
            # Extract token from Bearer format
            token = auth_header.split(' ')[1] if ' ' in auth_header else auth_header

            # Verify token
            decoded = jwt.decode(token, config['jwt_secret'], algorithms=['HS256'])

            # Add user to request context
            request.user = decoded

            # Ensure user has an ID
            if 'id' not in request.user and 'sub' in request.user:
                request.user['id'] = request.user['sub']

            return f(*args, **kwargs)
        except jwt.ExpiredSignatureError:
            return jsonify({'message': 'Token has expired'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': 'Token is not valid'}), 401
        except Exception as e:
            print(f"Auth error: {str(e)}")
            return jsonify({'message': 'Authentication error'}), 401

    return decorated

# List of public routes that don't require authentication
public_routes = [
    '/api/health',
    '/api/auth/login',
    '/api/auth/register'
]

def conditional_auth():
    """
    Conditional authentication middleware.
    Only applies auth middleware to protected routes.
    """
    # This will be implemented at the blueprint level in Flask
    pass
