import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const SpeakerSubNav = () => {
  const location = useLocation();
  const currentPath = location.pathname;

  const navItems = [
    { label: 'All Speakers', path: '/dashboard/projects/speakers' },
    { label: 'Verified Speakers', path: '/dashboard/projects/speakers/verified' },
    { label: 'Pending Speakers', path: '/dashboard/projects/speakers/pending' },
    { label: 'Add Speaker', path: '/dashboard/projects/speakers/add' },
  ];

  return (
    <div className="bg-white shadow-sm rounded-lg mb-6">
      <div className="flex overflow-x-auto justify-center">
        {navItems.map((item, index) => {
          const isActive =
            (item.path === '/dashboard/projects/speakers' && currentPath === '/dashboard/projects/speakers') ||
            (item.path !== '/dashboard/projects/speakers' && currentPath.startsWith(item.path));

          return (
            <Link
              key={index}
              to={item.path}
              className={`px-6 py-3 text-sm font-medium whitespace-nowrap ${
                isActive
                  ? 'text-[#412D6C] border-b-2 border-[#412D6C]'
                  : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'
              }`}
            >
              {item.label}
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default SpeakerSubNav;
