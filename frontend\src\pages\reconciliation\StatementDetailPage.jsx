import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  FiFileText,
  FiDownload,
  FiAlertCircle,
  FiArrowLeft,
  FiTrash2,
  FiCalendar,
  FiDollarSign,
  FiCreditCard,
  FiList,
  FiRefreshCw
} from 'react-icons/fi';
import { motion } from 'framer-motion';
import useReconciliationStore from '../../store/reconciliationStore';
import StatementPreview from '../../components/reconciliation/StatementPreview';
import { format, isValid, parseISO } from 'date-fns';
import api from '../../services/api';

// Helper function to safely format dates
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    // Try to parse the date string
    const date = typeof dateString === 'string' ? parseISO(dateString) : new Date(dateString);

    // Check if the date is valid
    if (!isValid(date)) {
      return 'Invalid Date';
    }

    return format(date, 'MMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

const StatementDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const {
    currentStatement,
    fetchStatementById,
    deleteStatement,
    fetchTransactionsByStatement,
    transactions,
    statementsLoading,
    statementsError,
    transactionsLoading,
    transactionsError
  } = useReconciliationStore();

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewError, setPreviewError] = useState(null);

  // Function to fetch preview data
  const fetchPreviewData = async () => {
    if (!id) return;

    try {
      setPreviewLoading(true);
      setPreviewError(null);

      const response = await api.statement.getStatementPreview(id);

      if (response && response.status === true && response.data) {
        console.log('Preview data fetched successfully:', response.data);
        setPreviewData(response.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching statement preview:', error);
      setPreviewError(error.message || 'Failed to load preview');
    } finally {
      setPreviewLoading(false);
    }
  };

  useEffect(() => {
    const loadStatement = async () => {
      try {
        await fetchStatementById(id);
        await fetchTransactionsByStatement(id);
        // Fetch preview data
        await fetchPreviewData();
      } catch (error) {
        console.error('Error loading statement:', error);
      }
    };

    loadStatement();
  }, [id, fetchStatementById, fetchTransactionsByStatement]);

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteStatement(id);
      setShowDeleteModal(false);
      navigate('/statements');
    } catch (error) {
      console.error('Error deleting statement:', error);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
  };

  // Loading state
  if (statementsLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Error state
  if (statementsError) {
    return (
      <div className="bg-red-50 p-6 rounded-lg shadow-sm">
        <div className="flex items-center">
          <FiAlertCircle className="text-red-500 mr-3" size={24} />
          <h2 className="text-lg font-medium text-red-800">Error Loading Statement</h2>
        </div>
        <p className="mt-2 text-red-700">{statementsError}</p>
        <button
          onClick={() => navigate('/statements')}
          className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
        >
          Back to Statements
        </button>
      </div>
    );
  }

  if (!currentStatement) {
    return (
      <div className="bg-yellow-50 p-6 rounded-lg shadow-sm">
        <div className="flex items-center">
          <FiAlertCircle className="text-yellow-500 mr-3" size={24} />
          <h2 className="text-lg font-medium text-yellow-800">Statement Not Found</h2>
        </div>
        <p className="mt-2 text-yellow-700">The requested statement could not be found.</p>
        <button
          onClick={() => navigate('/statements')}
          className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
        >
          Back to Statements
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <button
          onClick={() => navigate('/statements')}
          className="flex items-center text-gray-600 hover:text-gray-800"
        >
          <FiArrowLeft className="mr-2" />
          Back to Statements
        </button>
      </div>

      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            {currentStatement.name}
          </h1>
          <div className="flex items-center space-x-4">
            <button
              onClick={handleDeleteClick}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              <FiTrash2 className="inline-block mr-2" />
              Delete Statement
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Statement Details</h2>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <FiCalendar className="text-indigo-500 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500">Statement Date</p>
                    <p className="font-medium">{formatDate(currentStatement.statement_date)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <FiCreditCard className="text-indigo-500 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500">Type</p>
                    <p className="font-medium capitalize">{currentStatement.statement_type || 'N/A'}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <FiList className="text-indigo-500 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500">Transactions</p>
                    <p className="font-medium">{transactions ? transactions.length : (currentStatement.count || currentStatement.transactionCount || 0)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <FiFileText className="text-indigo-500 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500">Uploaded</p>
                    <p className="font-medium">{formatDate(currentStatement.createdAt)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium text-gray-900">File Preview</h2>
              <div className="flex items-center space-x-3">
                <button
                  onClick={fetchPreviewData}
                  className="text-gray-600 hover:text-gray-800 text-sm flex items-center"
                  title="Refresh preview"
                >
                  <FiRefreshCw className={`mr-1 ${previewLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </button>
                <Link
                  to={`/statements/${id}/preview`}
                  state={{ previewData }}
                  className="text-indigo-600 hover:text-indigo-800 text-sm flex items-center"
                >
                  <FiFileText className="mr-1" /> Full Preview
                </Link>
              </div>
            </div>
            {/* Pass the cached preview data to the StatementPreview component */}
            {previewData ? (
              <StatementPreview
                statementId={id}
                cachedData={previewData}
                isLoading={previewLoading}
                error={previewError}
              />
            ) : (
              <StatementPreview
                statementId={id}
                isLoading={previewLoading}
                error={previewError}
              />
            )}
          </div>
        </div>

        <div className="mt-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Transactions</h2>
          {transactionsLoading ? (
            <div className="flex justify-center items-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            </div>
          ) : transactionsError ? (
            <div className="bg-red-50 p-4 rounded-md">
              <div className="flex">
                <FiAlertCircle className="text-red-500 mr-2" />
                <p className="text-red-700">{transactionsError}</p>
              </div>
            </div>
          ) : transactions && transactions.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.map((transaction) => (
                    <tr key={transaction._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(transaction.date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transaction.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {transaction.reference || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <span className={transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {transaction.amount >= 0 ? '+' : ''}{transaction.amount.toFixed(2)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          transaction.matchedWith ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {transaction.matchedWith ? 'Matched' : 'Unmatched'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <FiFileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
              <p className="mt-1 text-sm text-gray-500">
                This statement doesn't have any transactions yet.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <FiTrash2 className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Statement</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete this statement? This action cannot be undone and will also delete all associated transactions.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={confirmDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={cancelDelete}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StatementDetailPage;
