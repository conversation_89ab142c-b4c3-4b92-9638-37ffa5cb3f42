"""
Statement controller for the Bank Reconciliation API.
"""

import os
import time
from datetime import datetime
from flask import request, jsonify, send_file
from werkzeug.utils import secure_filename
from bson import ObjectId
from models.statement_model import Statement
from models.statement_data_model import StatementData
from services.statement_data_service import process_and_store_statement_data, get_statement_data
from utils.file_parser import parse_file

# Create uploads directory if it doesn't exist
uploads_dir = os.path.join(os.getcwd(), 'uploads')
try:
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir, recursive=True)
        print(f"Created uploads directory: {uploads_dir}")
except Exception as e:
    print(f"Error creating uploads directory: {str(e)}")

def get_all_statements():
    """
    Get all statements for the current user.
    """
    try:
        # Get user ID from request
        user_id = request.user['id']

        # Get pagination parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        skip = (page - 1) * limit

        # Get statements
        statements = Statement.find_all_by_user(user_id, limit, skip)

        # Format response
        statements_data = [statement.to_dict() for statement in statements]

        return jsonify({
            'status': True,
            'data': statements_data
        }), 200

    except Exception as e:
        print(f"Error getting statements: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

def get_statement_by_id(statement_id):
    """
    Get statement by ID.
    """
    try:
        # Get user ID from request
        user_id = request.user['id']

        # Get statement
        statement = Statement.find_by_id(statement_id, user_id)
        if not statement:
            return jsonify({'message': 'Statement not found'}), 404

        return jsonify({
            'status': True,
            'data': statement.to_dict()
        }), 200

    except Exception as e:
        print(f"Error getting statement: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

def delete_statement(statement_id):
    """
    Delete statement.
    """
    try:
        # Get user ID from request
        user_id = request.user['id']

        # Get statement
        statement = Statement.find_by_id(statement_id, user_id)
        if not statement:
            return jsonify({'message': 'Statement not found'}), 404

        # Delete statement
        statement.delete()

        # Delete statement file
        if os.path.exists(statement.file_path):
            os.remove(statement.file_path)

        return jsonify({
            'status': True,
            'message': 'Statement deleted successfully'
        }), 200

    except Exception as e:
        print(f"Error deleting statement: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

def upload_statement():
    """
    Upload statement.
    """
    try:
        # Check if file is provided
        if 'file' not in request.files:
            return jsonify({
                'status': False,
                'message': 'No file part'
            }), 400

        file = request.files['file']

        # Check if file is empty
        if file.filename == '':
            return jsonify({
                'status': False,
                'message': 'No selected file'
            }), 400

        # Get user ID from request
        user_id = request.user['id']

        # Get statement data from request
        name = request.form.get('name', 'Unnamed Statement')
        bank_name = request.form.get('bankName', 'Unknown Bank')
        account_number = request.form.get('accountNumber', 'Unknown Account')

        # Handle date fields safely
        try:
            start_date = request.form.get('startDate')
            if start_date and start_date.strip():
                start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            else:
                start_date = None
        except Exception:
            start_date = None

        try:
            end_date = request.form.get('endDate')
            if end_date and end_date.strip():
                end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            else:
                end_date = None
        except Exception:
            end_date = None

        # Handle numeric fields safely
        try:
            start_balance = float(request.form.get('startBalance', 0))
        except (ValueError, TypeError):
            start_balance = 0

        try:
            end_balance = float(request.form.get('endBalance', 0))
        except (ValueError, TypeError):
            end_balance = 0

        # Secure filename
        filename = secure_filename(file.filename)

        # Add timestamp to filename to avoid conflicts
        timestamp = int(time.time())
        filename = f"{timestamp}_{filename}"

        # Ensure uploads directory exists
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir, exist_ok=True)

        # Save file
        file_path = os.path.join(uploads_dir, filename)
        file.save(file_path)

        # Create statement
        statement = Statement(
            name=name,
            bank_name=bank_name,
            account_number=account_number,
            start_date=start_date,
            end_date=end_date,
            start_balance=start_balance,
            end_balance=end_balance,
            file_name=filename,
            file_path=file_path,
            user_id=user_id
        )

        # Save statement to database
        statement.save()

        # Start processing the statement data in the background
        statement_id = str(statement._id)
        try:
            process_and_store_statement_data(statement_id, user_id)
        except Exception as process_error:
            print(f"Error processing statement data: {str(process_error)}")
            # Continue even if processing fails - we'll try again when the user views the statement

        return jsonify({
            'status': True,
            'message': 'Statement uploaded successfully',
            'data': {
                **statement.to_dict(),
                'processingStatus': 'processing',
                'id': statement_id
            }
        }), 201

    except Exception as e:
        print(f"Error uploading statement: {str(e)}")
        return jsonify({
            'status': False,
            'message': f'Error uploading statement: {str(e)}'
        }), 500

def parse_statement_file():
    """
    Parse statement file.
    """
    try:
        # Check if file is provided
        if 'file' not in request.files:
            return jsonify({'message': 'No file part'}), 400

        file = request.files['file']

        # Check if file is empty
        if file.filename == '':
            return jsonify({'message': 'No selected file'}), 400

        # Secure filename
        filename = secure_filename(file.filename)

        # Add timestamp to filename to avoid conflicts
        timestamp = int(time.time())
        filename = f"{timestamp}_{filename}"

        # Save file
        file_path = os.path.join(uploads_dir, filename)
        file.save(file_path)

        # Parse file
        file_data = parse_file(file_path)

        return jsonify({
            'status': True,
            'message': 'File parsed successfully',
            'data': {
                'fileName': filename,
                'filePath': file_path,
                'fileSize': os.path.getsize(file_path),
                'fileType': os.path.splitext(filename)[1].lower(),
                'content': file_data['content'],
                'metadata': file_data['metadata'],
                'summary': file_data['summary']
            }
        }), 200

    except Exception as e:
        print(f"Error parsing file: {str(e)}")
        return jsonify({'message': 'Server error'}), 500

def get_statement_preview(statement_id):
    """
    Get statement preview.
    """
    try:
        # Get user ID from request
        user_id = request.user['id']

        # Get statement
        statement = Statement.find_by_id(statement_id, user_id)
        if not statement:
            return jsonify({
                'status': False,
                'message': 'Statement not found'
            }), 404

        # Check if file exists
        if not os.path.exists(statement.file_path):
            return jsonify({
                'status': False,
                'message': 'Statement file not found',
                'data': {
                    'id': str(statement._id),
                    'name': statement.name,
                    'fileName': statement.file_name,
                    'fileType': os.path.splitext(statement.file_name)[1].lower(),
                    'uploadDate': statement.created_at.isoformat(),
                    'processingStatus': 'failed'
                }
            }), 404

        try:
            # Get statement data
            projection = ['content', 'metadata', 'summary', 'insights', 'status']
            statement_data = get_statement_data(statement_id, projection)

            if statement_data and statement_data.status == 'completed':
                # Return processed data
                return jsonify({
                    'status': True,
                    'data': {
                        'id': str(statement._id),
                        'name': statement.name,
                        'fileName': statement.file_name,
                        'fileSize': statement_data.metadata.get('fileSize', 0),
                        'fileType': statement_data.metadata.get('fileType', ''),
                        'uploadDate': statement.created_at.isoformat(),
                        'content': statement_data.content,
                        'metadata': statement_data.metadata,
                        'summary': statement_data.summary,
                        'insights': statement_data.insights,
                        'processingStatus': statement_data.status
                    }
                }), 200
            else:
                # Fall back to parsing the file directly
                try:
                    file_preview = parse_file(statement.file_path)

                    # Start processing in the background if statement_data doesn't exist
                    if not statement_data:
                        process_and_store_statement_data(statement_id, user_id)

                    return jsonify({
                        'status': True,
                        'data': {
                            'id': str(statement._id),
                            'name': statement.name,
                            'fileName': statement.file_name,
                            'fileSize': file_preview['metadata']['fileSize'],
                            'fileType': file_preview['metadata']['fileType'],
                            'uploadDate': statement.created_at.isoformat(),
                            'preview': file_preview['preview'],
                            'content': file_preview['content'],
                            'metadata': file_preview['metadata'],
                            'summary': file_preview['summary'],
                            'insights': {},
                            'processingStatus': 'processing' if statement_data else 'processing'
                        }
                    }), 200
                except Exception as parse_error:
                    print(f"Error parsing file: {str(parse_error)}")
                    # Return basic file info with error
                    return jsonify({
                        'status': False,
                        'message': f'Error parsing file: {str(parse_error)}',
                        'data': {
                            'id': str(statement._id),
                            'name': statement.name,
                            'fileName': statement.file_name,
                            'fileSize': os.path.getsize(statement.file_path),
                            'fileType': os.path.splitext(statement.file_name)[1].lower(),
                            'uploadDate': statement.created_at.isoformat(),
                            'processingStatus': 'failed'
                        }
                    }), 200
        except Exception as data_error:
            print(f"Error getting statement data: {str(data_error)}")
            # Try to parse the file directly as a fallback
            try:
                file_preview = parse_file(statement.file_path)
                return jsonify({
                    'status': True,
                    'data': {
                        'id': str(statement._id),
                        'name': statement.name,
                        'fileName': statement.file_name,
                        'fileSize': file_preview['metadata']['fileSize'],
                        'fileType': file_preview['metadata']['fileType'],
                        'uploadDate': statement.created_at.isoformat(),
                        'preview': file_preview['preview'],
                        'content': file_preview['content'],
                        'metadata': file_preview['metadata'],
                        'summary': file_preview['summary'],
                        'insights': {},
                        'processingStatus': 'processing'
                    }
                }), 200
            except Exception as parse_error:
                print(f"Error parsing file: {str(parse_error)}")
                # Return basic file info with error
                return jsonify({
                    'status': False,
                    'message': f'Error getting statement data: {str(data_error)}',
                    'data': {
                        'id': str(statement._id),
                        'name': statement.name,
                        'fileName': statement.file_name,
                        'fileSize': os.path.getsize(statement.file_path),
                        'fileType': os.path.splitext(statement.file_name)[1].lower(),
                        'uploadDate': statement.created_at.isoformat(),
                        'processingStatus': 'failed'
                    }
                }), 200

    except Exception as e:
        print(f"Error getting statement preview: {str(e)}")
        return jsonify({
            'status': False,
            'message': f'Server error: {str(e)}'
        }), 500

def download_statement_file(statement_id):
    """
    Download statement file.
    """
    try:
        # Get user ID from request
        user_id = request.user['id']

        # Get statement
        statement = Statement.find_by_id(statement_id, user_id)
        if not statement:
            return jsonify({'message': 'Statement not found'}), 404

        # Check if file exists
        if not os.path.exists(statement.file_path):
            return jsonify({'message': 'Statement file not found'}), 404

        # Send file
        return send_file(
            statement.file_path,
            as_attachment=True,
            download_name=statement.file_name,
            mimetype='application/octet-stream'
        )

    except Exception as e:
        print(f"Error downloading statement file: {str(e)}")
        return jsonify({'message': 'Server error'}), 500
