"""
PDF extraction utilities for the Bank Reconciliation API.
Based on the pdf.py implementation for fintech applications.
"""

import os
import pdfplumber
import re
import json
import warnings
import sys

# Define expected headers for consistency
EXPECTED_HEADERS = ["EntryDate", "Details", "ValueDate", "Debit", "Credit", "Balance"]

def clean_text(text):
    """Clean text by removing extra whitespace and newlines."""
    if not text:
        return ""
    # Replace newlines with spaces but preserve important information
    cleaned = re.sub(r'\s+', ' ', text.strip())
    return cleaned

def clean_date(date_str):
    """Clean date strings by removing newlines."""
    if not date_str:
        return ""
    # Remove newlines from date strings like "01-MAR-\n25"
    return date_str.replace('\n', '')

def process_table(table, expected_headers=EXPECTED_HEADERS):
    """Process a single table and return a list of transaction dictionaries."""
    transactions = []

    if not table or len(table) == 0:
        return transactions

    # Get headers from the first row
    raw_headers = table[0]

    # Check if the first row actually contains headers
    # If not, we'll use the expected headers directly
    has_headers = False
    for h in raw_headers:
        if h and any(expected.lower() in h.lower() for expected in expected_headers):
            has_headers = True
            break

    # Map columns to expected headers
    column_mapping = {}

    if has_headers:
        # Clean and normalize headers from the table
        for j, h in enumerate(raw_headers):
            if h and h.strip():
                # Clean up header text
                clean_header = h.strip()
                # Map to expected header if similar
                for expected in expected_headers:
                    if expected.lower() in clean_header.lower():
                        column_mapping[j] = expected
                        break
                if j not in column_mapping:
                    column_mapping[j] = clean_header
    else:
        # Use positional mapping based on expected order
        # Typical bank statement order: EntryDate, Details, ValueDate, Debit, Credit, Balance
        for j, expected in enumerate(expected_headers):
            if j < len(raw_headers):
                column_mapping[j] = expected

    # Determine which rows to process
    start_row = 1 if has_headers else 0

    # Process each data row
    for row_idx in range(start_row, len(table)):
        row = table[row_idx]
        row_data = {}

        # Initialize all expected headers with empty strings
        for header in expected_headers:
            row_data[header] = ""

        # Fill in the data we have
        for j, cell in enumerate(row):
            if j in column_mapping:
                header = column_mapping[j]

                # Skip if this is a header row
                if has_headers and row_idx == 0:
                    continue

                # Apply special cleaning based on column type
                if header in ["EntryDate", "ValueDate"]:
                    row_data[header] = clean_date(cell) if cell else ""
                elif header == "Details":
                    row_data[header] = clean_text(cell) if cell else ""
                else:
                    row_data[header] = clean_text(cell) if cell else ""
            elif j < len(expected_headers):
                # Fallback to positional mapping
                header = expected_headers[j]
                row_data[header] = clean_text(cell) if cell else ""

        # Only add non-empty rows (must have at least one non-empty value)
        if any(value.strip() for value in row_data.values()):
            # Make sure we're not adding a row with all the data as keys instead of values
            if not all(k in expected_headers for k in row_data.keys()):
                continue

            transactions.append(row_data)

    return transactions

def validate_transaction(transaction, expected_headers=EXPECTED_HEADERS):
    """Validate and fix transaction data to ensure it's properly structured."""
    # Check if any unexpected keys exist (data as keys)
    unexpected_keys = [k for k in transaction.keys() if k not in expected_headers]

    if unexpected_keys:
        # We have data as keys - need to restructure
        fixed_transaction = {header: "" for header in expected_headers}

        # Try to map the unexpected keys to the correct headers
        for bad_key in unexpected_keys:
            # Try to determine which header this value belongs to
            if re.search(r'\d{2}-[A-Z]{3}-\s?\d{2}', bad_key):
                # This looks like a date
                fixed_transaction["EntryDate"] = clean_date(bad_key)
            elif re.search(r'RANDALPHA', bad_key):
                # This looks like transaction details
                fixed_transaction["Details"] = clean_text(bad_key)
            elif re.search(r'^\d+,\d+\.\d{2}$', bad_key):
                # This looks like an amount
                if not fixed_transaction["Debit"]:
                    fixed_transaction["Debit"] = clean_text(bad_key)
                elif not fixed_transaction["Credit"]:
                    fixed_transaction["Credit"] = clean_text(bad_key)
                elif not fixed_transaction["Balance"]:
                    fixed_transaction["Balance"] = clean_text(bad_key)

        # Also check the values of the bad keys
        for bad_key, value in transaction.items():
            if bad_key not in expected_headers:
                if re.search(r'\d{2}-[A-Z]{3}-\s?\d{2}', value):
                    # This looks like a date
                    if not fixed_transaction["EntryDate"]:
                        fixed_transaction["EntryDate"] = clean_date(value)
                    elif not fixed_transaction["ValueDate"]:
                        fixed_transaction["ValueDate"] = clean_date(value)
                elif re.search(r'RANDALPHA', value):
                    # This looks like transaction details
                    fixed_transaction["Details"] = clean_text(value)
                elif re.search(r'^\d+,\d+\.\d{2}$', value):
                    # This looks like an amount
                    if not fixed_transaction["Debit"]:
                        fixed_transaction["Debit"] = clean_text(value)
                    elif not fixed_transaction["Credit"]:
                        fixed_transaction["Credit"] = clean_text(value)
                    elif not fixed_transaction["Balance"]:
                        fixed_transaction["Balance"] = clean_text(value)

        return fixed_transaction

    return transaction

def extract_tables_from_pdf(pdf_path, verbose=True, max_pages=14):  # Default to 14 pages as requested
    """Extract tables from a PDF file and return as a list of dictionaries."""
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    all_transactions = []

    # Suppress specific warnings
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", message="CropBox missing from /Page")

        try:
            with pdfplumber.open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                pages_to_process = min(total_pages, max_pages) if max_pages else total_pages

                # Process each page
                for i in range(pages_to_process):
                    page = pdf.pages[i]
                    page_num = i + 1

                    if verbose:
                        print(f"Processing page {page_num} of {total_pages}")

                    try:
                        tables = page.extract_tables()

                        if tables:
                            for table_idx, table in enumerate(tables):
                                if verbose:
                                    print(f"Table {table_idx+1} on page {page_num}:")

                                # Process the table
                                page_transactions = process_table(table, EXPECTED_HEADERS)

                                # Validate and add transactions
                                for transaction in page_transactions:
                                    # Validate and fix transaction structure
                                    valid_transaction = validate_transaction(transaction)

                                    # Only add if we have at least some data
                                    if any(valid_transaction.values()):
                                        if verbose:
                                            print(json.dumps(valid_transaction, indent=2))
                                        all_transactions.append(valid_transaction)

                        # Save progress after each page to avoid losing data if script is interrupted
                        if page_num % 5 == 0 or page_num == total_pages:
                            if verbose:
                                print(f"Progress: {len(all_transactions)} transactions processed so far")

                        # Stop at page 14 as requested
                        if page_num >= 14:
                            if verbose:
                                print("Stopping at page 14 as requested")
                            break

                    except Exception as page_error:
                        print(f"Warning: Error processing page {page_num}: {page_error}")
                        print("Continuing with next page...")
                        continue

        except Exception as e:
            print(f"Error processing PDF: {e}")
            raise

    return all_transactions

def extract_bank_statement_data(pdf_path, max_pages=14):
    """Extract bank statement data from a PDF file."""
    transactions = extract_tables_from_pdf(pdf_path, max_pages=max_pages)

    # Extract metadata
    metadata = {
        'fileName': os.path.basename(pdf_path),
        'fileSize': os.path.getsize(pdf_path),
        'pageCount': 0,
        'bankName': 'RANDA ALPHA',  # Default for RANDA ALPHA statements
        'accountNumber': 'Unknown',
        'startDate': None,
        'endDate': None,
        'startBalance': 0,
        'endBalance': 0
    }

    # Try to extract metadata from the PDF
    try:
        with pdfplumber.open(pdf_path) as pdf:
            metadata['pageCount'] = len(pdf.pages)

            # Extract text from first page
            first_page_text = pdf.pages[0].extract_text()

            # Try to extract bank name
            bank_match = re.search(r'(BANK|Bank)\s+OF\s+([A-Z]+)', first_page_text)
            if bank_match:
                metadata['bankName'] = bank_match.group(0)
            elif 'RANDALPHA' in first_page_text:
                metadata['bankName'] = 'RANDA ALPHA'

            # Try to extract account number
            account_match = re.search(r'Account\s+Number[:\s]+([0-9]+)', first_page_text)
            if account_match:
                metadata['accountNumber'] = account_match.group(1)

            # Try to extract start and end balances
            if transactions:
                # Assume first transaction has starting balance
                if 'Balance' in transactions[0]:
                    metadata['startBalance'] = parse_amount(transactions[0]['Balance'])

                # Assume last transaction has ending balance
                if 'Balance' in transactions[-1]:
                    metadata['endBalance'] = parse_amount(transactions[-1]['Balance'])
    except Exception as e:
        print(f"Error extracting metadata: {e}")

    return {
        'metadata': metadata,
        'transactions': transactions
    }

def parse_amount(amount_str):
    """Parse an amount string to a float."""
    if not amount_str:
        return 0

    # Remove currency symbols and commas
    cleaned = re.sub(r'[^\d.-]', '', amount_str)

    try:
        return float(cleaned)
    except ValueError:
        return 0
