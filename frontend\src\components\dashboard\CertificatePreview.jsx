import React, { useState } from 'react';
import CertificateShareModal from '../certificate/CertificateShareModal';

const CertificatePreview = ({ certificate, onClose }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  
  const handleDownload = () => {
    setIsDownloading(true);
    // Simulate download delay
    setTimeout(() => {
      setIsDownloading(false);
      // In a real app, this would trigger an actual download
    }, 1500);
  };
  
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };
  
  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-auto">
        {/* Certificate Header */}
        <div className="bg-[#412D6C] text-white p-6 rounded-t-xl flex justify-between items-center">
          <h3 className="text-xl font-bold">Certificate Preview</h3>
          <button 
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Certificate Content */}
        <div className="p-8">
          <div className="border-8 border-[#412D6C]/10 rounded-lg p-8 bg-[url('/certificate-bg.png')] bg-cover bg-center">
            <div className="text-center">
              <div className="text-[#412D6C] text-xl font-bold mb-2">CERTIFICATE OF COMPLETION</div>
              <div className="text-gray-500 mb-8">This certifies that</div>
              
              <div className="text-3xl font-serif font-bold text-[#412D6C] mb-8">
                {certificate.studentName || "John Doe"}
              </div>
              
              <div className="text-gray-500 mb-4">has successfully completed the course</div>
              <div className="text-2xl font-bold text-[#412D6C] mb-8">{certificate.title}</div>
              
              {certificate.grade && (
                <div className="text-gray-500 mb-8">with a grade of {certificate.grade}</div>
              )}
              
              <div className="flex justify-center items-center mb-8">
                <div className="w-32 h-px bg-gray-300"></div>
                <div className="mx-4 text-[#412D6C]">
                  <svg className="w-12 h-12 opacity-80" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-4-8v2h8v-2H8zm0-4v2h8V10H8zm2-4V4h4v2h-4z"/>
                  </svg>
                </div>
                <div className="w-32 h-px bg-gray-300"></div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="text-center">
                  <div className="text-sm text-gray-500">Date Issued</div>
                  <div className="font-medium">{formatDate(certificate.issueDate)}</div>
                </div>
                
                <div className="text-center">
                  <div className="text-sm text-gray-500">Instructor</div>
                  <div className="font-medium">{certificate.instructor}</div>
                </div>
                
                <div className="text-center">
                  <div className="text-sm text-gray-500">Credential ID</div>
                  <div className="font-medium">{certificate.credentialId}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Certificate Actions */}
        <div className="bg-gray-50 p-6 rounded-b-xl flex justify-between items-center">
          <button 
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Close
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setIsShareModalOpen(true)}
              className="px-4 py-2 border border-[#412D6C] text-[#412D6C] rounded-lg hover:bg-[#412D6C]/5"
            >
              Share
            </button>
            
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className={`px-4 py-2 bg-[#412D6C] text-white rounded-lg ${
                isDownloading ? 'opacity-70 cursor-not-allowed' : 'hover:bg-[#362659]'
              }`}
            >
              {isDownloading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Downloading...
                </span>
              ) : 'Download PDF'}
            </button>
          </div>
        </div>
      </div>
      
      {/* Share Modal */}
      <CertificateShareModal 
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        certificate={certificate}
      />
    </div>
  );
};

export default CertificatePreview;
