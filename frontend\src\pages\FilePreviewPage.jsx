import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { FiArrowLeft, FiDownload, FiSearch, FiFileText, FiRefreshCw } from 'react-icons/fi';
import api from '../services/api';
import FilePreview from '../components/files/FilePreview';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { formatFileSize, formatDate } from '../utils/formatters';

const FilePreviewPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  // Try to get cached data from location state
  const cachedData = location.state?.previewData;

  const [loading, setLoading] = useState(!cachedData);
  const [error, setError] = useState(null);
  const [fileData, setFileData] = useState(cachedData || null);
  const [analyzing, setAnalyzing] = useState(false);

  // Function to fetch file data
  const fetchFileData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.statement.getStatementPreview(id);

      if (response.status && response.data) {
        setFileData(response.data);
      } else {
        setError('Failed to load file preview');
      }
    } catch (err) {
      console.error('Error fetching file preview:', err);
      setError(err.message || 'Failed to load file preview');
    } finally {
      setLoading(false);
    }
  };

  // Only fetch if we don't have cached data
  useEffect(() => {
    if (id && !cachedData) {
      console.log('No cached data found, fetching from API');
      fetchFileData();
    } else if (cachedData) {
      console.log('Using cached data for file preview');
    }
  }, [id, cachedData]);

  const handleDownload = async () => {
    try {
      // Use the statement ID to download the file
      const downloadUrl = `/api/statements/${id}/download`;

      // Create a hidden link and click it to trigger the download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.target = '_blank';
      link.download = fileData?.fileName || 'statement';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.error('Error downloading file:', err);
      alert('Failed to download file: ' + (err.message || 'Unknown error'));
    }
  };

  const handleAnalyze = async () => {
    if (!fileData) {
      return;
    }

    try {
      setAnalyzing(true);

      // Call the analyze endpoint using the statement ID
      const response = await api.statement.getStatementPreview(id);

      if (response.status && response.data) {
        setFileData(response.data);
      }
    } catch (err) {
      console.error('Error analyzing file:', err);
      alert('Failed to analyze file: ' + (err.message || 'Unknown error'));
    } finally {
      setAnalyzing(false);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading file preview</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
        <button
          onClick={handleBack}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <FiArrowLeft className="mr-2" /> Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <div>
          <button
            onClick={handleBack}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mb-2 md:mb-0"
          >
            <FiArrowLeft className="mr-2" /> Back
          </button>
          <h1 className="text-2xl font-bold text-gray-900 mt-2">{fileData?.name || 'File Preview'}</h1>
          <p className="text-sm text-gray-500">
            {fileData?.fileName} • {formatFileSize(fileData?.fileSize)}
          </p>
        </div>
        <div className="flex space-x-2 mt-4 md:mt-0">
          <button
            onClick={fetchFileData}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FiRefreshCw className={`mr-2 ${loading ? 'animate-spin' : ''}`} /> Refresh
          </button>
          <button
            onClick={handleAnalyze}
            disabled={analyzing || loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {analyzing ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" /> Analyzing...
              </>
            ) : (
              <>
                <FiSearch className="mr-2" /> Analyze
              </>
            )}
          </button>
          <button
            onClick={handleDownload}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FiDownload className="mr-2" /> Download
          </button>
        </div>
      </div>

      {/* File metadata */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">File Information</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Details about the uploaded file.</p>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
          <dl className="sm:divide-y sm:divide-gray-200">
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">File name</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{fileData?.fileName}</dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">File type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {getFileTypeLabel(fileData?.fileType)}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">File size</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatFileSize(fileData?.fileSize)}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Upload date</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {formatDate(fileData?.uploadDate)}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* File preview */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">File Preview</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Preview of the file content.
            </p>
          </div>
          <div className="flex items-center">
            <FiFileText className="text-gray-400 mr-2" />
            <div className="flex flex-col items-end">
              <span className="text-sm text-gray-500">
                {getFileTypeLabel(fileData?.fileType)}
              </span>
              {fileData?.fetchTime && (
                <span className="text-xs text-gray-400">
                  Fetched in {fileData.fetchTime}ms
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="border-t border-gray-200">
          {fileData && (
            <FilePreview
              fileData={fileData}
              onDownload={handleDownload}
              onAnalyze={handleAnalyze}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// Helper functions
// Note: formatFileSize and formatDate are imported from formatters.js

const getFileTypeLabel = (fileType) => {
  if (!fileType) return 'Unknown';

  const typeMap = {
    'pdf': 'PDF Document',
    'csv': 'CSV Spreadsheet',
    'xlsx': 'Excel Spreadsheet',
    'xls': 'Excel Spreadsheet'
  };

  return typeMap[fileType.toLowerCase().replace('.', '')] || fileType;
};

export default FilePreviewPage;
