# Simple PDF Table Extractor

A minimal script that uses the `@mkas3/pdf-table-parser` package to extract tables from PDF files.

## Installation

Install the required dependency:

```bash
npm install @mkas3/pdf-table-parser
```

## Usage

### CommonJS Version

```javascript
const fs = require('fs');
const { extractPdfTable } = require('@mkas3/pdf-table-parser');

const fileBuffer = fs.readFileSync('example.pdf');

extractPdfTable(fileBuffer).then(res => {
  console.log(JSON.stringify(res));
});
```

### ES Module Version

```javascript
import fs from 'fs';
import { extractPdfTable } from '@mkas3/pdf-table-parser';

const fileBuffer = fs.readFileSync('example.pdf');

extractPdfTable(fileBuffer).then(res => {
  console.log(JSON.stringify(res));
});
```

### Command Line Usage

Run the script with a PDF file path as an argument:

```bash
node extract-pdf-tables.js path/to/your/file.pdf
```

Or for the ES Module version:

```bash
node extract-pdf-tables-esm.js path/to/your/file.pdf
```

## Output

The script outputs the extracted table data as JSON to the console.
