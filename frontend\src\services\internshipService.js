import { API_BASE_URL, INTERNSHIP_ROUTES } from '../apiRoutes';
import axios from 'axios';

// Get user's internship applications
export const getUserApplications = async (userId) => {
  try {
    // Get the auth token
    const token = localStorage.getItem('authToken');

    if (!token) {
      return {
        status: false,
        error: 'Authentication required. Please log in to check your internship applications.'
      };
    }

    // Make a direct axios request with the token
    const response = await axios.get(
      INTERNSHIP_ROUTES.GET_USER_APPLICATIONS(userId),
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        // This is important - it tells axios to resolve the promise even for error status codes
        validateStatus: function (status) {
          return status >= 200 && status < 500; // Resolve for any status code less than 500
        }
      }
    );

    // If the response indicates not found (either through status code or notFound flag)
    if (response.status === 404 || (response.data && response.data.notFound)) {
      return {
        status: false,
        notFound: true,
        message: response.data.message || 'APPLICATION_NOT_FOUND',
        meta: response.data.meta || {
          error: 'No internship applications found for this user.',
          suggestions: ['Apply for an internship to see your applications here.']
        }
      };
    }

    // For successful responses
    if (response.status === 200 && response.data.status) {
      return {
        status: true,
        data: [response.data.data] // Wrap in array since we expect an array of applications
      };
    }

    // For other non-success responses
    return {
      status: false,
      error: response.data.meta?.error || response.data.message || 'Failed to get internship applications'
    };
  } catch (error) {
    console.error('Error getting internship applications:', error);

    // Handle network errors or other exceptions
    return {
      status: false,
      error: 'Network error or server unavailable. Please try again later.'
    };
  }
};

// Export the service as an object
export const internshipService = {
  getUserApplications
};

export default internshipService;
