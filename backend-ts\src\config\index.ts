import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

const config = {
  port: process.env.PORT || 8080,
  nodeEnv: process.env.NODE_ENV || 'development',
  mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/bank_reconciliation',
  mongoDatabase: process.env.MONGODB_DATABASE || 'bank_reconciliation',
  jwtSecret: process.env.JWT_SECRET || 'your_jwt_secret_key',
  jwtExpiration: parseInt(process.env.JWT_EXPIRATION || '86400', 10), // 24 hours in seconds
};

export default config;
