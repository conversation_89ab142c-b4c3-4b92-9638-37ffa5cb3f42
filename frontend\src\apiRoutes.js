/**
 * API Routes Configuration
 *
 * This file contains all API routes used in the bank reconciliation application.
 * Frontend can import these routes to ensure consistency between frontend and backend.
 */

// Base API URL - should match the one used in backend
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api';

// Auth Routes
const AUTH_ROUTES = {
  LOGIN: `${API_BASE_URL}/auth/login`,
  REGISTER: `${API_BASE_URL}/auth/register`,
};

// User Routes
const USER_ROUTES = {
  GET_USER_ME: `${API_BASE_URL}/users/me`,
  UPDATE_USER_PROFILE: `${API_BASE_URL}/users/me`,
  DELETE_USER: `${API_BASE_URL}/users/me`,
};

// Bank Routes
const BANK_ROUTES = {
  ANALYZE_STATEMENT: `${API_BASE_URL}/bank/analyze-statement`,
};

// Statement Routes
const STATEMENT_ROUTES = {
  GET_ALL: `${API_BASE_URL}/statements`,
  GET_BY_ID: (id) => `${API_BASE_URL}/statements/${id}`,
  DELETE: (id) => `${API_BASE_URL}/statements/${id}`,
  UPLOAD: `${API_BASE_URL}/statements/upload`,
  GET_PREVIEW: (id) => `${API_BASE_URL}/statements/${id}/preview`,
  DOWNLOAD: (id) => `${API_BASE_URL}/statements/${id}/download`,
};

// File Routes
const FILE_ROUTES = {
  UPLOAD: `${API_BASE_URL}/files/upload`,
  PREVIEW: (filePath) => `${API_BASE_URL}/files/preview/${encodeURIComponent(filePath)}`,
  DOWNLOAD: (filePath) => `${API_BASE_URL}/files/download/${encodeURIComponent(filePath)}`,
};

// Transaction Routes
const TRANSACTION_ROUTES = {
  GET_ALL: `${API_BASE_URL}/transactions`,
  GET_BY_ID: (id) => `${API_BASE_URL}/transactions/${id}`,
  CREATE: `${API_BASE_URL}/transactions`,
  UPDATE: (id) => `${API_BASE_URL}/transactions/${id}`,
  DELETE: (id) => `${API_BASE_URL}/transactions/${id}`,
  GET_BY_STATEMENT: (statementId) => `${API_BASE_URL}/transactions/statement/${statementId}`,
  MATCH: `${API_BASE_URL}/transactions/match`,
  UNMATCH: (id) => `${API_BASE_URL}/transactions/unmatch/${id}`,
};

// Reconciliation Routes
const RECONCILIATION_ROUTES = {
  GET_ALL: `${API_BASE_URL}/reconciliations`,
  GET_BY_ID: (id) => `${API_BASE_URL}/reconciliations/${id}`,
  CREATE: `${API_BASE_URL}/reconciliations`,
  GET_TRANSACTIONS: (id) => `${API_BASE_URL}/reconciliations/${id}/transactions`,
  COMPLETE: (id) => `${API_BASE_URL}/reconciliations/${id}/complete`,
};

// Analytics Routes
const ANALYTICS_ROUTES = {
  POST_ANALYTICS: `${API_BASE_URL}/analytics`,
};

// Dev Routes
const DEV_ROUTES = {
  DECODE_TOKEN: `${API_BASE_URL}/oauth/decode`,
};

// Export all routes
export {
  API_BASE_URL,
  AUTH_ROUTES,
  USER_ROUTES,
  BANK_ROUTES,
  STATEMENT_ROUTES,
  FILE_ROUTES,
  TRANSACTION_ROUTES,
  RECONCILIATION_ROUTES,
  ANALYTICS_ROUTES,
  DEV_ROUTES,
};
