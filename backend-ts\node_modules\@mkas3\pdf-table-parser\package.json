{"name": "@mkas3/pdf-table-parser", "description": "Simplified parsing of tables from PDF", "license": "MIT", "version": "1.2.18", "keywords": ["pdf", "table", "parser", "types"], "author": {"name": "mkas3 📛", "url": "https://github.com/mkas3"}, "bugs": {"url": "https://github.com/mkas3/pdf-table-parser/issues"}, "repository": {"type": "git", "url": "https://github.com/mkas3/pdf-table-parser"}, "homepage": "https://github.com/mkas3/pdf-table-parser", "publishConfig": {"access": "public"}, "files": ["dist"], "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"build": "rollup -c --bundleConfigAsCjs", "dev": "tsc --watch", "lint-staged": "lint-staged"}, "lint-staged": {"*.ts": ["prettier --write", "eslint -c .eslintrc.js --no-error-on-unmatched-pattern --fix"]}, "peerDependencies": {"pdfjs-dist": "^4.3.136"}, "dependencies": {"pdfjs-dist": "^4.3.136"}, "devDependencies": {"@mkas3/eslint": "^1.0.2", "@mkas3/prettier": "^1.0.1", "@mkas3/rollup": "^1.0.31", "@mkas3/tsconfig": "^1.0.4", "@types/node": "^20.14.2", "husky": "^9.0.11", "lint-staged": "^15.2.5", "rollup": "^4.18.0", "typescript": "^5.4.5"}}