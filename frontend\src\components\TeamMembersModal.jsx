import React, { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import { userService } from '../services/api';

const TeamMembersModal = ({ isOpen, onClose, onSelect }) => {
  const [teamMembers, setTeamMembers] = useState([]); // Initialize with an empty array
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [isLoading, setIsLoading] = useState(false); // Loading state
  const [error, setError] = useState(null); // Error state

  useEffect(() => {
    if (isOpen) {
      fetchTeamMembers();
    }
  }, [isOpen]);

  const fetchTeamMembers = async () => {
    setIsLoading(true);
    try {
      const response = await userService.getEmployees();
      if (response.status === true) {
        setTeamMembers(response.data); // Set team members from the "data" field
      } else {
        setError(response.message || 'Failed to fetch team members.');
      }
    } catch (error) {
      console.error('Error fetching team members:', error);
      setError('Failed to fetch team members.');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMemberSelection = (member) => {
    setSelectedMembers((prev) => {
      if (prev.includes(member)) {
        return prev.filter((m) => m !== member);
      } else {
        return [...prev, member];
      }
    });
  };

  const handleConfirmSelection = () => {
    onSelect(selectedMembers); // Pass selected members back to the parent
    onClose(); // Close the modal
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-2xl bg-white rounded-xl p-6">
          <Dialog.Title className="text-xl font-bold mb-4">Select Team Members</Dialog.Title>
          {isLoading ? (
            <p>Loading team members...</p>
          ) : error ? (
            <p className="text-red-600">{error}</p>
          ) : (
            <div className="space-y-2">
              {teamMembers.length > 0 ? (
                teamMembers.map((member) => (
                  <div key={member._id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`member-${member._id}`}
                      className="mr-2"
                      checked={selectedMembers.includes(member._id)}
                      onChange={() => toggleMemberSelection(member._id)}
                    />
                    <label htmlFor={`member-${member._id}`}>
                      {`${member.firstName} ${member.lastName} (${member.department})`}
                    </label>
                  </div>
                ))
              ) : (
                <p>No team members available.</p>
              )}
            </div>
          )}
          <div className="flex justify-end gap-2 mt-4">
            <button
              type="button"
              className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="button"
              className="px-4 py-2 bg-teal-700 text-white rounded-lg hover:bg-teal-800"
              onClick={handleConfirmSelection}
              disabled={isLoading}
            >
              Confirm
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default TeamMembersModal;