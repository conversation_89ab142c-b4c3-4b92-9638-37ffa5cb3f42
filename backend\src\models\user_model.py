"""
User model for the Bank Reconciliation API.
"""

from datetime import datetime
from bson import ObjectId
import bcrypt
from pymongo import MongoClient
from config import config

# Connect to MongoDB
client = MongoClient(config['mongo_uri'])
db = client[config['mongo_database']]
users_collection = db['users']

class User:
    """
    User model class.
    """
    def __init__(self, email, password, first_name, last_name, role='user'):
        self.email = email.lower().strip()
        self.password = self._hash_password(password)
        self.first_name = first_name.strip()
        self.last_name = last_name.strip()
        self.role = role
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def _hash_password(self, password):
        """
        Hash password using bcrypt.
        """
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def compare_password(self, password):
        """
        Compare password with hashed password.
        """
        return bcrypt.checkpw(password.encode('utf-8'), self.password.encode('utf-8'))
    
    def to_dict(self):
        """
        Convert user object to dictionary.
        """
        return {
            'email': self.email,
            'firstName': self.first_name,
            'lastName': self.last_name,
            'role': self.role,
            'createdAt': self.created_at,
            'updatedAt': self.updated_at
        }
    
    @classmethod
    def find_by_email(cls, email):
        """
        Find user by email.
        """
        user_data = users_collection.find_one({'email': email.lower()})
        if not user_data:
            return None
        
        user = cls(
            email=user_data['email'],
            password='',  # We don't need to set the password as it's already hashed
            first_name=user_data['firstName'],
            last_name=user_data['lastName'],
            role=user_data['role']
        )
        user.password = user_data['password']  # Set the hashed password directly
        user._id = user_data['_id']
        user.created_at = user_data.get('createdAt', datetime.now())
        user.updated_at = user_data.get('updatedAt', datetime.now())
        
        return user
    
    @classmethod
    def find_by_id(cls, user_id):
        """
        Find user by ID.
        """
        user_data = users_collection.find_one({'_id': ObjectId(user_id)})
        if not user_data:
            return None
        
        user = cls(
            email=user_data['email'],
            password='',
            first_name=user_data['firstName'],
            last_name=user_data['lastName'],
            role=user_data['role']
        )
        user.password = user_data['password']
        user._id = user_data['_id']
        user.created_at = user_data.get('createdAt', datetime.now())
        user.updated_at = user_data.get('updatedAt', datetime.now())
        
        return user
    
    def save(self):
        """
        Save user to database.
        """
        user_data = {
            'email': self.email,
            'password': self.password,
            'firstName': self.first_name,
            'lastName': self.last_name,
            'role': self.role,
            'createdAt': self.created_at,
            'updatedAt': datetime.now()
        }
        
        if hasattr(self, '_id'):
            # Update existing user
            users_collection.update_one(
                {'_id': self._id},
                {'$set': user_data}
            )
        else:
            # Create new user
            result = users_collection.insert_one(user_data)
            self._id = result.inserted_id
        
        return self
