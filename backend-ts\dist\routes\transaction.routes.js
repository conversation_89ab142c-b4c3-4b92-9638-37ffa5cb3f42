"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const transaction_controller_1 = require("../controllers/transaction.controller");
const router = express_1.default.Router();
// @route   GET /api/transactions
// @desc    Get all transactions
// @access  Private
router.get('/', transaction_controller_1.getAllTransactions);
// @route   GET /api/transactions/:id
// @desc    Get transaction by ID
// @access  Private
router.get('/:id', transaction_controller_1.getTransactionById);
// @route   POST /api/transactions
// @desc    Create a new transaction
// @access  Private
router.post('/', transaction_controller_1.createTransaction);
// @route   PUT /api/transactions/:id
// @desc    Update transaction
// @access  Private
router.put('/:id', transaction_controller_1.updateTransaction);
// @route   DELETE /api/transactions/:id
// @desc    Delete transaction
// @access  Private
router.delete('/:id', transaction_controller_1.deleteTransaction);
// @route   GET /api/transactions/statement/:id
// @desc    Get transactions by statement ID
// @access  Private
router.get('/statement/:id', transaction_controller_1.getTransactionsByStatement);
// @route   POST /api/transactions/match
// @desc    Match transactions
// @access  Private
router.post('/match', transaction_controller_1.matchTransactions);
// @route   POST /api/transactions/unmatch/:id
// @desc    Unmatch transaction
// @access  Private
router.post('/unmatch/:id', transaction_controller_1.unmatchTransaction);
exports.default = router;
