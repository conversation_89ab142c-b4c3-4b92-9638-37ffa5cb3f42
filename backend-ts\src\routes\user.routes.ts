import express from 'express';
import { getCurrentUser, updateUser, deleteUser } from '../controllers/user.controller';

const router = express.Router();

// @route   GET /api/users/me
// @desc    Get current user
// @access  Private
router.get('/me', getCurrentUser);

// @route   PUT /api/users/me
// @desc    Update user
// @access  Private
router.put('/me', updateUser);

// @route   DELETE /api/users/me
// @desc    Delete user
// @access  Private
router.delete('/me', deleteUser);

export default router;
