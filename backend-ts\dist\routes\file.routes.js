"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const file_controller_1 = require("../controllers/file.controller");
const router = express_1.default.Router();
// @route   POST /api/files/upload
// @desc    Upload a file
// @access  Private
router.post('/upload', file_controller_1.uploadFile);
// @route   GET /api/files/preview/:filePath
// @desc    Preview file content
// @access  Private
router.get('/preview/:filePath', file_controller_1.previewFile);
// @route   GET /api/files/download/:filePath
// @desc    Download file
// @access  Private
router.get('/download/:filePath', file_controller_1.downloadFile);
// @route   POST /api/files/analyze
// @desc    Analyze file content and extract insights
// @access  Private
router.post('/analyze', file_controller_1.analyzeFile);
exports.default = router;
