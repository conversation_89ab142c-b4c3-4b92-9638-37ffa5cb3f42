import React from 'react';
import { Pie } from 'react-chartjs-2';

const StudyTimeDistribution = ({ studyData = [] }) => {
  // Calculate total hours with safety check
  const totalHours = Array.isArray(studyData)
    ? studyData.reduce((total, item) => total + (item?.hours || 0), 0)
    : 0;

  // Prepare data for chart with safety checks
  const chartData = {
    labels: Array.isArray(studyData) ? studyData.map(item => item?.subject || 'Unknown') : [],
    datasets: [
      {
        data: Array.isArray(studyData) ? studyData.map(item => item?.hours || 0) : [],
        backgroundColor: [
          'rgba(106, 76, 166, 0.8)',
          'rgba(65, 45, 108, 0.8)',
          'rgba(138, 108, 198, 0.8)',
          'rgba(86, 66, 128, 0.8)',
          'rgba(159, 129, 219, 0.8)',
          'rgba(118, 98, 158, 0.8)',
        ],
        borderColor: 'rgba(255, 255, 255, 0.5)',
        borderWidth: 1,
      },
    ],
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const label = context.label || '';
            const value = context.raw || 0;
            const percentage = ((value / totalHours) * 100).toFixed(1);
            return `${label}: ${value}h (${percentage}%)`;
          }
        }
      }
    }
  };

  return (
    <div className="bg-white/30 backdrop-blur-xl p-6 rounded-xl shadow-lg border border-white/20">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-[#412D6C] font-semibold">Study Time Distribution</h3>
        <div className="text-sm text-gray-500">
          Total: <span className="font-medium">{totalHours} hours</span>
        </div>
      </div>

      <div className="h-[250px] relative">
        <Pie data={chartData} options={options} />
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 gap-2">
          {Array.isArray(studyData) && studyData.map((item, index) => (
            <div key={index} className="flex items-center justify-between text-sm">
              <div className="flex items-center">
                <span
                  className="inline-block w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: chartData.datasets[0].backgroundColor[index % chartData.datasets[0].backgroundColor.length] }}
                ></span>
                <span className="truncate">{item.subject}</span>
              </div>
              <span className="font-medium">{item.hours}h</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default StudyTimeDistribution;
