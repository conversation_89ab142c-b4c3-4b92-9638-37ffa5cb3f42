import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import useStore from '../../store/useStore';
import { useUser } from '../../contexts/UserContext.jsx';

// Helper function to safely store data in localStorage
const safelyStoreData = (key, value) => {
  try {
    // First clear any existing value
    localStorage.removeItem(key);

    // Then set the new value
    localStorage.setItem(key, value);

    // Verify it was set correctly
    const storedValue = localStorage.getItem(key);
    if (storedValue !== value) {
      console.error(`Failed to store ${key} in localStorage. Expected: ${value}, Got: ${storedValue}`);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error storing ${key} in localStorage:`, error);
    return false;
  }
};

const Spinner = () => (
  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
);

const Login = () => {
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);

  // Get state management functions from both systems
  const setUser = useStore((state) => state.setUser);
  const { login: contextLogin } = useUser();

  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

  // Check for session expired parameter in URL
  useEffect(() => {
    const sessionExpired = searchParams.get('session');
    if (sessionExpired === 'expired') {
      setError('Your session has expired. Please log in again.');
      // Clear the URL parameter without refreshing the page
      window.history.replaceState({}, document.title, '/login');
    }
  }, [searchParams]);

  // Simple function to get dashboard route based on role
  const getDashboardRoute = (role) => {
    // For the bank reconciliation app, all users go to the same dashboard
    return '/dashboard';
  };

  const handleLogin = async (e) => {
    e.preventDefault();

    if (!formData.email || !formData.password) {
      setError('Please fill in all fields.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('Sending login request to:', `${apiBaseUrl}/auth/login`);

      const loginResponse = await fetch(`${apiBaseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          email: formData.email,
          password: formData.password
        }),
      });

      const loginData = await loginResponse.json();
      console.log('Login response:', loginData);

      if (!loginResponse.ok) {
        setError(loginData.error || loginData.message || 'Login failed');
        console.error('Login failed:', loginData);
        return;
      }

      // Check if token exists in the response
      if (!loginData.token) {
        console.error('Token missing in login response');
        setError('Authentication error: Token missing in response');
        return;
      }

      // Store token and user ID in localStorage using our helper function
      const tokenStored = safelyStoreData('authToken', loginData.token);
      safelyStoreData('userId', loginData.data.id);

      // Log the token for debugging
      console.log('Initial token storage:', localStorage.getItem('authToken') ? 'Success' : 'Failed');

      // If token storage failed, try again
      if (!tokenStored) {
        console.warn('Initial token storage failed, trying again...');
        safelyStoreData('authToken', loginData.token);
      }

      // User data is already included in the login response
      const userData = loginData.data;

      const firstName = userData.firstName;
      let role = userData.role;

      // No need to capitalize role for bank reconciliation app

      // Create user object
      const userObject = {
        id: userData.id,
        firstName: firstName,
        lastName: userData.lastName,
        email: userData.email,
        role: role,
        token: loginData.token
      };

      // Update Zustand store
      setUser(userObject);

      // Update UserContext with user object and token separately
      // This prevents the context from overwriting our token
      contextLogin(userObject, loginData.token);

      // Check if token is still in localStorage after context updates
      const tokenAfterContextUpdate = localStorage.getItem('authToken');
      console.log('Token after context updates:', tokenAfterContextUpdate ? 'Present' : 'Missing');

      // If token is missing, store it again
      if (!tokenAfterContextUpdate) {
        console.warn('Token missing after context updates, storing again...');
        safelyStoreData('authToken', loginData.token);
      }

      // Store additional user info in localStorage using our helper function
      safelyStoreData('userRole', role);
      safelyStoreData('userEmail', userData.email);
      safelyStoreData('lastLoginTime', Date.now().toString());

      // Get the correct redirect route based on the role
      const redirectRoute = getDashboardRoute(role);

      // Add a delay to ensure token is properly saved to localStorage
      setTimeout(() => {
        // Check if token is still in localStorage
        const token = localStorage.getItem('authToken');
        console.log('Token before redirect:', token ? 'Present' : 'Missing');

        // If token is missing, try storing it again
        if (!token) {
          console.warn('Token missing before redirect, storing again...');

          // Try direct localStorage call
          localStorage.setItem('authToken', loginData.token);

          // Add another check
          const finalCheck = localStorage.getItem('authToken');
          console.log('Final token check:', finalCheck ? 'Present' : 'Missing');

          if (!finalCheck) {
            // Last resort: try to store in a different key
            localStorage.setItem('auth_token_backup', loginData.token);
            console.log('Stored token in backup key');
          }
        }

        // Store token in sessionStorage as a backup
        try {
          sessionStorage.setItem('authToken', loginData.token);
          console.log('Token stored in sessionStorage as backup');
        } catch (e) {
          console.error('Failed to store token in sessionStorage:', e);
        }

        // Create a cookie as another backup
        try {
          document.cookie = `authToken=${loginData.token}; path=/; max-age=3600`;
          console.log('Token stored in cookie as backup');
        } catch (e) {
          console.error('Failed to store token in cookie:', e);
        }

        // Redirect to the dashboard
        window.location.href = redirectRoute;
      }, 1000);

    } catch (error) {
      console.error('Login error:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-b from-purple-100 to-white">
      <div className="bg-white/70 backdrop-blur-md rounded-lg shadow-2xl p-8 max-w-md w-full border border-white/50">
        <h2 className="text-2xl font-bold mb-6 text-center text-[#412D6C]">Login to Your Account</h2>

        {error && (
          <div className="p-3 bg-red-100 text-red-700 rounded-lg text-sm mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleLogin} className="space-y-4">
          <div>
            <input
              type="email"
              placeholder="Email"
              className="w-full p-3 border border-gray-300 bg-white/50 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
            />
          </div>

          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              className="w-full p-3 border border-gray-300 bg-white/50 backdrop-blur-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-[#412D6C]"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 cursor-pointer"
            >
              {showPassword ? <FaEyeSlash size={20} /> : <FaEye size={20} />}
            </button>
          </div>

          <div className="text-right">
            <Link to="/forgot-password" className="text-sm text-[#412D6C] hover:underline">
              Forgot Password?
            </Link>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-[#412D6C] text-white py-3 rounded-lg hover:bg-[#362659] transition-colors flex items-center justify-center cursor-pointer"
          >
            {loading ? <Spinner /> : 'Login'}
          </button>

          <div className="text-center mt-4">
            <p className="text-gray-600">
              Don&apos;t have an account?{' '}
              <Link to="/signup" className="text-[#412D6C] font-medium hover:underline">
                Sign Up
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
