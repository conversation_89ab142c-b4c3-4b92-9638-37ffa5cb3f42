import express from 'express';
import {
  getAllTransactions,
  getTransactionById,
  createTransaction,
  updateTransaction,
  deleteTransaction,
  getTransactionsByStatement,
  matchTransactions,
  unmatchTransaction
} from '../controllers/transaction.controller';

const router = express.Router();

// @route   GET /api/transactions
// @desc    Get all transactions
// @access  Private
router.get('/', getAllTransactions);

// @route   GET /api/transactions/:id
// @desc    Get transaction by ID
// @access  Private
router.get('/:id', getTransactionById);

// @route   POST /api/transactions
// @desc    Create a new transaction
// @access  Private
router.post('/', createTransaction);

// @route   PUT /api/transactions/:id
// @desc    Update transaction
// @access  Private
router.put('/:id', updateTransaction);

// @route   DELETE /api/transactions/:id
// @desc    Delete transaction
// @access  Private
router.delete('/:id', deleteTransaction);

// @route   GET /api/transactions/statement/:id
// @desc    Get transactions by statement ID
// @access  Private
router.get('/statement/:id', getTransactionsByStatement);

// @route   POST /api/transactions/match
// @desc    Match transactions
// @access  Private
router.post('/match', matchTransactions);

// @route   POST /api/transactions/unmatch/:id
// @desc    Unmatch transaction
// @access  Private
router.post('/unmatch/:id', unmatchTransaction);

export default router;
