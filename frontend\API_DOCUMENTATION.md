# API Documentation for PoolothQ Backend

This document outlines the API endpoints required for the frontend implementation. The backend should implement these endpoints to ensure proper functionality of the frontend features.

## Base URL

```
http://localhost:5000/api/v1
```

## Authentication

All API endpoints require authentication via a bearer token stored in localStorage.

```
Authorization: Bearer <token>
```

## Error Handling

All endpoints should return appropriate HTTP status codes and error messages in the following format:

```json
{
  "status": false,
  "message": "ERROR_CODE",
  "meta": {
    "error": "Human-readable error message",
    "suggestions": ["Suggestion 1", "Suggestion 2"]
  }
}
```

## Success Response Format

All successful responses should follow this format:

```json
{
  "status": true,
  "message": "SUCCESS_MESSAGE",
  "data": {
    // Response data
  }
}
```

## API Endpoints

### User Management

#### Get All Users

```
GET /user/all
```

**Response:**

```json
{
  "status": true,
  "message": "USERS_FETCHED_SUCCESSFULLY",
  "data": [
    {
      "id": "user_id",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "role": "Student",
      "status": "Active",
      "joinDate": "2023-10-15",
      "lastActive": "2024-04-20"
    },
    // More users...
  ]
}
```

#### Get User by ID

```
GET /user/:userId
```

**Response:**

```json
{
  "status": true,
  "message": "USER_FETCHED_SUCCESSFULLY",
  "data": {
    "id": "user_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "Student",
    "status": "Active",
    "joinDate": "2023-10-15",
    "lastActive": "2024-04-20",
    "profile": {
      // Additional user profile data
    }
  }
}
```

#### Create User

```
POST /user/create
```

**Request Body:**

```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword",
  "role": "Student"
}
```

**Response:**

```json
{
  "status": true,
  "message": "USER_CREATED_SUCCESSFULLY",
  "data": {
    "id": "new_user_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "Student",
    "status": "Active",
    "joinDate": "2024-04-22"
  }
}
```

#### Update User

```
PUT /user/:userId
```

**Request Body:**

```json
{
  "name": "John Doe Updated",
  "email": "<EMAIL>",
  "role": "Instructor",
  "status": "Active"
}
```

**Response:**

```json
{
  "status": true,
  "message": "USER_UPDATED_SUCCESSFULLY",
  "data": {
    "id": "user_id",
    "name": "John Doe Updated",
    "email": "<EMAIL>",
    "role": "Instructor",
    "status": "Active"
  }
}
```

#### Delete User

```
DELETE /user/:userId
```

**Response:**

```json
{
  "status": true,
  "message": "USER_DELETED_SUCCESSFULLY"
}
```

### Course Management

#### Get All Courses

```
GET /courses
```

**Query Parameters:**
- `category` (optional): Filter by category
- `level` (optional): Filter by level
- `search` (optional): Search term for title or description

**Response:**

```json
{
  "status": true,
  "message": "COURSES_FETCHED_SUCCESSFULLY",
  "data": [
    {
      "id": "course_id",
      "title": "Web Development Fundamentals",
      "instructor": "John Smith",
      "category": "Development",
      "enrollments": 245,
      "rating": 4.8,
      "status": "Published",
      "createdAt": "2023-09-15",
      "thumbnail": "https://example.com/thumbnail.jpg"
    },
    // More courses...
  ]
}
```

#### Get Course by ID

```
GET /courses/:courseId
```

**Response:**

```json
{
  "status": true,
  "message": "COURSE_DETAILS_FETCHED_SUCCESSFULLY",
  "data": {
    "id": "course_id",
    "title": "Web Development Fundamentals",
    "instructor": {
      "id": "instructor_id",
      "firstName": "John",
      "lastName": "Smith",
      "email": "<EMAIL>"
    },
    "category": "Development",
    "level": "Beginner",
    "description": "Learn the fundamentals of web development...",
    "enrollments": 245,
    "rating": 4.8,
    "status": "Published",
    "createdAt": "2023-09-15",
    "thumbnail": "https://example.com/thumbnail.jpg",
    "modules": [
      {
        "id": "module_id",
        "title": "HTML Basics",
        "lessons": [
          {
            "id": "lesson_id",
            "title": "Introduction to HTML",
            "duration": 15,
            "type": "video"
          },
          // More lessons...
        ]
      },
      // More modules...
    ]
  }
}
```

#### Create Course

```
POST /courses
```

**Request Body:**

```json
{
  "title": "New Course Title",
  "instructor": "instructor_id",
  "category": "Development",
  "level": "Beginner",
  "description": "Course description...",
  "status": "Draft",
  "thumbnail": "https://example.com/thumbnail.jpg",
  "modules": [
    {
      "title": "Module 1",
      "lessons": [
        {
          "title": "Lesson 1",
          "duration": 15,
          "type": "video",
          "content": "video_url_or_content"
        }
      ]
    }
  ]
}
```

**Response:**

```json
{
  "status": true,
  "message": "COURSE_CREATED_SUCCESSFULLY",
  "data": {
    "id": "new_course_id",
    "title": "New Course Title",
    // Other course details...
  }
}
```

#### Update Course

```
PUT /courses/:courseId
```

**Request Body:**

```json
{
  "title": "Updated Course Title",
  "category": "Development",
  "level": "Intermediate",
  "description": "Updated course description...",
  "status": "Published"
  // Other fields to update...
}
```

**Response:**

```json
{
  "status": true,
  "message": "COURSE_UPDATED_SUCCESSFULLY",
  "data": {
    "id": "course_id",
    "title": "Updated Course Title",
    // Other updated course details...
  }
}
```

#### Delete Course

```
DELETE /courses/:courseId
```

**Response:**

```json
{
  "status": true,
  "message": "COURSE_DELETED_SUCCESSFULLY"
}
```

#### Get Course Enrollments

```
GET /courses/:courseId/enrollments
```

**Response:**

```json
{
  "status": true,
  "message": "COURSE_ENROLLMENTS_FETCHED_SUCCESSFULLY",
  "data": [
    {
      "id": "enrollment_id",
      "userId": "user_id",
      "userName": "John Doe",
      "enrollmentDate": "2024-03-15",
      "progress": 45,
      "status": "Active"
    },
    // More enrollments...
  ]
}
```

### Scholarship Management

#### Get All Scholarships

```
GET /scholarships
```

**Query Parameters:**
- `status` (optional): Filter by status (Active, Draft, Closed)

**Response:**

```json
{
  "status": true,
  "message": "SCHOLARSHIPS_FETCHED_SUCCESSFULLY",
  "data": [
    {
      "id": "scholarship_id",
      "title": "Web Development Excellence Scholarship",
      "amount": 5000,
      "deadline": "2024-06-30",
      "eligibility": "Students pursuing web development courses",
      "status": "Active",
      "applicants": 45,
      "awarded": 0
    },
    // More scholarships...
  ]
}
```

#### Get Scholarship by ID

```
GET /scholarships/:scholarshipId
```

**Response:**

```json
{
  "status": true,
  "message": "SCHOLARSHIP_FETCHED_SUCCESSFULLY",
  "data": {
    "id": "scholarship_id",
    "title": "Web Development Excellence Scholarship",
    "amount": 5000,
    "deadline": "2024-06-30",
    "eligibility": "Students pursuing web development courses",
    "status": "Active",
    "applicants": 45,
    "awarded": 0,
    "description": "Detailed scholarship description...",
    "requirements": ["Requirement 1", "Requirement 2"],
    "applicationProcess": "Application process details..."
  }
}
```

#### Create Scholarship

```
POST /scholarships
```

**Request Body:**

```json
{
  "title": "New Scholarship",
  "amount": 6000,
  "deadline": "2024-08-15",
  "eligibility": "Eligibility criteria...",
  "status": "Draft",
  "description": "Detailed scholarship description...",
  "requirements": ["Requirement 1", "Requirement 2"],
  "applicationProcess": "Application process details..."
}
```

**Response:**

```json
{
  "status": true,
  "message": "SCHOLARSHIP_CREATED_SUCCESSFULLY",
  "data": {
    "id": "new_scholarship_id",
    "title": "New Scholarship",
    // Other scholarship details...
  }
}
```

#### Update Scholarship

```
PUT /scholarships/:scholarshipId
```

**Request Body:**

```json
{
  "title": "Updated Scholarship Title",
  "amount": 7000,
  "deadline": "2024-09-01",
  "status": "Active"
  // Other fields to update...
}
```

**Response:**

```json
{
  "status": true,
  "message": "SCHOLARSHIP_UPDATED_SUCCESSFULLY",
  "data": {
    "id": "scholarship_id",
    "title": "Updated Scholarship Title",
    // Other updated scholarship details...
  }
}
```

#### Delete Scholarship

```
DELETE /scholarships/:scholarshipId
```

**Response:**

```json
{
  "status": true,
  "message": "SCHOLARSHIP_DELETED_SUCCESSFULLY"
}
```

#### Get Scholarship Applications

```
GET /scholarships/:scholarshipId/applications
```

**Response:**

```json
{
  "status": true,
  "message": "SCHOLARSHIP_APPLICATIONS_FETCHED_SUCCESSFULLY",
  "data": [
    {
      "id": "application_id",
      "scholarshipId": "scholarship_id",
      "studentName": "John Doe",
      "studentEmail": "<EMAIL>",
      "submissionDate": "2024-04-10",
      "status": "Under Review"
    },
    // More applications...
  ]
}
```

#### Update Application Status

```
PUT /scholarships/applications/:applicationId
```

**Request Body:**

```json
{
  "status": "Approved" // or "Rejected", "Under Review"
}
```

**Response:**

```json
{
  "status": true,
  "message": "APPLICATION_STATUS_UPDATED_SUCCESSFULLY",
  "data": {
    "id": "application_id",
    "status": "Approved"
  }
}
```

### Internship Management

#### Get All Internships

```
GET /internships
```

**Query Parameters:**
- `location` (optional): Filter by location
- `status` (optional): Filter by status (Active, Draft, Closed)

**Response:**

```json
{
  "status": true,
  "message": "INTERNSHIPS_FETCHED_SUCCESSFULLY",
  "data": [
    {
      "id": "internship_id",
      "title": "Frontend Developer Internship",
      "company": "TechSolutions Inc.",
      "location": "Remote",
      "duration": "3 months",
      "stipend": 1200,
      "deadline": "2024-05-30",
      "status": "Active",
      "applicants": 38,
      "selected": 0
    },
    // More internships...
  ]
}
```

#### Get Internship by ID

```
GET /internships/:internshipId
```

**Response:**

```json
{
  "status": true,
  "message": "INTERNSHIP_FETCHED_SUCCESSFULLY",
  "data": {
    "id": "internship_id",
    "title": "Frontend Developer Internship",
    "company": "TechSolutions Inc.",
    "location": "Remote",
    "duration": "3 months",
    "stipend": 1200,
    "deadline": "2024-05-30",
    "status": "Active",
    "applicants": 38,
    "selected": 0,
    "description": "Detailed internship description...",
    "requirements": ["Requirement 1", "Requirement 2"],
    "responsibilities": ["Responsibility 1", "Responsibility 2"],
    "applicationProcess": "Application process details..."
  }
}
```

#### Create Internship

```
POST /internships
```

**Request Body:**

```json
{
  "title": "New Internship",
  "company": "Company Name",
  "location": "Location",
  "duration": "Duration",
  "stipend": 1500,
  "deadline": "2024-07-15",
  "status": "Draft",
  "description": "Detailed internship description...",
  "requirements": ["Requirement 1", "Requirement 2"],
  "responsibilities": ["Responsibility 1", "Responsibility 2"],
  "applicationProcess": "Application process details..."
}
```

**Response:**

```json
{
  "status": true,
  "message": "INTERNSHIP_CREATED_SUCCESSFULLY",
  "data": {
    "id": "new_internship_id",
    "title": "New Internship",
    // Other internship details...
  }
}
```

#### Update Internship

```
PUT /internships/:internshipId
```

**Request Body:**

```json
{
  "title": "Updated Internship Title",
  "stipend": 1800,
  "deadline": "2024-08-01",
  "status": "Active"
  // Other fields to update...
}
```

**Response:**

```json
{
  "status": true,
  "message": "INTERNSHIP_UPDATED_SUCCESSFULLY",
  "data": {
    "id": "internship_id",
    "title": "Updated Internship Title",
    // Other updated internship details...
  }
}
```

#### Delete Internship

```
DELETE /internships/:internshipId
```

**Response:**

```json
{
  "status": true,
  "message": "INTERNSHIP_DELETED_SUCCESSFULLY"
}
```

#### Get Internship Applications

```
GET /internships/:internshipId/applications
```

**Response:**

```json
{
  "status": true,
  "message": "INTERNSHIP_APPLICATIONS_FETCHED_SUCCESSFULLY",
  "data": [
    {
      "id": "application_id",
      "internshipId": "internship_id",
      "studentName": "John Doe",
      "studentEmail": "<EMAIL>",
      "resume": "resume_url",
      "submissionDate": "2024-04-10",
      "status": "Under Review"
    },
    // More applications...
  ]
}
```

#### Update Application Status

```
PUT /internships/applications/:applicationId
```

**Request Body:**

```json
{
  "status": "Selected" // or "Rejected", "Under Review"
}
```

**Response:**

```json
{
  "status": true,
  "message": "APPLICATION_STATUS_UPDATED_SUCCESSFULLY",
  "data": {
    "id": "application_id",
    "status": "Selected"
  }
}
```

### Dashboard Data

#### Get Student Dashboard Data

```
GET /student/dashboard/:userId
```

**Response:**

```json
{
  "status": true,
  "message": "DASHBOARD_DATA_FETCHED_SUCCESSFULLY",
  "data": {
    "stats": {
      "coursesInProgress": 4,
      "completedCourses": 12,
      "totalStudyHours": 48,
      "certificatesEarned": 5,
      "trends": {
        "coursesInProgressTrend": { "direction": "up", "value": "12" },
        "completedCoursesTrend": { "direction": "up", "value": "25" },
        "totalStudyHoursTrend": { "direction": "up", "value": "8" },
        "certificatesEarnedTrend": { "direction": "up", "value": "20" }
      }
    },
    "weeklyActivity": [
      { "day": "Sunday", "hours": 1.5 },
      { "day": "Monday", "hours": 3.2 },
      // Other days...
    ],
    "studyTimeDistribution": [
      { "subject": "Web Development", "hours": 12 },
      { "subject": "JavaScript", "hours": 8 },
      // Other subjects...
    ],
    "learningGoals": [
      {
        "id": "goal_id",
        "title": "Complete JavaScript Course",
        "description": "Finish all modules and assignments",
        "current": 75,
        "target": 100,
        "unit": "%",
        "deadline": "2024-06-30"
      },
      // More goals...
    ],
    "upcomingEvents": [
      {
        "title": "Assignment Due",
        "date": "2024-05-15",
        "description": "JavaScript Final Project",
        "type": "assignment"
      },
      // More events...
    ],
    "courses": [
      {
        "id": "course_id",
        "title": "Web Development Fundamentals",
        "thumbnail": "thumbnail_url",
        "progress": 75,
        "status": "In Progress",
        "instructor": "Sarah Johnson",
        "deadlines": [
          {
            "title": "HTML/CSS Assignment",
            "date": "2024-05-25",
            "type": "assignment"
          },
          // More deadlines...
        ]
      },
      // More courses...
    ],
    "notifications": [
      {
        "message": "New course available: Advanced React",
        "time": "2h ago",
        "read": false
      },
      // More notifications...
    ]
  }
}
```

#### Record Study Session

```
POST /dashboard/:userId/study-session
```

**Request Body:**

```json
{
  "subject": "Web Development",
  "hours": 2.5,
  "date": "2024-04-22" // Optional, defaults to current date
}
```

**Response:**

```json
{
  "status": true,
  "message": "STUDY_SESSION_RECORDED_SUCCESSFULLY",
  "data": {
    // Updated dashboard data
  }
}
```

#### Update Course Progress

```
POST /dashboard/:userId/course-progress/:courseId
```

**Request Body:**

```json
{
  "progress": 85,
  "completedModules": ["module_id_1", "module_id_2"],
  "status": "Almost Complete"
}
```

**Response:**

```json
{
  "status": true,
  "message": "COURSE_PROGRESS_UPDATED_SUCCESSFULLY",
  "data": {
    // Updated course data
  }
}
```

#### Add Learning Goal

```
POST /dashboard/:userId/goals
```

**Request Body:**

```json
{
  "title": "Complete Python Course",
  "description": "Finish all modules and get certificate",
  "target": 100,
  "unit": "%",
  "deadline": "2024-07-30"
}
```

**Response:**

```json
{
  "status": true,
  "message": "LEARNING_GOAL_ADDED_SUCCESSFULLY",
  "data": {
    "id": "new_goal_id",
    "title": "Complete Python Course",
    "description": "Finish all modules and get certificate",
    "current": 0,
    "target": 100,
    "unit": "%",
    "deadline": "2024-07-30",
    "createdAt": "2024-04-22",
    "updatedAt": "2024-04-22"
  }
}
```

#### Update Learning Goal

```
PUT /dashboard/:userId/goals/:goalId
```

**Request Body:**

```json
{
  "current": 50,
  "target": 100,
  "deadline": "2024-08-15"
}
```

**Response:**

```json
{
  "status": true,
  "message": "LEARNING_GOAL_UPDATED_SUCCESSFULLY",
  "data": {
    "id": "goal_id",
    "title": "Complete Python Course",
    "description": "Finish all modules and get certificate",
    "current": 50,
    "target": 100,
    "unit": "%",
    "deadline": "2024-08-15",
    "updatedAt": "2024-04-22"
  }
}
```

#### Delete Learning Goal

```
DELETE /dashboard/:userId/goals/:goalId
```

**Response:**

```json
{
  "status": true,
  "message": "LEARNING_GOAL_DELETED_SUCCESSFULLY"
}
```

## Implementation Notes

1. All endpoints should validate input data and return appropriate error messages.
2. Authentication should be implemented using JWT tokens.
3. User roles and permissions should be enforced for all endpoints.
4. Database models should be designed to support the data structures outlined in this documentation.
5. API responses should be consistent with the formats specified above.
6. All timestamps should be in ISO 8601 format.
7. File uploads (e.g., course thumbnails, resumes) should be handled securely.
8. Pagination should be implemented for endpoints that return large collections of data.
