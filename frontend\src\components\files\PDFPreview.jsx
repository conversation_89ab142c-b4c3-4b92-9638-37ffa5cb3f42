import React, { useState, useEffect } from 'react';
import { FiChevronLeft, FiChevronRight, FiGrid, FiAlignLeft, FiTable, FiList } from 'react-icons/fi';

const PDFPreview = ({ data, currentPage, setCurrentPage }) => {
  const [viewMode, setViewMode] = useState('statement'); // 'statement', 'text', 'tables', or 'json'
  const [parsedStatement, setParsedStatement] = useState(null);

  // Handle the new data structure
  const content = data.content || {};
  const totalPages = content.pages || data.metadata?.pageCount || 1;
  const text = content.text || data.preview || '';
  const tables = content.tables || [];
  const transactions = content.transactions || [];

  // Extract bank statement information
  useEffect(() => {
    if (text) {
      // Try to parse bank statement data from the text
      const statementData = parseStatementData(text);
      setParsedStatement(statementData);

      // If we successfully parsed statement data, default to statement view
      if (statementData && (statementData.entries.length > 0 || statementData.metadata.bankName)) {
        setViewMode('statement');
      }
    }
  }, [text]);

  // Check if this is a Polaris Bank statement
  const isPolarisBank = text && (text.includes('Polaris Bank Limited') || text.includes('POLARIS BANK'));

  // Store bank info for use in rendering
  const bankInfo = content.bankInfo || {};

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Parse bank statement data from text
  const parseStatementData = (text) => {
    const lines = text.split('\n').filter(line => line.trim() !== '');
    const result = {
      metadata: {
        bankName: '',
        accountNumber: '',
        accountType: '',
        dateRange: '',
        sortCode: '',
        branch: '',
        reportBy: '',
        staffInfo: '',
        address: [],
        recipient: [],
      },
      entries: [],
      balance: null,
      headers: ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance']
    };

    // Extract metadata from the beginning of the document
    let i = 0;

    // Look for bank name
    for (i = 0; i < Math.min(10, lines.length); i++) {
      if (lines[i].includes('BANK') || lines[i].includes('Bank')) {
        result.metadata.bankName = lines[i].trim();

        // Extract address lines (typically follow the bank name)
        let addressLineIndex = i + 1;
        while (addressLineIndex < i + 5 && addressLineIndex < lines.length) {
          const line = lines[addressLineIndex].trim();
          if (line && !line.includes('RANDALPHA') && !line.match(/\d{2}-[A-Z]{3}-\d{2}/)) {
            result.metadata.address.push(line);
          } else {
            break;
          }
          addressLineIndex++;
        }

        break;
      }
    }

    // Special handling for Polaris Bank
    if (text.includes('Polaris Bank Limited') || text.includes('POLARIS BANK')) {
      result.metadata.bankName = 'Polaris Bank Limited';

      // Look for address in the first few lines
      for (i = 0; i < Math.min(20, lines.length); i++) {
        const line = lines[i].trim();
        if (line.includes('OGBOMOSHO') || line.includes('OYO')) {
          result.metadata.address.push(line);
        }

        // Extract date range
        if (line.match(/\d{2}-[A-Z]{3}-\d{2} to \d{2}-[A-Z]{3}-\d{2}/)) {
          result.metadata.dateRange = line;
        }

        // Extract account details
        if (line.includes('ACCOUNT - NGN')) {
          result.metadata.accountType = line;
        } else if (line.includes('SORT CODE')) {
          result.metadata.sortCode = line;
        } else if (line.includes('Report By')) {
          result.metadata.reportBy = line;
        } else if (line.includes('Staff No')) {
          result.metadata.staffInfo = line;
        } else if (line.includes('Branch')) {
          result.metadata.branch = line;
        }
      }
    }

    // Look for recipient information (RANDALPHA...)
    for (i = 0; i < Math.min(30, lines.length); i++) {
      const line = lines[i].trim();
      if (line.includes('RANDALPHA') || line.includes('MICROFINANCE')) {
        result.metadata.recipient.push(line);

        // Extract recipient address lines
        let recipientLineIndex = i + 1;
        while (recipientLineIndex < i + 5 && recipientLineIndex < lines.length) {
          const line = lines[recipientLineIndex].trim();
          if (line && !line.match(/\d{2}-[A-Z]{3}-\d{2}/) && !line.includes('POLARIS') && !line.includes('ACCOUNT')) {
            result.metadata.recipient.push(line);
          } else {
            break;
          }
          recipientLineIndex++;
        }

        break;
      }
    }

    // Look for date range
    for (i = 0; i < Math.min(30, lines.length); i++) {
      const line = lines[i].trim();
      if (line.match(/\d{2}-[A-Z]{3}-\d{2}\s+[Tt]o\s+\d{2}-[A-Z]{3}-\d{2}/)) {
        result.metadata.dateRange = line;
        break;
      }
    }

    // Look for account information
    for (i = 0; i < Math.min(40, lines.length); i++) {
      const line = lines[i].trim();

      if (line.includes('ACCOUNT') || line.includes('Account')) {
        result.metadata.accountType = line;
      }

      if (line.includes('SORT CODE') || line.includes('SORT CODE:-')) {
        result.metadata.sortCode = line;
      }

      if (line.includes('BRANCH') || line.includes('Branch:-')) {
        result.metadata.branch = line;
      }

      if (line.includes('Report By:-')) {
        result.metadata.reportBy = line;
      }

      if (line.includes('Staff No:-')) {
        result.metadata.staffInfo = line;
      }
    }

    // Look for table headers and transaction data
    let tableStartIndex = -1;
    let tableHeaders = [];

    // Find the table header row (typically contains EntryDate, Details, ValueDate, etc.)
    for (i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Look for common header patterns in bank statements
      if (line.includes('EntryDate') ||
          line.includes('ENTRYDATE') ||
          (line.includes('Date') && line.includes('Details') && line.includes('Debit')) ||
          (line.includes('DATE') && line.includes('DETAILS') && line.includes('DEBIT')) ||
          (line.includes('Date') && line.includes('Description') && line.includes('Amount'))) {

        // For this specific format, headers might be concatenated without spaces
        if (line === 'EntryDateDetailsValueDateDebitCreditBalance') {
          tableHeaders = ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'];
        } else if (line.toUpperCase() === 'ENTRYDATEDETAILSVALUEDATEDEBITCREDITBALANCE') {
          tableHeaders = ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'];
        } else {
          // Split by multiple spaces to get column names
          tableHeaders = line.split(/\s{2,}/).filter(h => h.trim() !== '');

          // If we have headers in all caps, convert them to proper case
          tableHeaders = tableHeaders.map(header => {
            const upperHeader = header.toUpperCase();
            if (upperHeader === 'ENTRYDATE') return 'EntryDate';
            if (upperHeader === 'VALUEDATE') return 'ValueDate';
            if (upperHeader === 'DETAILS') return 'Details';
            if (upperHeader === 'DEBIT') return 'Debit';
            if (upperHeader === 'CREDIT') return 'Credit';
            if (upperHeader === 'BALANCE') return 'Balance';
            return header;
          });
        }

        result.headers = tableHeaders;
        tableStartIndex = i;
        break;
      }
    }

    // If we didn't find headers, but we see a pattern that looks like a bank statement,
    // use default headers
    if (tableStartIndex === -1) {
      // Look for patterns that indicate this is a bank statement
      for (i = 0; i < Math.min(20, lines.length); i++) {
        const line = lines[i].trim();

        // Check for balance B/F pattern
        if (line.includes('Balance B/F') || line.match(/^\d{2}-[A-Z]{3}-\d{2}$/)) {
          // This looks like a bank statement, use default headers
          tableHeaders = ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'];
          result.headers = tableHeaders;
          tableStartIndex = i - 1; // Set to line before the first entry
          break;
        }
      }
    }

    // If we found table headers, parse the transaction entries
    if (tableStartIndex > -1 && tableHeaders.length > 0) {
      // Find the initial balance if present
      for (i = tableStartIndex + 1; i < Math.min(tableStartIndex + 5, lines.length); i++) {
        const line = lines[i].trim();
        if (line.includes('Balance B/F') || line.includes('Opening Balance')) {
          const balanceMatch = line.match(/[\d,]+\.\d{2}/);
          if (balanceMatch) {
            result.balance = parseFloat(balanceMatch[0].replace(/,/g, ''));

            // Create a special entry for the opening balance
            const balanceEntry = {};
            balanceEntry['EntryDate'] = lines[i-1]?.trim() || '';
            balanceEntry['Details'] = line;
            balanceEntry['Balance'] = balanceMatch[0];
            result.entries.push(balanceEntry);
          }
        }
      }

      // For this specific format, we need a more direct approach
      // First, let's try to extract the raw data by looking at the pattern in the example

      // Reset entries array but keep the balance entry if it exists
      const balanceEntry = result.entries.length === 1 ? result.entries[0] : null;
      result.entries = balanceEntry ? [balanceEntry] : [];

      // Create a map to store the full transaction data
      const transactionMap = new Map();

      // First pass: collect all transaction data by date
      for (i = tableStartIndex + 1; i < lines.length; i++) {
        const line = lines[i].trim();

        // Skip empty lines
        if (!line) continue;

        // Check for entry date pattern (03-MAR-25)
        if (line.match(/^\d{2}-[A-Z]{3}-\d{2}$/)) {
          const entryDate = line;

          // Look for transaction details in the next line
          if (i + 1 < lines.length) {
            const detailsLine = lines[i+1].trim();

            // Check if this is a transaction detail line
            if (detailsLine.match(/^\d{2}\.\d{2}\.\d{4}\s+RANDALPHA/) ||
                detailsLine.includes('Balance B/F')) {

              // Create or update transaction entry
              if (!transactionMap.has(entryDate)) {
                transactionMap.set(entryDate, []);
              }

              const transaction = {
                EntryDate: entryDate,
                Details: detailsLine
              };

              transactionMap.get(entryDate).push(transaction);
            }
          }
        }

        // Check for value date pattern
        if (line.match(/^\d{2}-[A-Z]{3}-\d{2}$/) && transactionMap.has(line)) {
          const valueDate = line;
          const transactions = transactionMap.get(valueDate);

          // Look for amounts in the following lines
          if (transactions && transactions.length > 0 && i + 2 < lines.length) {
            const lastTransaction = transactions[transactions.length - 1];

            // Check for debit amount
            const debitLine = lines[i+1].trim();
            if (debitLine.match(/^[\d,]+\.\d{2}$/)) {
              lastTransaction.ValueDate = valueDate;
              lastTransaction.Debit = debitLine;

              // Check for credit amount
              if (i + 2 < lines.length) {
                const creditLine = lines[i+2].trim();
                if (creditLine.match(/^\d+$/)) {
                  lastTransaction.Credit = creditLine;

                  // Check for balance
                  if (i + 3 < lines.length) {
                    const balanceLine = lines[i+3].trim();
                    if (balanceLine.match(/^[\d,]+\.\d{2}$/)) {
                      lastTransaction.Balance = balanceLine;
                    }
                  }
                }
              }
            }
          }
        }
      }

      // Second approach: try to match the pattern directly from the raw text
      // This is a more brute-force approach for this specific format
      const rawText = text;

      // Look for patterns like:
      // 03-MAR-25
      // 01.03.2025 RANDALPHA MFB/OLALERE IDOWU MARY/'090496250301121728413602636378
      // 03-MAR-25
      // 960.000
      // 65,897,998.14

      const pattern = /(\d{2}-[A-Z]{3}-\d{2})\s+(\d{2}\.\d{2}\.\d{4}\s+RANDALPHA[^\n]+)\s+(\d{2}-[A-Z]{3}-\d{2})\s+([\d,]+\.\d{2})\s+(\d+)\s+([\d,]+\.\d{2})/g;

      let match;
      while ((match = pattern.exec(rawText)) !== null) {
        const entry = {
          EntryDate: match[1],
          Details: match[2],
          ValueDate: match[3],
          Debit: match[4],
          Credit: match[5],
          Balance: match[6]
        };

        result.entries.push(entry);
      }

      // If we still don't have entries, try a third approach with a more relaxed pattern
      if (result.entries.length <= 1) {
        // Try to find all the dates first
        const datePattern = /\d{2}-[A-Z]{3}-\d{2}/g;
        const dates = [];
        let dateMatch;

        while ((dateMatch = datePattern.exec(rawText)) !== null) {
          dates.push({
            date: dateMatch[0],
            index: dateMatch.index
          });
        }

        // Now try to find transaction details between dates
        for (let j = 0; j < dates.length - 1; j++) {
          const currentDate = dates[j];
          const nextDate = dates[j + 1];

          // Extract the text between these dates
          const segment = rawText.substring(currentDate.index, nextDate.index);

          // Look for transaction details
          if (segment.includes('RANDALPHA')) {
            // Find the details line
            const detailsMatch = segment.match(/(\d{2}\.\d{2}\.\d{4}\s+RANDALPHA[^\n]+)/);

            if (detailsMatch) {
              // Find amount patterns
              const amountMatches = segment.match(/([\d,]+\.\d{2})/g);

              if (amountMatches && amountMatches.length >= 2) {
                const entry = {
                  EntryDate: currentDate.date,
                  Details: detailsMatch[1],
                  ValueDate: nextDate.date,
                  Debit: amountMatches[0],
                  Credit: '0',
                  Balance: amountMatches[amountMatches.length - 1]
                };

                result.entries.push(entry);
              }
            }
          }
        }
      }

      // If we have a balance entry, make sure it's at the top
      if (balanceEntry && result.entries.length > 0) {
        // Remove any duplicate balance entries
        result.entries = result.entries.filter(entry =>
          !(entry.Details && entry.Details.includes('Balance B/F')));

        // Add the balance entry back at the top
        result.entries.unshift(balanceEntry);
      }

      // Last resort: if we still don't have proper entries, try a direct pattern match on the full text
      if (result.entries.length <= 1) {
        // This pattern looks for the specific format in the example
        const directPattern = /(\d{2}-[A-Z]{3}-\d{2})\s+(.*?RANDALPHA.*?)\s+(\d{2}-[A-Z]{3}-\d{2})\s+([\d,]+\.\d{2})\s+(\d+)\s+([\d,]+\.\d{2})/gs;

        let directMatch;
        while ((directMatch = directPattern.exec(text)) !== null) {
          const entry = {
            EntryDate: directMatch[1],
            Details: directMatch[2].trim(),
            ValueDate: directMatch[3],
            Debit: directMatch[4],
            Credit: directMatch[5],
            Balance: directMatch[6]
          };

          // Check if this is a duplicate
          const isDuplicate = result.entries.some(e =>
            e.EntryDate === entry.EntryDate &&
            e.Details === entry.Details);

          if (!isDuplicate) {
            result.entries.push(entry);
          }
        }
      }

      // Special case for the format in the screenshot example
      if (result.entries.length <= 1) {
        // This is a more comprehensive approach to extract data from the specific format
        // shown in the screenshot

        // First, try to find the table structure in the raw text
        const tablePattern = /EntryDate\s+Details\s+ValueDate\s+Debit\s+Credit\s+Balance/i;
        const tableMatch = text.match(tablePattern);

        if (tableMatch) {
          // We found the table header, now extract the data that follows
          const tableStartPos = tableMatch.index + tableMatch[0].length;
          const tableText = text.substring(tableStartPos);

          // Extract rows using a pattern that matches the table structure
          // This pattern is specifically designed for the Polaris Bank format
          const rowPattern = /(\d{2}-[A-Z]{3}-\d{2})\s+(.*?)\s+(\d{2}-[A-Z]{3}-\d{2})\s+([\d,]+\.\d{2})\s+(\d+)\s+([\d,]+\.\d{2})/g;

          let rowMatch;
          while ((rowMatch = rowPattern.exec(tableText)) !== null) {
            const entry = {
              EntryDate: rowMatch[1],
              Details: rowMatch[2].trim(),
              ValueDate: rowMatch[3],
              Debit: rowMatch[4],
              Credit: rowMatch[5],
              Balance: rowMatch[6]
            };

            result.entries.push(entry);
          }
        }

        // Special handling for Polaris Bank format
        if (text.includes('Polaris Bank Limited') || text.includes('POLARIS BANK')) {
          // Try a more flexible pattern for Polaris Bank statements
          const polarisPattern = /(\d{2}-[A-Z]{3}-\d{2})[^\n]*?(\d{2}\.\d{2}\.\d{4}\s+[^\n]+?)[^\n]*?(\d{2}-[A-Z]{3}-\d{2})[^\n]*?([\d,]+\.\d{2})[^\n]*?(\d+)[^\n]*?([\d,]+\.\d{2})/g;

          let polarisMatch;
          while ((polarisMatch = polarisPattern.exec(text)) !== null) {
            const entry = {
              EntryDate: polarisMatch[1],
              Details: polarisMatch[2].trim(),
              ValueDate: polarisMatch[3],
              Debit: polarisMatch[4],
              Credit: polarisMatch[5],
              Balance: polarisMatch[6]
            };

            // Check if this is a duplicate
            const isDuplicate = result.entries.some(e =>
              e.EntryDate === entry.EntryDate &&
              e.Details === entry.Details
            );

            if (!isDuplicate) {
              result.entries.push(entry);
            }
          }

          // If we still don't have enough entries, try a more specific pattern for the Polaris Bank format
          if (result.entries.length <= 1) {
            // First, find the balance B/F entry
            let balanceEntry = null;
            for (i = 0; i < lines.length; i++) {
              const line = lines[i].trim();
              if (line.includes('Balance B/F')) {
                const balanceMatch = line.match(/[\d,]+\.\d{2}/);
                if (balanceMatch) {
                  balanceEntry = {
                    EntryDate: '',
                    Details: 'Balance B/F........',
                    ValueDate: '',
                    Debit: '',
                    Credit: '',
                    Balance: balanceMatch[0]
                  };

                  // Add the balance entry to the results
                  result.entries.push(balanceEntry);
                  break;
                }
              }
            }

            // Now look for transaction entries with the format shown in the image
            // This pattern looks for lines with the date format 01.03.2025 followed by RANDALPHA
            const transactionPattern = /(\d{2}\.\d{2}\.\d{4})\s+(RANDALPHA\s+MFB\/[^\n]+)/g;

            let transactionMatch;
            while ((transactionMatch = transactionPattern.exec(text)) !== null) {
              const dateStr = transactionMatch[1];
              const details = transactionMatch[2].trim();

              // Look for the debit amount in nearby lines
              let debitAmount = '';
              let valueDate = dateStr;

              // Find the corresponding entry in the table format
              for (i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (line.includes(details)) {
                  // Look for the debit amount in the next few lines
                  for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
                    const amountLine = lines[j].trim();
                    if (amountLine.match(/^[\d,]+\.\d{2}$/)) {
                      debitAmount = amountLine;
                      break;
                    }
                  }
                  break;
                }
              }

              // Create the entry
              const entry = {
                EntryDate: dateStr,
                Details: details,
                ValueDate: valueDate,
                Debit: debitAmount,
                Credit: '0',
                Balance: ''
              };

              // Check if this is a duplicate
              const isDuplicate = result.entries.some(e =>
                e.Details === entry.Details
              );

              if (!isDuplicate) {
                result.entries.push(entry);
              }
            }
          }
        }
      }

      // If that didn't work, try a different approach
      if (result.entries.length <= 1) {
        // First, try to find the balance B/F entry
        let balanceEntry = null;
        for (i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line.includes('Balance B/F')) {
            const balanceMatch = line.match(/[\d,]+\.\d{2}/);
            if (balanceMatch) {
              // Look for the date in nearby lines
              let entryDate = '';
              for (let j = Math.max(0, i-3); j < Math.min(i+3, lines.length); j++) {
                const dateLine = lines[j].trim();
                if (dateLine.match(/^\d{2}-[A-Z]{3}-\d{2}$/)) {
                  entryDate = dateLine;
                  break;
                }
              }

              balanceEntry = {
                EntryDate: entryDate,
                Details: line,
                Balance: balanceMatch[0]
              };
              break;
            }
          }
        }

        // If we found a balance entry, add it
        if (balanceEntry) {
          result.entries = [balanceEntry];
        }

        // Now look for transaction entries with a more comprehensive approach
        // We'll scan the text for patterns that match transaction data

        // First, collect all date lines (potential entry dates)
        const dateLinesIndices = [];
        for (i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          if (line.match(/^\d{2}-[A-Z]{3}-\d{2}$/)) {
            dateLinesIndices.push(i);
          }
        }

        // Try to find the format from the screenshot
        // This format has dates in the first column and transaction details in the second column
        const entrydatePattern = /ENTRYDATE/i;
        const detailsPattern = /DETAILS/i;

        // Find the column positions
        let entrydateCol = -1;
        let detailsCol = -1;

        for (i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          const entrydateMatch = line.match(entrydatePattern);
          const detailsMatch = line.match(detailsPattern);

          if (entrydateMatch && detailsMatch) {
            entrydateCol = entrydateMatch.index;
            detailsCol = detailsMatch.index;
            break;
          }
        }

        // If we found the column positions, extract the data
        if (entrydateCol >= 0 && detailsCol >= 0) {
          // Find rows that have data in these columns
          for (i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Skip short lines
            if (line.length < Math.max(entrydateCol, detailsCol) + 5) continue;

            // Check if this line has a date in the first column
            const dateMatch = line.substring(0, detailsCol).trim().match(/^\d{2}$/);

            if (dateMatch) {
              // This looks like a date entry
              const entryDate = dateMatch[0];

              // Extract the details
              const details = line.substring(detailsCol).trim();

              // Only process if this is a transaction line
              if (details.includes('RANDALPHA') || details.includes('Balance B/F')) {
                const entry = {
                  EntryDate: entryDate,
                  Details: details
                };

                // For balance entry, extract the balance
                if (details.includes('Balance B/F')) {
                  const balanceMatch = details.match(/[\d,]+\.\d{2}/);
                  if (balanceMatch) {
                    entry.Balance = balanceMatch[0];
                  }
                }

                // Check if this is a duplicate
                const isDuplicate = result.entries.some(e =>
                  e.EntryDate === entry.EntryDate &&
                  e.Details === entry.Details);

                if (!isDuplicate) {
                  result.entries.push(entry);
                }
              }
            }
          }
        }

        // If we still don't have enough entries, try the original approach
        if (result.entries.length <= 1) {
          // Now process each potential transaction
          for (let dateIndex = 0; dateIndex < dateLinesIndices.length; dateIndex++) {
            const dateLineIndex = dateLinesIndices[dateIndex];
            const entryDate = lines[dateLineIndex].trim();

            // Look for transaction details in the following lines
            let detailsLine = '';
            let valueDate = '';
            let debit = '';
            let credit = '';
            let balance = '';

            // Check the next few lines for transaction details
            for (let j = dateLineIndex + 1; j < Math.min(dateLineIndex + 10, lines.length); j++) {
              const line = lines[j].trim();

              // Look for RANDALPHA pattern (transaction details)
              if (!detailsLine && line.includes('RANDALPHA')) {
                detailsLine = line;
                continue;
              }

              // Look for value date
              if (!valueDate && line.match(/^\d{2}-[A-Z]{3}-\d{2}$/)) {
                valueDate = line;
                continue;
              }

              // Look for debit amount
              if (!debit && line.match(/^[\d,]+\.\d{2}$/)) {
                debit = line;
                continue;
              }

              // Look for credit amount (usually 0)
              if (debit && !credit && line.match(/^\d+$/)) {
                credit = line;
                continue;
              }

              // Look for balance
              if (debit && credit && !balance && line.match(/^[\d,]+\.\d{2}$/)) {
                balance = line;
                break;
              }
            }

            // If we found transaction details, create an entry
            if (detailsLine) {
              const entry = {
                EntryDate: entryDate,
                Details: detailsLine,
                ValueDate: valueDate,
                Debit: debit,
                Credit: credit,
                Balance: balance
              };

              // Check if this is a duplicate
              const isDuplicate = result.entries.some(e =>
                e.EntryDate === entry.EntryDate &&
                e.Details === entry.Details);

              if (!isDuplicate) {
                result.entries.push(entry);
              }
            }
          }
        }

        // One more attempt - try to find the specific format from the screenshot
        if (result.entries.length <= 1) {
          // Look for lines that contain RANDALPHA
          for (i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            if (line.includes('RANDALPHA MFB/')) {
              // This is a transaction line
              // Extract the date from the beginning of the line if it exists
              let entryDate = '';
              const dateMatch = line.match(/^(\d{2}\.\d{2}\.\d{4})/);

              if (dateMatch) {
                entryDate = dateMatch[1];
              }

              const entry = {
                EntryDate: entryDate,
                Details: line
              };

              // Check if this is a duplicate
              const isDuplicate = result.entries.some(e =>
                e.Details === entry.Details);

              if (!isDuplicate) {
                result.entries.push(entry);
              }
            }
          }
        }

        // If we have entries but no headers, set default headers
        if (result.entries.length > 0 && (!result.headers || result.headers.length === 0)) {
          result.headers = ['EntryDate', 'Details', 'ValueDate', 'Debit', 'Credit', 'Balance'];
        }
      }
    }

    // If we still don't have entries, try the new approach for the specific format with date pattern "03-MAR-25"
    if (result.entries.length <= 1) {
      // Parse rows using known date pattern at start
      const entries = [];
      let current = null;

      for (const line of lines) {
        const dateMatch = line.match(/^\d{2}-[A-Z]{3}-\d{2}/); // e.g., 03-MAR-25

        if (dateMatch) {
          // New row starting with EntryDate
          if (current) entries.push(current);

          const [entryDate, ...rest] = line.split(/\s{2,}|\t/);
          current = {
            EntryDate: entryDate,
            Details: rest.join(' '),
            ValueDate: '',
            Debit: '',
            Credit: '',
            Balance: ''
          };
        } else if (current) {
          // Handle continuation lines or table columns
          const parts = line.split(/\s{2,}|\t/);
          if (parts.length >= 4) {
            [current.ValueDate, current.Debit, current.Credit, current.Balance] = parts;
          } else {
            // If continuation line, append to details
            current.Details += ' ' + line;
          }
        }
      }

      // Don't forget to add the last entry
      if (current) entries.push(current);

      // If we found entries with the new approach, use them
      if (entries.length > 0) {
        result.entries = entries;
      }
    }

    return result;
  };

  const renderStatementView = () => {
    // First check if we have backend-provided transactions data for Polaris Bank
    if (isPolarisBank && transactions.length > 0) {
      // Use the backend-provided data

      // Define headers for the table
      const headers = ['entryDate', 'details', 'valueDate', 'debit', 'credit', 'balance'];

      return (
        <div className="space-y-4">
          {/* Statement Header - Two column layout */}
          <div className="bg-white p-4 rounded border border-gray-200">
            <div className="grid grid-cols-2 gap-4">
              {/* Left column - Bank and address */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-1">{bankInfo.bankName || 'Polaris Bank Limited'}</h3>

                {bankInfo.address && bankInfo.address.map((line, index) => (
                  <p key={index} className="text-sm text-gray-700">{line}</p>
                ))}

                {bankInfo.recipient && bankInfo.recipient.length > 0 && (
                  <div className="mt-3">
                    {bankInfo.recipient.map((line, index) => (
                      <p key={index} className="text-sm text-gray-700">{line}</p>
                    ))}
                  </div>
                )}
              </div>

              {/* Right column - Account details */}
              <div className="text-right">
                {bankInfo.dateRange && (
                  <p className="text-sm font-medium text-gray-800">{bankInfo.dateRange}</p>
                )}

                {bankInfo.accountNumber && (
                  <p className="text-sm text-gray-700">{bankInfo.accountNumber}</p>
                )}

                {bankInfo.accountType && (
                  <p className="text-sm font-medium text-gray-800">{bankInfo.accountType}</p>
                )}

                {bankInfo.sortCode && (
                  <p className="text-sm text-gray-700">{bankInfo.sortCode}</p>
                )}

                {bankInfo.reportBy && (
                  <p className="text-sm text-gray-700 mt-3">{bankInfo.reportBy}</p>
                )}

                {bankInfo.staffInfo && (
                  <p className="text-sm text-gray-700">{bankInfo.staffInfo}</p>
                )}

                {bankInfo.branch && (
                  <p className="text-sm text-gray-700">{bankInfo.branch}</p>
                )}
              </div>
            </div>
          </div>

          {/* Transactions Table */}
          <div className="overflow-auto custom-scrollbar" style={{
            maxHeight: '60vh',
            overflowY: 'scroll',
            overflowX: 'scroll',
            border: '1px solid #ddd',
            borderRadius: '4px'
          }}>
            <table className="min-w-full divide-y divide-gray-200 table-fixed">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  {/* Line number header */}
                  <th scope="col" className="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100 w-12">
                    #
                  </th>

                  {/* Table headers */}
                  {headers.map((header, index) => {
                    // Determine column width based on header type
                    let width = "w-auto";
                    let alignment = "text-left";

                    if (header === 'entryDate' || header === 'valueDate') {
                      width = "w-24";
                    } else if (header === 'details') {
                      width = "w-96";
                    } else if (header === 'debit' || header === 'credit' || header === 'balance') {
                      width = "w-32";
                      alignment = "text-right";
                    }

                    return (
                      <th
                        key={index}
                        scope="col"
                        className={`px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${alignment} ${width}`}
                      >
                        {header.charAt(0).toUpperCase() + header.slice(1)}
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((entry, rowIndex) => {
                  // Determine if this is a balance row
                  const isBalanceRow = entry.details && entry.details.includes('Balance B/F');

                  return (
                    <tr key={rowIndex} className={isBalanceRow ? 'bg-gray-100' : rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      {/* Line number column */}
                      <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500 bg-gray-100 text-right font-mono w-12">
                        {rowIndex + 1}
                      </td>

                      {/* Entry data */}
                      {headers.map((header, colIndex) => {
                        const value = entry[header] || '';
                        const isNumeric = !isNaN(parseFloat(value.replace(/,/g, '')));

                        // Determine cell styling based on column type
                        let cellClass = "px-4 py-2 text-sm";

                        if (header === 'details') {
                          cellClass += " text-left whitespace-normal break-words";
                        } else {
                          cellClass += " whitespace-nowrap";
                        }

                        if (header === 'debit' && value) {
                          cellClass += " text-right font-mono text-red-600 font-medium";
                        } else if (header === 'credit' && value && value !== '0') {
                          cellClass += " text-right font-mono text-green-600 font-medium";
                        } else if (header === 'balance') {
                          cellClass += " text-right font-mono font-medium";
                        }

                        return (
                          <td key={colIndex} className={cellClass}>
                            {value}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* JSON View Button */}
          <div className="flex justify-end mt-4">
            <button
              onClick={() => setViewMode('json')}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm flex items-center"
            >
              <span className="mr-1">View JSON</span>
              <FiTable className="h-4 w-4" />
            </button>
          </div>
        </div>
      );
    }

    // Fall back to the client-side parsed data if backend data is not available
    if (!parsedStatement) {
      return (
        <div className="text-center text-gray-500">
          <p>Could not parse bank statement data. Try the Text view instead.</p>
        </div>
      );
    }

    const { metadata, entries, balance, headers } = parsedStatement;

    return (
      <div className="space-y-4">
        {/* Statement Header - Two column layout */}
        <div className="bg-white p-4 rounded border border-gray-200">
          <div className="grid grid-cols-2 gap-4">
            {/* Left column - Bank and address */}
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-1">{metadata.bankName || 'Bank Statement'}</h3>

              {metadata.address.map((line, index) => (
                <p key={index} className="text-sm text-gray-700">{line}</p>
              ))}

              {metadata.recipient.length > 0 && (
                <div className="mt-3">
                  {metadata.recipient.map((line, index) => (
                    <p key={index} className="text-sm text-gray-700">{line}</p>
                  ))}
                </div>
              )}
            </div>

            {/* Right column - Account details */}
            <div className="text-right">
              {metadata.dateRange && (
                <p className="text-sm font-medium text-gray-800">{metadata.dateRange}</p>
              )}

              {metadata.accountNumber && (
                <p className="text-sm text-gray-700">{metadata.accountNumber}</p>
              )}

              {metadata.accountType && (
                <p className="text-sm font-medium text-gray-800">{metadata.accountType}</p>
              )}

              {metadata.sortCode && (
                <p className="text-sm text-gray-700">{metadata.sortCode}</p>
              )}

              {metadata.reportBy && (
                <p className="text-sm text-gray-700 mt-3">{metadata.reportBy}</p>
              )}

              {metadata.staffInfo && (
                <p className="text-sm text-gray-700">{metadata.staffInfo}</p>
              )}

              {metadata.branch && (
                <p className="text-sm text-gray-700">{metadata.branch}</p>
              )}
            </div>
          </div>
        </div>

        {/* Transactions Table */}
        {entries.length > 0 ? (
          <div className="overflow-auto custom-scrollbar" style={{
            maxHeight: '60vh',
            overflowY: 'scroll',
            overflowX: 'scroll',
            border: '1px solid #ddd',
            borderRadius: '4px'
          }}>
            <table className="min-w-full divide-y divide-gray-200 table-fixed">
              <thead className="bg-gray-50 sticky top-0 z-10">
                <tr>
                  {/* Line number header */}
                  <th scope="col" className="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100 w-12">
                    #
                  </th>

                  {/* Use the detected headers or fallback to entry keys */}
                  {(headers.length > 0 ? headers : Object.keys(entries[0])).map((header, index) => {
                    // Determine column width based on header type
                    let width = "w-auto";
                    let alignment = "text-left";

                    if (header === 'EntryDate' || header === 'ValueDate') {
                      width = "w-24";
                    } else if (header === 'Details') {
                      width = "w-96";
                    } else if (header === 'Debit' || header === 'Credit' || header === 'Balance') {
                      width = "w-32";
                      alignment = "text-right";
                    }

                    return (
                      <th
                        key={index}
                        scope="col"
                        className={`px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${alignment} ${width}`}
                      >
                        {header}
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {entries.map((entry, rowIndex) => {
                  // Determine if this is a balance row
                  const isBalanceRow = entry.Details && entry.Details.includes('Balance B/F');

                  return (
                    <tr key={rowIndex} className={isBalanceRow ? 'bg-gray-100' : rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      {/* Line number column */}
                      <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500 bg-gray-100 text-right font-mono w-12">
                        {rowIndex + 1}
                      </td>

                      {/* Entry data */}
                      {(headers.length > 0 ? headers : Object.keys(entries[0])).map((header, colIndex) => {
                        const value = entry[header] || '';
                        const isNumeric = !isNaN(parseFloat(value.replace(/,/g, '')));

                        // Determine cell styling based on column type
                        let cellClass = "px-4 py-2 text-sm";

                        if (header === 'Details') {
                          cellClass += " text-left whitespace-normal break-words";
                        } else {
                          cellClass += " whitespace-nowrap";
                        }

                        if (header === 'Debit' && value) {
                          cellClass += " text-right font-mono text-red-600 font-medium";
                        } else if (header === 'Credit' && value && value !== '0') {
                          cellClass += " text-right font-mono text-green-600 font-medium";
                        } else if (header === 'Balance') {
                          cellClass += " text-right font-mono font-medium";
                        } else if (isNumeric) {
                          cellClass += " text-right font-mono";
                        }

                        if (isBalanceRow) {
                          cellClass += " font-medium";
                        }

                        // Special case for the format in the screenshot
                        // If we're missing values for some columns but have the Details,
                        // we can still display a meaningful table
                        let displayValue = value;

                        // Special handling for Polaris Bank format
                        if (metadata.bankName && metadata.bankName.includes('Polaris Bank')) {
                          // Format the EntryDate to match the table format (03-MAR-25)
                          if (header === 'EntryDate' && value && value.match(/^\d{2}\.\d{2}\.\d{4}$/)) {
                            // Convert from DD.MM.YYYY to DD-MMM-YY
                            const dateParts = value.split('.');
                            if (dateParts.length === 3) {
                              const day = dateParts[0];
                              const month = parseInt(dateParts[1]);
                              const year = dateParts[2].substring(2); // Get last 2 digits

                              // Convert month number to abbreviated month name
                              const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                                                 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
                              const monthName = monthNames[month - 1];

                              displayValue = `${day}-${monthName}-${year}`;
                            }
                          }

                          // If ValueDate is missing, use EntryDate
                          if (header === 'ValueDate' && !value && entry.EntryDate) {
                            if (entry.EntryDate.match(/^\d{2}\.\d{2}\.\d{4}$/)) {
                              // Convert from DD.MM.YYYY to DD-MMM-YY
                              const dateParts = entry.EntryDate.split('.');
                              if (dateParts.length === 3) {
                                const day = dateParts[0];
                                const month = parseInt(dateParts[1]);
                                const year = dateParts[2].substring(2); // Get last 2 digits

                                // Convert month number to abbreviated month name
                                const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                                                   'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
                                const monthName = monthNames[month - 1];

                                displayValue = `${day}-${monthName}-${year}`;
                              }
                            } else {
                              displayValue = entry.EntryDate;
                            }
                          }
                        }

                        // If this is a RANDALPHA entry with missing data, show a placeholder
                        if (header !== 'Details' && header !== 'EntryDate' && !value &&
                            entry.Details && entry.Details.includes('RANDALPHA')) {
                          if (header === 'ValueDate') {
                            displayValue = entry.EntryDate || '';
                          } else if (header === 'Debit') {
                            // For Polaris Bank, try to find the debit amount from the text section
                            if (metadata.bankName && metadata.bankName.includes('Polaris Bank')) {
                              // Look for the debit amount in the text section
                              const detailsText = entry.Details;
                              // Find the corresponding entry in the text section
                              const textLines = text.split('\n');
                              for (let i = 0; i < textLines.length; i++) {
                                const line = textLines[i].trim();
                                if (line.includes(detailsText.substring(0, 30))) {
                                  // Look for the debit amount in the next few lines
                                  for (let j = i - 3; j < i + 3; j++) {
                                    if (j >= 0 && j < textLines.length) {
                                      const amountLine = textLines[j].trim();
                                      const amountMatch = amountLine.match(/^[\d,]+\.\d{2}$/);
                                      if (amountMatch) {
                                        displayValue = amountMatch[0];
                                        break;
                                      }
                                    }
                                  }
                                  break;
                                }
                              }
                            } else {
                              // For debit, we could show a placeholder or leave it empty
                              displayValue = '';
                            }
                          } else if (header === 'Credit') {
                            displayValue = '0';
                          }
                        }

                        return (
                          <td key={colIndex} className={cellClass}>
                            {displayValue}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center text-gray-500 p-4">
            <p>No transaction entries found in this statement.</p>
          </div>
        )}
      </div>
    );
  };

  const renderTextView = () => {
    // Get the text content to display
    let textContent = '';
    if (content.pageContents && content.pageContents[currentPage]) {
      textContent = content.pageContents[currentPage];
    } else {
      textContent = text;
    }

    // Split text into lines and add line numbers
    const lines = textContent.split('\n');

    return (
      <div className="font-mono text-sm">
        {lines.map((line, index) => (
          <div key={index} className="flex">
            <div className="px-2 py-1 text-xs text-gray-500 bg-gray-100 text-right font-mono w-12 flex-shrink-0 select-none">
              {index + 1}
            </div>
            <div className="px-4 py-1 text-gray-700 whitespace-pre-wrap flex-grow">
              {line || ' '}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTableView = () => {
    if (!tables || tables.length === 0) {
      return (
        <div className="text-center text-gray-500">
          <p>No tables detected in this PDF.</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {tables.map((table, tableIndex) => (
          <div key={tableIndex} className="border rounded overflow-hidden">
            <div className="bg-gray-100 px-3 py-2 font-medium text-sm">
              Table {tableIndex + 1}
            </div>
            <div className="overflow-auto custom-scrollbar" style={{
              maxHeight: '50vh',
              overflowY: 'scroll',
              overflowX: 'scroll',
              border: '1px solid #ddd',
              borderRadius: '4px'
            }}>
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    {/* Line number header */}
                    <th scope="col" className="px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100 w-12">
                      #
                    </th>
                    {table.headers.map((header, index) => (
                      <th
                        key={index}
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {table.rows.map((row, rowIndex) => (
                    <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      {/* Line number column */}
                      <td className="px-2 py-4 whitespace-nowrap text-xs text-gray-500 bg-gray-100 text-right font-mono w-12">
                        {rowIndex + 1}
                      </td>
                      {table.headers.map((header, colIndex) => (
                        <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {row[header] || ''}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderJsonView = () => {
    // If we have transactions data, display it
    if (isPolarisBank && transactions.length > 0) {
      return (
        <div className="space-y-4">
          <div className="bg-white p-4 rounded border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Transaction Data (JSON)</h3>

            <div className="overflow-auto bg-gray-100 p-4 rounded" style={{ maxHeight: '60vh' }}>
              <pre className="text-sm font-mono whitespace-pre-wrap">
                {JSON.stringify(transactions, null, 2)}
              </pre>
            </div>
          </div>

          {/* Bank Info */}
          <div className="bg-white p-4 rounded border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Bank Information</h3>

            <div className="overflow-auto bg-gray-100 p-4 rounded" style={{ maxHeight: '30vh' }}>
              <pre className="text-sm font-mono whitespace-pre-wrap">
                {JSON.stringify(content.bankInfo, null, 2)}
              </pre>
            </div>
          </div>

          {/* Back to Statement View Button */}
          <div className="flex justify-end mt-4">
            <button
              onClick={() => setViewMode('statement')}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm flex items-center"
            >
              <span className="mr-1">Back to Statement View</span>
              <FiGrid className="h-4 w-4" />
            </button>
          </div>
        </div>
      );
    }

    // If we don't have transactions data, show the raw tables
    return (
      <div className="space-y-4">
        <div className="bg-white p-4 rounded border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Table Data (JSON)</h3>

          <div className="overflow-auto bg-gray-100 p-4 rounded" style={{ maxHeight: '60vh' }}>
            <pre className="text-sm font-mono whitespace-pre-wrap">
              {JSON.stringify(tables, null, 2)}
            </pre>
          </div>
        </div>

        {/* Back to Statement View Button */}
        <div className="flex justify-end mt-4">
          <button
            onClick={() => setViewMode('statement')}
            className="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm flex items-center"
          >
            <span className="mr-1">Back to Statement View</span>
            <FiGrid className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="border rounded-md overflow-hidden">
      <div className="bg-gray-100 p-4 flex flex-col">
        {/* View mode toggle */}
        <div className="flex justify-end mb-2">
          <div className="inline-flex rounded-md shadow-sm" role="group">
            {/* Statement View Button */}
            {parsedStatement && (parsedStatement.entries.length > 0 || parsedStatement.metadata.bankName) && (
              <button
                type="button"
                onClick={() => setViewMode('statement')}
                className={`px-4 py-2 text-sm font-medium ${
                  viewMode === 'statement'
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                } ${
                  tables && tables.length > 0 ? 'rounded-l-lg' : 'rounded-l-lg'
                }`}
              >
                <FiList className="inline mr-1" /> Statement
              </button>
            )}

            {/* Text View Button */}
            <button
              type="button"
              onClick={() => setViewMode('text')}
              className={`px-4 py-2 text-sm font-medium ${
                viewMode === 'text'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } ${
                parsedStatement && (parsedStatement.entries.length > 0 || parsedStatement.metadata.bankName)
                  ? tables && tables.length > 0 ? '' : 'rounded-r-lg'
                  : tables && tables.length > 0 ? 'rounded-l-lg' : 'rounded-lg'
              }`}
            >
              <FiAlignLeft className="inline mr-1" /> Text
            </button>

            {/* Tables View Button */}
            {tables && tables.length > 0 && (
              <button
                type="button"
                onClick={() => setViewMode('tables')}
                className={`px-4 py-2 text-sm font-medium ${
                  viewMode === 'tables'
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                } ${isPolarisBank && transactions.length > 0 ? '' : 'rounded-r-lg'}`}
              >
                <FiTable className="inline mr-1" /> Tables ({tables.length})
              </button>
            )}

            {/* JSON View Button - Only for Polaris Bank statements with transactions */}
            {isPolarisBank && transactions.length > 0 && (
              <button
                type="button"
                onClick={() => setViewMode('json')}
                className={`px-4 py-2 text-sm font-medium rounded-r-lg ${
                  viewMode === 'json'
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <FiGrid className="inline mr-1" /> JSON
              </button>
            )}
          </div>
        </div>

        {/* PDF content preview with custom scrollbars */}
        <div className="bg-white p-6 rounded shadow-sm min-h-[400px] custom-scrollbar" style={{
          overflowY: 'scroll',
          overflowX: 'scroll',
          border: '1px solid #ddd',
          borderRadius: '4px'
        }}>
          {text ? (
            viewMode === 'statement'
              ? renderStatementView()
              : viewMode === 'text'
                ? renderTextView()
                : viewMode === 'tables'
                  ? renderTableView()
                  : renderJsonView()
          ) : (
            <div className="text-center text-gray-500">
              <p>PDF preview not available.</p>
            </div>
          )}
        </div>

        {/* Pagination controls */}
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-500">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handlePrevPage}
              disabled={currentPage <= 1}
              className={`p-1 rounded ${
                currentPage <= 1
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <FiChevronLeft className="h-5 w-5" />
            </button>
            <button
              onClick={handleNextPage}
              disabled={currentPage >= totalPages}
              className={`p-1 rounded ${
                currentPage >= totalPages
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <FiChevronRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFPreview;
