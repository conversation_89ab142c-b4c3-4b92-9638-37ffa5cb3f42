"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.unmatchTransaction = exports.matchTransactions = exports.getTransactionsByStatement = exports.deleteTransaction = exports.updateTransaction = exports.createTransaction = exports.getTransactionById = exports.getAllTransactions = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const transaction_model_1 = __importStar(require("../models/transaction.model"));
/**
 * Get all transactions
 * @route GET /api/transactions
 */
const getAllTransactions = async (req, res) => {
    try {
        // Get user ID from authenticated user
        const userId = req.user.sub;
        // Find all transactions for this user
        const transactions = await transaction_model_1.default.find({ userId })
            .sort({ date: -1 })
            .populate('statementId', 'name bankName')
            .populate('matchedWith', 'date description amount reference');
        res.status(200).json({
            status: true,
            count: transactions.length,
            data: transactions,
        });
    }
    catch (error) {
        console.error('Error getting transactions:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getAllTransactions = getAllTransactions;
/**
 * Get transaction by ID
 * @route GET /api/transactions/:id
 */
const getTransactionById = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid transaction ID' });
            return;
        }
        // Find transaction by ID and user ID
        const transaction = await transaction_model_1.default.findOne({
            _id: id,
            userId,
        })
            .populate('statementId', 'name bankName')
            .populate('matchedWith', 'date description amount reference');
        if (!transaction) {
            res.status(404).json({ message: 'Transaction not found' });
            return;
        }
        res.status(200).json({
            status: true,
            data: transaction,
        });
    }
    catch (error) {
        console.error('Error getting transaction:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getTransactionById = getTransactionById;
/**
 * Create a new transaction
 * @route POST /api/transactions
 */
const createTransaction = async (req, res) => {
    try {
        const { date, description, amount, reference, source, statementId } = req.body;
        const userId = req.user.sub;
        // Create new transaction
        const transaction = new transaction_model_1.default({
            date,
            description,
            amount,
            reference,
            source,
            statementId,
            userId,
        });
        // Save transaction to database
        await transaction.save();
        res.status(201).json({
            status: true,
            message: 'Transaction created successfully',
            data: transaction,
        });
    }
    catch (error) {
        console.error('Error creating transaction:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.createTransaction = createTransaction;
/**
 * Update transaction
 * @route PUT /api/transactions/:id
 */
const updateTransaction = async (req, res) => {
    try {
        const { id } = req.params;
        const { date, description, amount, reference, source, status } = req.body;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid transaction ID' });
            return;
        }
        // Find transaction by ID and user ID
        const transaction = await transaction_model_1.default.findOne({
            _id: id,
            userId,
        });
        if (!transaction) {
            res.status(404).json({ message: 'Transaction not found' });
            return;
        }
        // Update transaction fields
        if (date)
            transaction.date = new Date(date);
        if (description)
            transaction.description = description;
        if (amount)
            transaction.amount = amount;
        if (reference)
            transaction.reference = reference;
        if (source)
            transaction.source = source;
        if (status)
            transaction.status = status;
        // Save updated transaction
        await transaction.save();
        res.status(200).json({
            status: true,
            message: 'Transaction updated successfully',
            data: transaction,
        });
    }
    catch (error) {
        console.error('Error updating transaction:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.updateTransaction = updateTransaction;
/**
 * Delete transaction
 * @route DELETE /api/transactions/:id
 */
const deleteTransaction = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid transaction ID' });
            return;
        }
        // Find and delete transaction
        const transaction = await transaction_model_1.default.findOneAndDelete({
            _id: id,
            userId,
        });
        if (!transaction) {
            res.status(404).json({ message: 'Transaction not found' });
            return;
        }
        // If this transaction was matched with another, update the other transaction
        if (transaction.matchedWith) {
            await transaction_model_1.default.findByIdAndUpdate(transaction.matchedWith, {
                $unset: { matchedWith: 1 },
                status: transaction_model_1.TransactionStatus.UNMATCHED,
            });
        }
        res.status(200).json({
            status: true,
            message: 'Transaction deleted successfully',
        });
    }
    catch (error) {
        console.error('Error deleting transaction:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.deleteTransaction = deleteTransaction;
/**
 * Get transactions by statement ID
 * @route GET /api/transactions/statement/:id
 */
const getTransactionsByStatement = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid statement ID' });
            return;
        }
        // Find all transactions for this statement and user
        const transactions = await transaction_model_1.default.find({
            statementId: id,
            userId,
        }).sort({ date: -1 });
        res.status(200).json({
            status: true,
            count: transactions.length,
            data: transactions,
        });
    }
    catch (error) {
        console.error('Error getting transactions by statement:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getTransactionsByStatement = getTransactionsByStatement;
/**
 * Match transactions
 * @route POST /api/transactions/match
 */
const matchTransactions = async (req, res) => {
    try {
        const { transaction1Id, transaction2Id } = req.body;
        const userId = req.user.sub;
        // Validate ObjectIds
        if (!mongoose_1.default.Types.ObjectId.isValid(transaction1Id) || !mongoose_1.default.Types.ObjectId.isValid(transaction2Id)) {
            res.status(400).json({ message: 'Invalid transaction ID' });
            return;
        }
        // Find both transactions
        const transaction1 = await transaction_model_1.default.findOne({
            _id: transaction1Id,
            userId,
        });
        const transaction2 = await transaction_model_1.default.findOne({
            _id: transaction2Id,
            userId,
        });
        if (!transaction1 || !transaction2) {
            res.status(404).json({ message: 'One or both transactions not found' });
            return;
        }
        // Update both transactions to be matched with each other
        transaction1.matchedWith = transaction2._id;
        transaction1.status = transaction_model_1.TransactionStatus.MATCHED;
        await transaction1.save();
        transaction2.matchedWith = transaction1._id;
        transaction2.status = transaction_model_1.TransactionStatus.MATCHED;
        await transaction2.save();
        res.status(200).json({
            status: true,
            message: 'Transactions matched successfully',
            data: {
                transaction1,
                transaction2,
            },
        });
    }
    catch (error) {
        console.error('Error matching transactions:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.matchTransactions = matchTransactions;
/**
 * Unmatch transaction
 * @route POST /api/transactions/unmatch/:id
 */
const unmatchTransaction = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.sub;
        // Validate ObjectId
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({ message: 'Invalid transaction ID' });
            return;
        }
        // Find transaction
        const transaction = await transaction_model_1.default.findOne({
            _id: id,
            userId,
        });
        if (!transaction) {
            res.status(404).json({ message: 'Transaction not found' });
            return;
        }
        // If this transaction is matched with another, update the other transaction
        if (transaction.matchedWith) {
            await transaction_model_1.default.findByIdAndUpdate(transaction.matchedWith, {
                $unset: { matchedWith: 1 },
                status: transaction_model_1.TransactionStatus.UNMATCHED,
            });
        }
        // Update this transaction
        transaction.matchedWith = undefined;
        transaction.status = transaction_model_1.TransactionStatus.UNMATCHED;
        await transaction.save();
        res.status(200).json({
            status: true,
            message: 'Transaction unmatched successfully',
            data: transaction,
        });
    }
    catch (error) {
        console.error('Error unmatching transaction:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.unmatchTransaction = unmatchTransaction;
