import mongoose, { Document, Schema } from 'mongoose';

// StatementData interface
export interface IStatementData extends Document {
  statementId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  status: 'processing' | 'completed' | 'failed';
  content: any; // The actual statement content in BSON format
  metadata: {
    fileType: string;
    fileName: string;
    fileSize: number;
    uploadDate: Date;
    processingTime?: number;
    pageCount?: number;
    rowCount?: number;
    columnCount?: number;
  };
  summary?: {
    startDate?: Date;
    endDate?: Date;
    startBalance?: number;
    endBalance?: number;
    totalCredits?: number;
    totalDebits?: number;
    transactionCount?: number;
  };
  insights?: any;
  createdAt: Date;
  updatedAt: Date;
}

// StatementData schema
const statementDataSchema = new Schema<IStatementData>(
  {
    statementId: {
      type: Schema.Types.ObjectId,
      ref: 'Statement',
      required: [true, 'Statement ID is required'],
      index: true, // Add index for faster queries
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true, // Add index for faster queries
    },
    status: {
      type: String,
      enum: ['processing', 'completed', 'failed'],
      default: 'processing',
      index: true, // Add index for faster queries by status
    },
    content: {
      type: Schema.Types.Mixed,
      required: [true, 'Content is required'],
    },
    metadata: {
      fileType: {
        type: String,
        required: [true, 'File type is required'],
      },
      fileName: {
        type: String,
        required: [true, 'File name is required'],
      },
      fileSize: {
        type: Number,
        required: [true, 'File size is required'],
      },
      uploadDate: {
        type: Date,
        default: Date.now,
      },
      processingTime: Number,
      pageCount: Number,
      rowCount: Number,
      columnCount: Number,
    },
    summary: {
      startDate: Date,
      endDate: Date,
      startBalance: Number,
      endBalance: Number,
      totalCredits: Number,
      totalDebits: Number,
      transactionCount: Number,
    },
    insights: Schema.Types.Mixed,
  },
  {
    timestamps: true,
  }
);

// Create compound index for faster lookups by statementId and status
statementDataSchema.index({ statementId: 1, status: 1 });

// Create and export StatementData model
export default mongoose.model<IStatementData>('StatementData', statementDataSchema);
