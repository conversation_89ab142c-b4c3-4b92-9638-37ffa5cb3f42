type Merge = {
    row: number;
    col: number;
    width: number;
    height: number;
};
type Merges = Record<string, Merge>;
type MergeAlias = Record<string, string>;
type PageTables = {
    page: number;
    tables: string[][];
    merges: Merges;
    merge_alias: MergeAlias;
    width: number;
    height: number;
};
type PdfTableParseResult = {
    pageTables: PageTables[];
    numPages: number;
    currentPages: number;
};
type ExtractPdfTableOptions = {
    maxEdgesPerPage?: number;
    progressFunc?: (page: PdfTableParseResult) => void;
};
declare const extractPdfTable: (buffer: ArrayBuffer, options?: ExtractPdfTableOptions) => Promise<PdfTableParseResult>;

export { type ExtractPdfTableOptions, type Merge, type MergeAlias, type Merges, type PageTables, type PdfTableParseResult, extractPdfTable };
