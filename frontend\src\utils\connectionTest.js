/**
 * Connection Test Utility
 * 
 * This file provides functions to test the connection between frontend and backend
 */

import { API_BASE_URL } from '../apiRoutes';

// Test the connection to the backend
export const testBackendConnection = async () => {
  try {
    // Try to connect to the backend health check endpoint
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Backend connection successful:', data);
      return {
        success: true,
        message: 'Connected to backend successfully',
        data
      };
    } else {
      console.error('Backend connection failed with status:', response.status);
      return {
        success: false,
        message: `Failed to connect to backend: ${response.status} ${response.statusText}`,
      };
    }
  } catch (error) {
    console.error('Backend connection error:', error);
    return {
      success: false,
      message: `Error connecting to backend: ${error.message}`,
    };
  }
};

// Test authentication with the backend
export const testAuthConnection = async (token) => {
  try {
    // Try to connect to a protected endpoint
    const response = await fetch(`${API_BASE_URL}/users/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Auth connection successful:', data);
      return {
        success: true,
        message: 'Authenticated with backend successfully',
        data
      };
    } else {
      console.error('Auth connection failed with status:', response.status);
      return {
        success: false,
        message: `Failed to authenticate with backend: ${response.status} ${response.statusText}`,
      };
    }
  } catch (error) {
    console.error('Auth connection error:', error);
    return {
      success: false,
      message: `Error authenticating with backend: ${error.message}`,
    };
  }
};

export default {
  testBackendConnection,
  testAuthConnection
};
